# CollegeCampus Admin System Setup

This document provides comprehensive instructions for setting up and using the CollegeCampus admin system.

## 🚀 Quick Start

### 1. Environment Setup

1. Copy the environment template:
```bash
cp .env.example .env.local
```

2. Update the environment variables in `.env.local`:
```env
# Database (Required)
DATABASE_URL="postgresql://username:password@localhost:5432/college_campus"

# JWT Secret (Required - Generate a strong random string)
JWT_SECRET="your-super-secret-jwt-key-here"

# App URLs
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

### 2. Database Setup

1. Generate database schema:
```bash
npm run db:generate
```

2. Push schema to database:
```bash
npm run db:push
```

3. Create the super admin user:
```bash
npm run db:seed
```

This will create a super admin with:
- **Email**: `<EMAIL>`
- **Password**: `Admin@123456`
- **Role**: `super_admin`

⚠️ **IMPORTANT**: Change the default password after first login!

### 3. Start the Application

```bash
npm run dev
```

### 4. Access Admin Panel

1. Navigate to: `http://localhost:3000/admin/login`
2. Login with the super admin credentials
3. You'll be redirected to: `http://localhost:3000/admin/dashboard`

## 🔐 Admin System Features

### Super Admin Capabilities
- **Full System Access**: Complete control over all features
- **User Management**: Create, manage, and assign roles to admin users
- **Role Assignment**: Assign specific roles with limited permissions
- **Activity Monitoring**: View all admin activities and system logs
- **System Settings**: Configure website settings and preferences

### Admin Roles & Permissions

#### 1. **Content Manager**
- Manage colleges (create, edit, delete)
- Manage courses (create, edit, delete)
- Manage articles/blog posts
- SEO optimization for content

#### 2. **Review Moderator**
- Review and moderate student reviews
- Approve/reject reviews
- Manage review reports
- View review analytics

#### 3. **Lead Manager**
- Manage contact inquiries and leads
- Track lead status and follow-ups
- Assign leads to team members
- View lead conversion analytics

#### 4. **Finance Officer**
- Manage student admissions
- Track scholarship applications
- Monitor financial transactions
- Generate financial reports
- Commission tracking

#### 5. **SEO Specialist**
- Manage SEO settings
- Optimize content for search engines
- View analytics and performance metrics
- Manage meta tags and keywords

## 📊 Dashboard Features

### Real-time Statistics
- Total colleges, courses, reviews
- Pending reviews requiring moderation
- New leads and inquiries
- Scholarship applications
- Student admissions
- Published articles

### Recent Activity Feed
- Real-time activity log of all admin actions
- User identification and timestamps
- Action details and affected resources

## 🛠 API Architecture

### Authentication System
- **JWT-based authentication** with secure token management
- **Role-based access control** (RBAC) for granular permissions
- **Activity logging** for all admin actions
- **Session management** with automatic token refresh

### API Endpoints Structure
```
/api/auth/admin/
├── login          # Admin login
├── logout         # Admin logout
├── me             # Get current user
└── change-password # Change password

/api/admin/
├── dashboard/stats    # Dashboard statistics
├── users/            # Admin user management
├── activity-logs/    # Activity monitoring
└── [resource]/       # Resource-specific endpoints

/api/colleges/        # College management
/api/courses/         # Course management
/api/reviews/         # Review moderation
/api/scholarships/    # Scholarship management
/api/leads/           # Lead management
/api/articles/        # Content management
```

## 🔧 Technical Implementation

### Frontend Stack
- **Next.js 15** with App Router
- **TypeScript** for type safety
- **TanStack Query** for API state management
- **Tailwind CSS** for styling
- **React Hook Form** for form handling

### Backend Stack
- **Hono.js** for API routes
- **Drizzle ORM** with PostgreSQL
- **Zod** for validation
- **bcryptjs** for password hashing
- **Jose** for JWT handling

### Database Schema
- **Comprehensive admin system** with roles and permissions
- **Activity logging** for audit trails
- **Complete college and course management**
- **Review system** with moderation workflow
- **Lead tracking** and conversion management
- **Financial tracking** for admissions and scholarships

## 🚦 Getting Started Workflow

### For Super Admin
1. Login with default credentials
2. Change default password immediately
3. Create additional admin users with specific roles
4. Configure system settings
5. Start managing content and users

### For Regular Admins
1. Receive login credentials from super admin
2. Login and change password
3. Access features based on assigned role
4. Perform daily tasks within permissions

## 🔒 Security Features

### Authentication Security
- **Strong password requirements** (minimum 8 characters)
- **JWT token expiration** (24 hours)
- **Secure password hashing** with bcrypt (12 rounds)
- **IP address logging** for all activities

### Authorization Security
- **Role-based permissions** prevent unauthorized access
- **Resource-level security** checks
- **Activity logging** for audit trails
- **Session invalidation** on logout

### Data Security
- **Input validation** with Zod schemas
- **SQL injection prevention** with Drizzle ORM
- **XSS protection** with proper sanitization
- **CORS configuration** for API security

## 📈 Monitoring & Analytics

### Activity Monitoring
- **Real-time activity logs** with detailed information
- **User action tracking** with timestamps
- **IP address and user agent logging**
- **Resource modification history**

### Performance Monitoring
- **API response time tracking**
- **Database query optimization**
- **Error logging and reporting**
- **System health monitoring**

## 🔄 Maintenance

### Regular Tasks
- **Monitor activity logs** for suspicious activities
- **Review user permissions** periodically
- **Update passwords** regularly
- **Backup database** regularly
- **Monitor system performance**

### Updates and Upgrades
- **Keep dependencies updated**
- **Monitor security advisories**
- **Test new features** in staging environment
- **Document changes** and updates

## 🆘 Troubleshooting

### Common Issues

#### Login Problems
- Verify database connection
- Check JWT_SECRET configuration
- Ensure user is active in database
- Clear browser cache and cookies

#### Permission Errors
- Verify user role assignments
- Check API endpoint permissions
- Review activity logs for details
- Contact super admin for role updates

#### Database Issues
- Check DATABASE_URL configuration
- Verify database connectivity
- Run database migrations
- Check for schema updates

## 📞 Support

For technical support or questions:
- Check the activity logs for error details
- Review the API documentation
- Contact the development team
- Submit issues through the proper channels

---

**Note**: This admin system is designed for production use with comprehensive security, monitoring, and management features. Always follow security best practices and keep the system updated.
