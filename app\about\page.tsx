import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'About Us | CollegeCampus - Your Education Partner',
  description: 'Learn about CollegeCampus mission to help students find the perfect college and course. Discover our story, values, and commitment to education.',
  keywords: 'about CollegeCampus, education platform, college search, student guidance',
};

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">About CollegeCampus</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Empowering students to make informed decisions about their educational journey
              through comprehensive college and course information.
            </p>
          </div>
        </div>
      </div>

      {/* Mission & Vision */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">🎯</span>
              </div>
              <h2 className="text-2xl font-bold text-gray-900">Our Mission</h2>
            </div>
            <p className="text-gray-700 leading-relaxed">
              To democratize access to quality education information and help every student
              find the perfect college and course that aligns with their aspirations, abilities,
              and financial circumstances. We believe that the right education can transform lives
              and create a better future for all.
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">🚀</span>
              </div>
              <h2 className="text-2xl font-bold text-gray-900">Our Vision</h2>
            </div>
            <p className="text-gray-700 leading-relaxed">
              To become India&apos;s most trusted and comprehensive education platform, where students,
              parents, and educators can access accurate, up-to-date information about colleges,
              courses, scholarships, and career opportunities to make informed educational decisions.
            </p>
          </div>
        </div>
      </div>

      {/* Our Story */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Story</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Born from the need to simplify the complex world of higher education choices
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">💡</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">The Problem</h3>
              <p className="text-gray-600">
                Students and parents struggled to find reliable, comprehensive information
                about colleges and courses, often making decisions based on incomplete data.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">🛠️</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">The Solution</h3>
              <p className="text-gray-600">
                We created a comprehensive platform that aggregates verified information
                about colleges, courses, fees, placements, and scholarships in one place.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">🌟</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">The Impact</h3>
              <p className="text-gray-600">
                Thousands of students have made informed decisions about their education,
                leading to better career outcomes and reduced educational debt.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Our Values */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Values</h2>
          <p className="text-xl text-gray-600">
            The principles that guide everything we do
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="text-3xl mb-4">🔍</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Transparency</h3>
            <p className="text-gray-600 text-sm">
              We provide honest, unbiased information to help students make informed decisions.
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="text-3xl mb-4">✅</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Accuracy</h3>
            <p className="text-gray-600 text-sm">
              All information is verified and regularly updated to ensure reliability.
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="text-3xl mb-4">🤝</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Accessibility</h3>
            <p className="text-gray-600 text-sm">
              Education information should be accessible to everyone, regardless of background.
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="text-3xl mb-4">💪</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Empowerment</h3>
            <p className="text-gray-600 text-sm">
              We empower students to take control of their educational journey.
            </p>
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="bg-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Impact in Numbers</h2>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">10,000+</div>
              <div className="text-gray-600">Colleges Listed</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-green-600 mb-2">500+</div>
              <div className="text-gray-600">Courses Covered</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-600 mb-2">1M+</div>
              <div className="text-gray-600">Students Helped</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-orange-600 mb-2">50,000+</div>
              <div className="text-gray-600">Scholarships Listed</div>
            </div>
          </div>
        </div>
      </div>

      {/* Team Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Team</h2>
          <p className="text-xl text-gray-600">
            Passionate educators and technologists working to transform education
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {[
            {
              name: 'Dr. Priya Sharma',
              role: 'Founder & CEO',
              description: 'Former education consultant with 15+ years of experience in higher education.',
              image: '/placeholder-team1.jpg',
            },
            {
              name: 'Rahul Gupta',
              role: 'CTO',
              description: 'Tech entrepreneur passionate about using technology to solve education challenges.',
              image: '/placeholder-team2.jpg',
            },
            {
              name: 'Anjali Patel',
              role: 'Head of Content',
              description: 'Education researcher ensuring accuracy and relevance of all platform content.',
              image: '/placeholder-team3.jpg',
            },
          ].map((member) => (
            <div key={member.name} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
              <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4"></div>
              <h3 className="text-xl font-semibold text-gray-900 mb-1">{member.name}</h3>
              <p className="text-blue-600 font-medium mb-3">{member.role}</p>
              <p className="text-gray-600 text-sm">{member.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Contact CTA */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4">Get in Touch</h2>
            <p className="text-xl text-blue-100 mb-8">
              Have questions or want to partner with us? We&apos;d love to hear from you.
            </p>
            <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
              <button className="w-full sm:w-auto bg-white hover:bg-gray-100 text-blue-600 py-3 px-8 rounded-lg font-medium transition-colors duration-200">
                Contact Us
              </button>
              <button className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400 text-white py-3 px-8 rounded-lg font-medium transition-colors duration-200">
                Partner With Us
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
