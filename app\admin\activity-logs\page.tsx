'use client';

import { useState } from 'react';
import { useActivityLogs } from '@/features/api/use-activity-logs';
import { useIsAdminAuthenticated } from '@/features/api/use-admin-auth';

export default function ActivityLogsPage() {
  const { user } = useIsAdminAuthenticated();
  const [page, setPage] = useState(1);
  const [action, setAction] = useState('all');
  const [resource, setResource] = useState('all');
  const [adminId, setAdminId] = useState('all');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  
  const { data, isLoading, error } = useActivityLogs({ 
    page, 
    action: action !== 'all' ? action : undefined,
    resource: resource !== 'all' ? resource : undefined,
    adminId: adminId !== 'all' ? adminId : undefined,
    dateFrom: dateFrom || undefined,
    dateTo: dateTo || undefined,
  });
  
  const logs = data?.data || [];
  const meta = data?.meta;

  const canViewAllLogs = user?.role === 'super_admin';

  const getActionColor = (action: string) => {
    if (action.includes('create')) return 'bg-green-100 text-green-800';
    if (action.includes('update')) return 'bg-blue-100 text-blue-800';
    if (action.includes('delete')) return 'bg-red-100 text-red-800';
    if (action.includes('login')) return 'bg-purple-100 text-purple-800';
    if (action.includes('approve')) return 'bg-emerald-100 text-emerald-800';
    if (action.includes('reject')) return 'bg-orange-100 text-orange-800';
    return 'bg-gray-100 text-gray-800';
  };

  const getResourceIcon = (resource: string) => {
    switch (resource) {
      case 'colleges': return '🏫';
      case 'courses': return '📚';
      case 'reviews': return '⭐';
      case 'leads': return '👥';
      case 'scholarships': return '💰';
      case 'articles': return '📝';
      case 'admin_users': return '👤';
      case 'settings': return '⚙️';
      default: return '📄';
    }
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Activity Logs</h1>
          <p className="mt-1 text-sm text-gray-600">
            Track all admin activities and system changes
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow mb-6 p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Action</label>
            <select
              value={action}
              onChange={(e) => setAction(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            >
              <option value="all">All Actions</option>
              <option value="create">Create</option>
              <option value="update">Update</option>
              <option value="delete">Delete</option>
              <option value="login">Login</option>
              <option value="approve">Approve</option>
              <option value="reject">Reject</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Resource</label>
            <select
              value={resource}
              onChange={(e) => setResource(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            >
              <option value="all">All Resources</option>
              <option value="colleges">Colleges</option>
              <option value="courses">Courses</option>
              <option value="reviews">Reviews</option>
              <option value="leads">Leads</option>
              <option value="scholarships">Scholarships</option>
              <option value="articles">Articles</option>
              <option value="admin_users">Admin Users</option>
              <option value="settings">Settings</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">From Date</label>
            <input
              type="date"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">To Date</label>
            <input
              type="date"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            />
          </div>

          <div className="flex items-end">
            <button
              onClick={() => {
                setAction('all');
                setResource('all');
                setAdminId('all');
                setDateFrom('');
                setDateTo('');
              }}
              className="w-full px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm font-medium transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Activity Logs */}
      <div className="bg-white rounded-lg shadow">
        {isLoading ? (
          <div className="p-6">
            <div className="animate-pulse space-y-4">
              {[...Array(10)].map((_, i) => (
                <div key={i} className="flex items-start space-x-4 p-4 border-b border-gray-200">
                  <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="w-20 h-4 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          </div>
        ) : error ? (
          <div className="p-6 text-center">
            <div className="text-red-600 mb-2">Error loading activity logs</div>
            <p className="text-gray-600">{error.message}</p>
          </div>
        ) : logs.length === 0 ? (
          <div className="p-6 text-center">
            <div className="text-gray-400 mb-2">No activity logs found</div>
            <p className="text-gray-600">No activities match your current filters</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {logs.map((log: any) => (
              <div key={log.id} className="p-4 hover:bg-gray-50">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm">
                      {getResourceIcon(log.resource)}
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getActionColor(log.action)}`}>
                        {log.action.replace('_', ' ')}
                      </span>
                      <span className="text-sm text-gray-500">
                        {log.resource.replace('_', ' ')}
                      </span>
                      {log.resourceId && (
                        <span className="text-xs text-gray-400">
                          ID: {log.resourceId.slice(0, 8)}...
                        </span>
                      )}
                    </div>
                    
                    <div className="text-sm text-gray-900 mb-1">
                      <span className="font-medium">{log.adminName}</span>
                      <span className="text-gray-600 ml-1">
                        {log.action.includes('create') && 'created'}
                        {log.action.includes('update') && 'updated'}
                        {log.action.includes('delete') && 'deleted'}
                        {log.action.includes('login') && 'logged in'}
                        {log.action.includes('approve') && 'approved'}
                        {log.action.includes('reject') && 'rejected'}
                        {!log.action.includes('create') && !log.action.includes('update') && 
                         !log.action.includes('delete') && !log.action.includes('login') && 
                         !log.action.includes('approve') && !log.action.includes('reject') && 'performed action on'}
                      </span>
                      <span className="ml-1">
                        {log.resource.replace('_', ' ')}
                      </span>
                    </div>

                    {log.details && (
                      <div className="text-xs text-gray-500 mt-1">
                        {typeof log.details === 'object' ? (
                          <div className="space-y-1">
                            {log.details.name && (
                              <div><span className="font-medium">Name:</span> {log.details.name}</div>
                            )}
                            {log.details.changes && Array.isArray(log.details.changes) && (
                              <div><span className="font-medium">Changes:</span> {log.details.changes.join(', ')}</div>
                            )}
                            {log.details.status && (
                              <div><span className="font-medium">Status:</span> {log.details.status}</div>
                            )}
                          </div>
                        ) : (
                          <span>{log.details}</span>
                        )}
                      </div>
                    )}

                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                      <span>{new Date(log.createdAt).toLocaleString()}</span>
                      <span>IP: {log.ipAddress || 'N/A'}</span>
                      <span>Agent: {log.userAgent ? log.userAgent.slice(0, 50) + '...' : 'N/A'}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {meta && meta.totalPages > 1 && (
          <div className="px-6 py-3 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {((meta.page - 1) * meta.limit) + 1} to {Math.min(meta.page * meta.limit, meta.total)} of {meta.total} results
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setPage(page - 1)}
                  disabled={!meta.hasPrev}
                  className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                <span className="px-3 py-1 text-sm">
                  Page {meta.page} of {meta.totalPages}
                </span>
                <button
                  onClick={() => setPage(page + 1)}
                  disabled={!meta.hasNext}
                  className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
