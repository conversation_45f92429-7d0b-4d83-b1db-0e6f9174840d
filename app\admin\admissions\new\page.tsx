'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation, useQuery } from '@tanstack/react-query';

// Force dynamic rendering
export const dynamic = 'force-dynamic';
import { ArrowLeft, Save, User, GraduationCap, DollarSign, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import Link from 'next/link';
import { useAuthToken } from '@/hooks/use-local-storage';

interface AdmissionFormData {
  // Student Information
  studentName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other';
  address: string;
  city: string;
  state: string;
  pincode: string;

  // Academic Information
  courseId: string;
  courseName: string;
  academicYear: string;
  admissionDate: string;
  rollNumber: string;

  // Financial Information
  totalFees: number;
  paidAmount: number;
  pendingAmount: number;
  scholarshipAmount?: number;

  // Status Information
  overallStatus: 'active_student' | 'course_completed' | 'withdrew_admission' | 'dropout';

  // Additional Information
  guardianName?: string;
  guardianPhone?: string;
  emergencyContact?: string;
  notes?: string;
}

export default function NewAdmissionPage() {
  const [mounted, setMounted] = useState(false);
  const router = useRouter();
  const { getAuthHeaders } = useAuthToken();

  const [formData, setFormData] = useState<AdmissionFormData>({
    studentName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: 'male',
    address: '',
    city: '',
    state: '',
    pincode: '',
    courseId: '',
    courseName: '',
    academicYear: new Date().getFullYear().toString(),
    admissionDate: new Date().toISOString().split('T')[0],
    rollNumber: '',
    totalFees: 0,
    paidAmount: 0,
    pendingAmount: 0,
    scholarshipAmount: 0,
    overallStatus: 'active_student',
    guardianName: '',
    guardianPhone: '',
    emergencyContact: '',
    notes: '',
  });

  // ALL HOOKS MUST BE CALLED BEFORE ANY CONDITIONAL RETURNS
  // Fetch courses for dropdown
  const { data: coursesData } = useQuery({
    queryKey: ['courses-list'],
    queryFn: async () => {
      const response = await fetch('/api/courses?limit=100', {
        headers: getAuthHeaders(),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch courses');
      }

      return response.json();
    },
    enabled: mounted,
  });

  const createAdmissionMutation = useMutation({
    mutationFn: async (data: AdmissionFormData) => {
      const response = await fetch('/api/admissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create admission record');
      }

      return response.json();
    },
    onSuccess: (data) => {
      toast.success('Admission record created successfully');
      router.push('/admin/admissions');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to create admission record');
    },
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const courses = coursesData?.data?.courses || [];

  // Auto-calculate pending amount
  const updatePendingAmount = (totalFees: number, paidAmount: number, scholarshipAmount: number = 0) => {
    const pending = totalFees - paidAmount - scholarshipAmount;
    setFormData(prev => ({
      ...prev,
      totalFees,
      paidAmount,
      scholarshipAmount,
      pendingAmount: Math.max(0, pending),
    }));
  };

  const handleCourseChange = (courseId: string) => {
    const selectedCourse = courses.find((course: any) => course.id === courseId);
    setFormData(prev => ({
      ...prev,
      courseId,
      courseName: selectedCourse?.name || '',
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.studentName.trim()) {
      toast.error('Student name is required');
      return;
    }

    if (!formData.email.trim()) {
      toast.error('Email is required');
      return;
    }

    if (!formData.courseId) {
      toast.error('Course selection is required');
      return;
    }

    if (!formData.rollNumber.trim()) {
      toast.error('Roll number is required');
      return;
    }

    createAdmissionMutation.mutate(formData);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/admissions">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Admissions
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Add New Admission</h1>
            <p className="text-muted-foreground">
              Create a new student admission record
            </p>
          </div>
        </div>
        <Button
          type="submit"
          form="admission-form"
          disabled={createAdmissionMutation.isPending}
        >
          <Save className="mr-2 h-4 w-4" />
          {createAdmissionMutation.isPending ? 'Creating...' : 'Create Admission'}
        </Button>
      </div>

      <form id="admission-form" onSubmit={handleSubmit} className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Student Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5" />
              Student Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="studentName">Student Name *</Label>
                <Input
                  id="studentName"
                  value={formData.studentName}
                  onChange={(e) => setFormData(prev => ({ ...prev, studentName: e.target.value }))}
                  placeholder="Enter student name"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="+91 9876543210"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dateOfBirth">Date of Birth</Label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => setFormData(prev => ({ ...prev, dateOfBirth: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="gender">Gender</Label>
                <Select value={formData.gender} onValueChange={(value: 'male' | 'female' | 'other') => setFormData(prev => ({ ...prev, gender: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="rollNumber">Roll Number *</Label>
                <Input
                  id="rollNumber"
                  value={formData.rollNumber}
                  onChange={(e) => setFormData(prev => ({ ...prev, rollNumber: e.target.value }))}
                  placeholder="Enter roll number"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Textarea
                id="address"
                value={formData.address}
                onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                placeholder="Enter complete address"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                  placeholder="City"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  value={formData.state}
                  onChange={(e) => setFormData(prev => ({ ...prev, state: e.target.value }))}
                  placeholder="State"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="pincode">Pincode</Label>
                <Input
                  id="pincode"
                  value={formData.pincode}
                  onChange={(e) => setFormData(prev => ({ ...prev, pincode: e.target.value }))}
                  placeholder="Pincode"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Academic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <GraduationCap className="mr-2 h-5 w-5" />
              Academic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="courseId">Course *</Label>
              <Select value={formData.courseId} onValueChange={handleCourseChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select course" />
                </SelectTrigger>
                <SelectContent>
                  {courses.map((course: any) => (
                    <SelectItem key={course.id} value={course.id}>
                      {course.name} ({course.shortName})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="academicYear">Academic Year</Label>
                <Input
                  id="academicYear"
                  value={formData.academicYear}
                  onChange={(e) => setFormData(prev => ({ ...prev, academicYear: e.target.value }))}
                  placeholder="2024"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="admissionDate">Admission Date</Label>
                <Input
                  id="admissionDate"
                  type="date"
                  value={formData.admissionDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, admissionDate: e.target.value }))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="overallStatus">Status</Label>
              <Select value={formData.overallStatus} onValueChange={(value: 'active_student' | 'course_completed' | 'withdrew_admission' | 'dropout') => setFormData(prev => ({ ...prev, overallStatus: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active_student">Active Student</SelectItem>
                  <SelectItem value="course_completed">Course Completed</SelectItem>
                  <SelectItem value="withdrew_admission">Withdrew Admission</SelectItem>
                  <SelectItem value="dropout">Dropout</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Financial Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="mr-2 h-5 w-5" />
              Financial Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="totalFees">Total Fees (₹)</Label>
                <Input
                  id="totalFees"
                  type="number"
                  value={formData.totalFees}
                  onChange={(e) => updatePendingAmount(Number(e.target.value), formData.paidAmount, formData.scholarshipAmount)}
                  placeholder="0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="paidAmount">Paid Amount (₹)</Label>
                <Input
                  id="paidAmount"
                  type="number"
                  value={formData.paidAmount}
                  onChange={(e) => updatePendingAmount(formData.totalFees, Number(e.target.value), formData.scholarshipAmount)}
                  placeholder="0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="scholarshipAmount">Scholarship Amount (₹)</Label>
                <Input
                  id="scholarshipAmount"
                  type="number"
                  value={formData.scholarshipAmount}
                  onChange={(e) => updatePendingAmount(formData.totalFees, formData.paidAmount, Number(e.target.value))}
                  placeholder="0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="pendingAmount">Pending Amount (₹)</Label>
                <Input
                  id="pendingAmount"
                  type="number"
                  value={formData.pendingAmount}
                  readOnly
                  className="bg-gray-50"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Guardian & Emergency Information */}
        <Card>
          <CardHeader>
            <CardTitle>Guardian & Emergency Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="guardianName">Guardian Name</Label>
                <Input
                  id="guardianName"
                  value={formData.guardianName}
                  onChange={(e) => setFormData(prev => ({ ...prev, guardianName: e.target.value }))}
                  placeholder="Guardian name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="guardianPhone">Guardian Phone</Label>
                <Input
                  id="guardianPhone"
                  value={formData.guardianPhone}
                  onChange={(e) => setFormData(prev => ({ ...prev, guardianPhone: e.target.value }))}
                  placeholder="+91 9876543210"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="emergencyContact">Emergency Contact</Label>
              <Input
                id="emergencyContact"
                value={formData.emergencyContact}
                onChange={(e) => setFormData(prev => ({ ...prev, emergencyContact: e.target.value }))}
                placeholder="Emergency contact details"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Additional notes or comments"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}
