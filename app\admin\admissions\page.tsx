'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

// Force dynamic rendering
export const dynamic = 'force-dynamic';
import { Plus, Search, Edit, Trash2, User, GraduationCap, DollarSign, Calendar, Building } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import Link from 'next/link';
import { useAuthToken } from '@/hooks/use-local-storage';
import { ClientOnly } from '@/components/client-only';
import { TableSkeleton } from '@/components/ui/loading-skeleton';

interface StudentAdmission {
  id: string;
  studentId: string;
  name: string;
  email: string;
  phone: string;
  collegeId: string;
  collegeName: string;
  courseId: string;
  courseName: string;
  yearOfAdmission: string;
  admissionDate: string;
  collegeFirstYearFee: number;
  scholarshipAmountAwarded: number;
  scholarshipDisbursementStatus: string;
  commissionAmount: number;
  commissionStatus: string;
  overallStatus: string;
  createdAt: string;
  updatedAt: string;
}

interface AdmissionsResponse {
  success: boolean;
  data: {
    admissions: StudentAdmission[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export default function AdmissionsPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Student Admissions</h1>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  return <AdmissionsPageContent />;
}

function AdmissionsPageContent() {
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState<string>('all');
  const [year, setYear] = useState<string>('all');
  const limit = 10;

  const queryClient = useQueryClient();
  const { getAuthHeaders } = useAuthToken();

  // All hooks must be called before any conditional returns
  const { data: admissionsData, isLoading } = useQuery<AdmissionsResponse>({
    queryKey: ['admin-admissions', page, search, status, year],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
        ...(status && status !== 'all' && { status }),
        ...(year && year !== 'all' && { year }),
      });

      const response = await fetch(`/api/admissions?${params}`, {
        headers: getAuthHeaders(),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch admissions');
      }

      return response.json();
    },
  });

  const deleteAdmissionMutation = useMutation({
    mutationFn: async (admissionId: string) => {
      const response = await fetch(`/api/admissions/${admissionId}`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete admission');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-admissions'] });
      toast.success('Admission deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to delete admission');
    },
  });

  const updateStatusMutation = useMutation({
    mutationFn: async ({ admissionId, status }: { admissionId: string; status: string }) => {
      const response = await fetch(`/api/admissions/${admissionId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        },
        credentials: 'include',
        body: JSON.stringify({ overallStatus: status }),
      });

      if (!response.ok) {
        throw new Error('Failed to update admission status');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-admissions'] });
      toast.success('Admission status updated successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update admission status');
    },
  });



  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active_student':
        return <Badge className="bg-green-100 text-green-800">Active Student</Badge>;
      case 'course_completed':
        return <Badge className="bg-blue-100 text-blue-800">Course Completed</Badge>;
      case 'withdrew_admission':
        return <Badge className="bg-red-100 text-red-800">Withdrew</Badge>;
      case 'dropout':
        return <Badge className="bg-gray-100 text-gray-800">Dropout</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getCommissionStatusBadge = (status: string) => {
    switch (status) {
      case 'fully_received':
        return <Badge className="bg-green-100 text-green-800">Fully Received</Badge>;
      case 'partially_received':
        return <Badge className="bg-yellow-100 text-yellow-800">Partially Received</Badge>;
      case 'pending_invoice':
        return <Badge className="bg-blue-100 text-blue-800">Pending Invoice</Badge>;
      case 'invoice_sent':
        return <Badge className="bg-purple-100 text-purple-800">Invoice Sent</Badge>;
      case 'disputed':
        return <Badge className="bg-red-100 text-red-800">Disputed</Badge>;
      case 'not_applicable':
        return <Badge className="bg-gray-100 text-gray-800">Not Applicable</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const admissions = admissionsData?.data?.admissions || [];
  const totalPages = admissionsData?.data?.totalPages || 1;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Student Admissions</h1>
          <p className="text-muted-foreground">
            Track and manage student admissions and financial records
          </p>
        </div>
        <Link href="/admin/admissions/new">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            New Admission
          </Button>
        </Link>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search admissions..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active_student">Active Student</SelectItem>
            <SelectItem value="course_completed">Course Completed</SelectItem>
            <SelectItem value="withdrew_admission">Withdrew</SelectItem>
            <SelectItem value="dropout">Dropout</SelectItem>
          </SelectContent>
        </Select>
        <Select value={year} onValueChange={setYear}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by year" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Years</SelectItem>
            <SelectItem value="2024">2024</SelectItem>
            <SelectItem value="2023">2023</SelectItem>
            <SelectItem value="2022">2022</SelectItem>
            <SelectItem value="2021">2021</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Admissions Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Student</TableHead>
              <TableHead>College & Course</TableHead>
              <TableHead>Year</TableHead>
              <TableHead>Fees & Commission</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Commission Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  Loading admissions...
                </TableCell>
              </TableRow>
            ) : admissions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  No admissions found
                </TableCell>
              </TableRow>
            ) : (
              admissions.map((admission) => (
                <TableRow key={admission.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium flex items-center">
                        <User className="mr-2 h-4 w-4 text-muted-foreground" />
                        {admission.name}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        ID: {admission.studentId}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {admission.email}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium flex items-center">
                        <Building className="mr-2 h-4 w-4 text-muted-foreground" />
                        {admission.collegeName}
                      </div>
                      <div className="text-sm text-muted-foreground flex items-center">
                        <GraduationCap className="mr-1 h-3 w-3" />
                        {admission.courseName}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Calendar className="mr-1 h-4 w-4 text-muted-foreground" />
                      {admission.yearOfAdmission}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">
                        <span className="text-muted-foreground">Fee:</span> {formatCurrency(admission.collegeFirstYearFee)}
                      </div>
                      <div className="text-sm">
                        <span className="text-muted-foreground">Commission:</span> {formatCurrency(admission.commissionAmount)}
                      </div>
                      {admission.scholarshipAmountAwarded > 0 && (
                        <div className="text-sm text-green-600">
                          <span className="text-muted-foreground">Scholarship:</span> {formatCurrency(admission.scholarshipAmountAwarded)}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(admission.overallStatus)}</TableCell>
                  <TableCell>{getCommissionStatusBadge(admission.commissionStatus)}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Link href={`/admin/admissions/${admission.id}/edit`}>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Select
                        value={admission.overallStatus}
                        onValueChange={(newStatus) =>
                          updateStatusMutation.mutate({
                            admissionId: admission.id,
                            status: newStatus,
                          })
                        }
                      >
                        <SelectTrigger className="w-[120px] h-8">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="active_student">Active</SelectItem>
                          <SelectItem value="course_completed">Completed</SelectItem>
                          <SelectItem value="withdrew_admission">Withdrew</SelectItem>
                          <SelectItem value="dropout">Dropout</SelectItem>
                        </SelectContent>
                      </Select>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="sm" className="text-red-600">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Admission</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete {admission.name}'s admission record? This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => deleteAdmissionMutation.mutate(admission.id)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {admissions.length} of {admissionsData?.data?.total || 0} admissions
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
            >
              Previous
            </Button>
            <span className="text-sm">
              Page {page} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}


