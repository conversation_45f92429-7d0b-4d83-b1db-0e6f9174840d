'use client';

import { useParams, useRouter } from 'next/navigation';
import { useCollege } from '@/features/api/use-colleges';
import { useIsAdminAuthenticated } from '@/features/api/use-admin-auth';
import { CollegeForm } from '@/components/admin/CollegeForm';
import { useEffect } from 'react';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export default function EditCollegePage() {
  const params = useParams();
  const router = useRouter();
  const { user, isLoading: authLoading } = useIsAdminAuthenticated();
  const collegeId = params.id as string;
  
  const { data, isLoading, error } = useCollege(collegeId);
  const college = data?.data;

  useEffect(() => {
    if (!authLoading && user) {
      const canManageColleges = user.role === 'super_admin' || user.role === 'content_manager';
      if (!canManageColleges) {
        router.push('/admin/dashboard');
      }
    }
  }, [user, authLoading, router]);

  if (authLoading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const canManageColleges = user?.role === 'super_admin' || user?.role === 'content_manager';

  if (!canManageColleges) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Access Denied
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>You don't have permission to edit colleges. Only super admins and content managers can perform this action.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !college) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="text-red-800">
            <h3 className="font-medium">Error loading college</h3>
            <p className="mt-1 text-sm">{error?.message || 'College not found'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <CollegeForm college={college} isEditing={true} />
    </div>
  );
}
