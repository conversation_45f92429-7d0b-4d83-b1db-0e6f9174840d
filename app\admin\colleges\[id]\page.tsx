'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { useCollege, useToggleCollegeStatus, useDeleteCollege } from '@/features/api/use-colleges';
import { useIsAdminAuthenticated } from '@/features/api/use-admin-auth';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export default function CollegeDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useIsAdminAuthenticated();
  const collegeId = params.id as string;
  
  const { data, isLoading, error } = useCollege(collegeId);
  const toggleStatusMutation = useToggleCollegeStatus();
  const deleteMutation = useDeleteCollege();
  
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  
  const college = data?.data;
  const canManageColleges = user?.role === 'super_admin' || user?.role === 'content_manager';

  const handleToggleStatus = async (field: 'isPublished' | 'isVerified' | 'isFeatured', value: boolean) => {
    if (!college) return;
    
    try {
      await toggleStatusMutation.mutateAsync({ id: college.id, field, value });
    } catch (error) {
      console.error('Failed to toggle status:', error);
    }
  };

  const handleDelete = async () => {
    if (!college) return;
    
    try {
      await deleteMutation.mutateAsync(college.id);
      router.push('/admin/colleges');
    } catch (error) {
      console.error('Failed to delete college:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="bg-white rounded-lg shadow p-6 space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !college) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="text-red-800">
            <h3 className="font-medium">Error loading college</h3>
            <p className="mt-1 text-sm">{error?.message || 'College not found'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-start mb-6">
        <div className="flex items-center space-x-4">
          {college.logo && (
            <img
              src={college.logo}
              alt={college.name}
              className="w-16 h-16 rounded-lg object-cover"
            />
          )}
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{college.name}</h1>
            <p className="text-gray-600">{college.shortName}</p>
            <div className="flex items-center space-x-2 mt-2">
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                college.isPublished 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {college.isPublished ? 'Published' : 'Draft'}
              </span>
              {college.isVerified && (
                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                  Verified
                </span>
              )}
              {college.isFeatured && (
                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                  Featured
                </span>
              )}
            </div>
          </div>
        </div>
        
        {canManageColleges && (
          <div className="flex items-center space-x-3">
            <Link
              href={`/admin/colleges/${college.id}/edit`}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Edit College
            </Link>
            <button
              onClick={() => setShowDeleteModal(true)}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Delete
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Basic Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">Established Year</label>
                <p className="mt-1 text-sm text-gray-900">{college.establishedYear || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">College Type</label>
                <p className="mt-1 text-sm text-gray-900">{college.collegeType || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Affiliation</label>
                <p className="mt-1 text-sm text-gray-900">{college.affiliation || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">NIRF Ranking</label>
                <p className="mt-1 text-sm text-gray-900">{college.nirfRanking || 'Not Ranked'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Total Students</label>
                <p className="mt-1 text-sm text-gray-900">{college.totalStudents?.toLocaleString() || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Faculty Count</label>
                <p className="mt-1 text-sm text-gray-900">{college.facultyCount?.toLocaleString() || 'N/A'}</p>
              </div>
            </div>
          </div>

          {/* Description */}
          {college.description && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Description</h2>
              <p className="text-gray-700 leading-relaxed">{college.description}</p>
            </div>
          )}

          {/* About */}
          {college.about && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">About</h2>
              <div className="prose max-w-none">
                <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">{college.about}</p>
              </div>
            </div>
          )}

          {/* Vision & Mission */}
          {(college.vision || college.mission) && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Vision & Mission</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {college.vision && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Vision</h3>
                    <p className="text-gray-700 leading-relaxed">{college.vision}</p>
                  </div>
                )}
                {college.mission && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Mission</h3>
                    <p className="text-gray-700 leading-relaxed">{college.mission}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Location */}
          {college.location && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Location</h2>
              <div className="space-y-2">
                {college.location.address && (
                  <p className="text-gray-700">{college.location.address}</p>
                )}
                <p className="text-gray-700">
                  {[college.location.city, college.location.state, college.location.country]
                    .filter(Boolean)
                    .join(', ')}
                  {college.location.pincode && ` - ${college.location.pincode}`}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          {canManageColleges && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Published</span>
                  <button
                    onClick={() => handleToggleStatus('isPublished', !college.isPublished)}
                    disabled={toggleStatusMutation.isPending}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      college.isPublished ? 'bg-green-600' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        college.isPublished ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Verified</span>
                  <button
                    onClick={() => handleToggleStatus('isVerified', !college.isVerified)}
                    disabled={toggleStatusMutation.isPending}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      college.isVerified ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        college.isVerified ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Featured</span>
                  <button
                    onClick={() => handleToggleStatus('isFeatured', !college.isFeatured)}
                    disabled={toggleStatusMutation.isPending}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      college.isFeatured ? 'bg-purple-600' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        college.isFeatured ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Statistics */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Reviews</span>
                <span className="text-sm font-medium text-gray-900">{college.totalReviews}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Overall Rating</span>
                <span className="text-sm font-medium text-gray-900">
                  {college.overallRating ? `${college.overallRating}/5` : 'No ratings'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Created</span>
                <span className="text-sm font-medium text-gray-900">
                  {new Date(college.createdAt).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Last Updated</span>
                <span className="text-sm font-medium text-gray-900">
                  {new Date(college.updatedAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          {college.contactInfo && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
              <div className="space-y-3">
                {college.contactInfo.phone && (
                  <div>
                    <span className="text-sm text-gray-600">Phone</span>
                    <p className="text-sm font-medium text-gray-900">{college.contactInfo.phone}</p>
                  </div>
                )}
                {college.contactInfo.email && (
                  <div>
                    <span className="text-sm text-gray-600">Email</span>
                    <p className="text-sm font-medium text-gray-900">{college.contactInfo.email}</p>
                  </div>
                )}
                {college.contactInfo.website && (
                  <div>
                    <span className="text-sm text-gray-600">Website</span>
                    <a
                      href={college.contactInfo.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm font-medium text-blue-600 hover:text-blue-800"
                    >
                      {college.contactInfo.website}
                    </a>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Delete College</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete "{college.name}"? This action cannot be undone.
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowDeleteModal(false)}
                    className="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDelete}
                    disabled={deleteMutation.isPending}
                    className="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300 disabled:opacity-50"
                  >
                    {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
