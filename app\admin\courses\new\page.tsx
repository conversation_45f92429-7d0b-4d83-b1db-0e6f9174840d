'use client';

import { useState, useEffect } from 'react';
import { CourseForm } from '@/components/admin/CourseForm';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export default function NewCoursePage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="container mx-auto py-6">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Add New Course</h1>
            <p className="mt-1 text-sm text-gray-600">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <CourseForm />
    </div>
  );
}
