'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

// Force dynamic rendering
export const dynamic = 'force-dynamic';
import { Plus, Search, Edit, Trash2, GraduationCap, Clock, DollarSign, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import Link from 'next/link';
import { useAuthToken } from '@/hooks/use-local-storage';
import { TableSkeleton } from '@/components/ui/loading-skeleton';

interface Course {
  id: string;
  name: string;
  shortName: string;
  slug: string;
  level: string;
  duration: string;
  stream: string;
  description: string;
  averageFees: number;
  averageSalary: number;
  isPopular: boolean;
  isPublished: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CoursesResponse {
  success: boolean;
  data: {
    courses: Course[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export default function CoursesPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Courses</h1>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  return <CoursesPageContent />;
}

function CoursesPageContent() {
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const [level, setLevel] = useState<string>('all');
  const [stream, setStream] = useState<string>('all');
  const [showBulkActions, setShowBulkActions] = useState(false);
  const limit = 10;

  const queryClient = useQueryClient();
  const { getAuthHeaders } = useAuthToken();

  // All hooks must be called before any conditional returns
  const { data: coursesData, isLoading } = useQuery<CoursesResponse>({
    queryKey: ['admin-courses', page, search, level, stream],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
        ...(level && level !== 'all' && { level }),
        ...(stream && stream !== 'all' && { stream }),
      });

      const response = await fetch(`/api/courses?${params}`, {
        headers: {
          ...getAuthHeaders(),
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch courses');
      }

      return response.json();
    },
  });

  const deleteCourse = useMutation({
    mutationFn: async (courseId: string) => {
      const response = await fetch(`/api/courses/${courseId}`, {
        method: 'DELETE',
        headers: {
          ...getAuthHeaders(),
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete course');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-courses'] });
      toast.success('Course deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to delete course');
    },
  });

  const toggleStatus = useMutation({
    mutationFn: async ({ courseId, field, value }: { courseId: string; field: string; value: boolean }) => {
      const response = await fetch(`/api/courses/${courseId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        },
        credentials: 'include',
        body: JSON.stringify({ [field]: value }),
      });

      if (!response.ok) {
        throw new Error('Failed to update course');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-courses'] });
      toast.success('Course updated successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update course');
    },
  });



  const getLevelBadge = (level: string) => {
    switch (level?.toLowerCase()) {
      case 'undergraduate':
        return <Badge className="bg-blue-100 text-blue-800">Undergraduate</Badge>;
      case 'postgraduate':
        return <Badge className="bg-purple-100 text-purple-800">Postgraduate</Badge>;
      case 'doctoral':
        return <Badge className="bg-red-100 text-red-800">Doctoral</Badge>;
      default:
        return <Badge variant="outline">{level}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const courses = coursesData?.data?.courses || [];
  const totalPages = coursesData?.data?.totalPages || 1;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Courses</h1>
          <p className="text-muted-foreground">
            Manage academic courses and programs
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button onClick={() => setShowBulkActions(!showBulkActions)} variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Bulk Actions
          </Button>
          <Link href="/admin/courses/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Course
            </Button>
          </Link>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search courses..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={level} onValueChange={setLevel}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by level" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Levels</SelectItem>
            <SelectItem value="undergraduate">Undergraduate</SelectItem>
            <SelectItem value="postgraduate">Postgraduate</SelectItem>
            <SelectItem value="doctoral">Doctoral</SelectItem>
          </SelectContent>
        </Select>
        <Select value={stream} onValueChange={setStream}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by stream" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Streams</SelectItem>
            <SelectItem value="engineering">Engineering</SelectItem>
            <SelectItem value="medical">Medical</SelectItem>
            <SelectItem value="management">Management</SelectItem>
            <SelectItem value="arts">Arts</SelectItem>
            <SelectItem value="science">Science</SelectItem>
            <SelectItem value="commerce">Commerce</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Courses Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Course</TableHead>
              <TableHead>Level</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>Avg. Fees</TableHead>
              <TableHead>Avg. Salary</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  Loading courses...
                </TableCell>
              </TableRow>
            ) : courses.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  No courses found
                </TableCell>
              </TableRow>
            ) : (
              courses.map((course) => (
                <TableRow key={course.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium flex items-center">
                        <GraduationCap className="mr-2 h-4 w-4 text-muted-foreground" />
                        {course.name}
                        {course.isPopular && (
                          <Badge className="ml-2 bg-orange-100 text-orange-800">Popular</Badge>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {course.shortName} • {course.stream}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getLevelBadge(course.level)}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Clock className="mr-1 h-4 w-4 text-muted-foreground" />
                      {course.duration || 'N/A'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <DollarSign className="mr-1 h-4 w-4 text-muted-foreground" />
                      {formatCurrency(course.averageFees)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <DollarSign className="mr-1 h-4 w-4 text-muted-foreground" />
                      {formatCurrency(course.averageSalary)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-1">
                        <Switch
                          checked={course.isPublished}
                          onCheckedChange={(checked) =>
                            toggleStatus.mutate({
                              courseId: course.id,
                              field: 'isPublished',
                              value: checked,
                            })
                          }
                        />
                        <span className="text-xs text-muted-foreground">
                          {course.isPublished ? 'Published' : 'Draft'}
                        </span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Link href={`/admin/courses/${course.id}/edit`}>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          toggleStatus.mutate({
                            courseId: course.id,
                            field: 'isPopular',
                            value: !course.isPopular,
                          })
                        }
                        className={course.isPopular ? 'text-orange-600' : 'text-muted-foreground'}
                      >
                        ⭐
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="sm" className="text-red-600">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Course</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete "{course.name}"? This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => deleteCourse.mutate(course.id)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {courses.length} of {coursesData?.data?.total || 0} courses
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
            >
              Previous
            </Button>
            <span className="text-sm">
              Page {page} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}


