'use client';

import type { Metadata } from 'next';
import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useIsAdminAuthenticated } from '@/features/api/use-admin-auth';
import { AdminLayout } from '@/components/admin/AdminLayout';

// Force dynamic rendering for all admin pages
export const dynamic = 'force-dynamic';

export default function AdminLayoutWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const [mounted, setMounted] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, isLoading } = useIsAdminAuthenticated();

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isLoading && !isAuthenticated && pathname !== '/admin/login') {
      router.push('/admin/login');
    }
  }, [mounted, isAuthenticated, isLoading, pathname, router]);

  // Don't render anything until mounted
  if (!mounted) {
    return null;
  }

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show login page without admin layout
  if (pathname === '/admin/login') {
    return <>{children}</>;
  }

  // Show protected content with admin layout
  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  return <AdminLayout>{children}</AdminLayout>;
}
