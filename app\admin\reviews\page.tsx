'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useIsAdminAuthenticated } from '@/features/api/use-admin-auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { <PERSON>, Filter, Eye, CheckCircle, XCircle, MessageSquare, Star, Calendar, User, ThumbsUp, ThumbsDown } from 'lucide-react';
import { toast } from 'sonner';

interface Review {
  id: string;
  title: string;
  review: string;
  overallRating: number;
  ratings: {
    academics?: number;
    faculty?: number;
    infrastructure?: number;
    placements?: number;
    socialLife?: number;
    valueForMoney?: number;
  };
  pros?: string;
  cons?: string;
  status: 'pending' | 'approved' | 'rejected';
  studentId: string;
  collegeId: string;
  courseName?: string;
  collegeName?: string;
  studentName?: string;
  yearOfStudy?: string;
  graduationYear?: number;
  isAnonymous: boolean;
  helpfulCount: number;
  reportCount: number;
  moderatedBy?: string;
  moderatedAt?: string;
  moderationNotes?: string;
  createdAt: string;
  updatedAt: string;
}

interface ReviewsResponse {
  success: boolean;
  data: {
    reviews: Review[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export default function ReviewsPage() {
  const { user } = useIsAdminAuthenticated();
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState('pending');
  const [search, setSearch] = useState('');
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [moderationNotes, setModerationNotes] = useState('');
  const [showModerationDialog, setShowModerationDialog] = useState(false);
  const [moderationAction, setModerationAction] = useState<'approve' | 'reject'>('approve');
  const limit = 10;

  const { data: reviewsData, isLoading, error } = useQuery<ReviewsResponse>({
    queryKey: ['admin-reviews', page, status, search],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(status && { status }),
        ...(search && { search }),
      });

      const response = await fetch(`/api/admin/reviews?${params}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch reviews');
      }

      return response.json();
    },
  });

  const moderateReview = useMutation({
    mutationFn: async ({ reviewId, action, notes }: { reviewId: string; action: 'approve' | 'reject'; notes?: string }) => {
      const response = await fetch(`/api/admin/reviews/${reviewId}/moderate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ action, notes }),
      });

      if (!response.ok) {
        throw new Error('Failed to moderate review');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-reviews'] });
      toast.success('Review moderated successfully');
      setShowModerationDialog(false);
      setSelectedReview(null);
      setModerationNotes('');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to moderate review');
    },
  });

  const deleteReview = useMutation({
    mutationFn: async (reviewId: string) => {
      const response = await fetch(`/api/admin/reviews/${reviewId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete review');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-reviews'] });
      toast.success('Review deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to delete review');
    },
  });

  const reviews = reviewsData?.data?.reviews || [];
  const totalPages = reviewsData?.data?.totalPages || 1;

  const canModerateReviews = user?.role === 'super_admin' || user?.role === 'review_moderator';

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Reviews</h1>
          <p className="mt-1 text-sm text-gray-600">
            Moderate and manage college reviews
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow mb-6 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search reviews..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="sm:w-48">
            <select
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Reviews</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </div>
      </div>

      {/* Reviews List */}
      <div className="bg-white rounded-lg shadow">
        {isLoading ? (
          <div className="p-6">
            <div className="animate-pulse space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="border-b border-gray-200 pb-4">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      <div className="h-3 bg-gray-200 rounded w-full"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : error ? (
          <div className="p-6 text-center">
            <div className="text-red-600 mb-2">Error loading reviews</div>
            <p className="text-gray-600">{error.message}</p>
          </div>
        ) : reviews.length === 0 ? (
          <div className="p-6 text-center">
            <div className="text-gray-400 mb-2">No reviews found</div>
            <p className="text-gray-600">
              {search ? 'Try adjusting your search criteria' : 'No reviews to moderate'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {reviews.map((review: any) => (
              <div key={review.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-gray-600 font-medium text-sm">
                          {review.studentName?.charAt(0) || 'A'}
                        </span>
                      </div>
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">{review.title}</h3>
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <span>{review.studentName || 'Anonymous'}</span>
                          <span>•</span>
                          <span>{review.collegeName}</span>
                          <span>•</span>
                          <span>{new Date(review.createdAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 mb-3">
                      <div className="flex items-center">
                        <span className="text-sm font-medium text-gray-700 mr-2">Overall:</span>
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <svg
                              key={i}
                              className={`w-4 h-4 ${
                                i < Math.floor(review.overallRating)
                                  ? 'text-yellow-400'
                                  : 'text-gray-300'
                              }`}
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                          ))}
                          <span className="ml-1 text-sm text-gray-600">
                            {review.overallRating}/5
                          </span>
                        </div>
                      </div>

                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        review.status === 'approved'
                          ? 'bg-green-100 text-green-800'
                          : review.status === 'rejected'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {review.status}
                      </span>
                    </div>

                    <p className="text-gray-700 mb-4 line-clamp-3">{review.review}</p>

                    {review.pros && (
                      <div className="mb-2">
                        <span className="text-sm font-medium text-green-700">Pros: </span>
                        <span className="text-sm text-gray-600">{review.pros}</span>
                      </div>
                    )}

                    {review.cons && (
                      <div className="mb-4">
                        <span className="text-sm font-medium text-red-700">Cons: </span>
                        <span className="text-sm text-gray-600">{review.cons}</span>
                      </div>
                    )}

                    {canModerateReviews && review.status === 'pending' && (
                      <div className="flex items-center space-x-3">
                        <Button
                          onClick={() => moderateReview.mutate({ reviewId: review.id, action: 'approve' })}
                          disabled={moderateReview.isPending}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="mr-1 h-4 w-4" />
                          Approve
                        </Button>
                        <Button
                          onClick={() => moderateReview.mutate({ reviewId: review.id, action: 'reject' })}
                          disabled={moderateReview.isPending}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          <XCircle className="mr-1 h-4 w-4" />
                          Reject
                        </Button>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline">
                              <Eye className="mr-1 h-4 w-4" />
                              View Details
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Review Details</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div>
                                <h3 className="font-medium text-gray-900">{review.title}</h3>
                                <p className="text-sm text-gray-600">
                                  By {review.studentName || 'Anonymous'} • {review.collegeName}
                                </p>
                              </div>

                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <span className="text-sm font-medium text-gray-700">Overall Rating:</span>
                                  <div className="flex items-center">
                                    {[...Array(5)].map((_, i) => (
                                      <Star
                                        key={i}
                                        className={`w-4 h-4 ${
                                          i < Math.floor(review.overallRating)
                                            ? 'text-yellow-400 fill-current'
                                            : 'text-gray-300'
                                        }`}
                                      />
                                    ))}
                                    <span className="ml-1 text-sm">{review.overallRating}/5</span>
                                  </div>
                                </div>

                                <div>
                                  <span className="text-sm font-medium text-gray-700">Status:</span>
                                  <Badge className={`ml-2 ${
                                    review.status === 'approved'
                                      ? 'bg-green-100 text-green-800'
                                      : review.status === 'rejected'
                                      ? 'bg-red-100 text-red-800'
                                      : 'bg-yellow-100 text-yellow-800'
                                  }`}>
                                    {review.status}
                                  </Badge>
                                </div>
                              </div>

                              <div>
                                <span className="text-sm font-medium text-gray-700">Review:</span>
                                <p className="mt-1 text-sm text-gray-600">{review.review}</p>
                              </div>

                              {review.pros && (
                                <div>
                                  <span className="text-sm font-medium text-green-700">Pros:</span>
                                  <p className="mt-1 text-sm text-gray-600">{review.pros}</p>
                                </div>
                              )}

                              {review.cons && (
                                <div>
                                  <span className="text-sm font-medium text-red-700">Cons:</span>
                                  <p className="mt-1 text-sm text-gray-600">{review.cons}</p>
                                </div>
                              )}

                              <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                  <span className="font-medium text-gray-700">Helpful Count:</span>
                                  <span className="ml-1">{review.helpfulCount}</span>
                                </div>
                                <div>
                                  <span className="font-medium text-gray-700">Report Count:</span>
                                  <span className="ml-1">{review.reportCount}</span>
                                </div>
                              </div>

                              {canModerateReviews && review.status === 'pending' && (
                                <div className="flex items-center space-x-3 pt-4 border-t">
                                  <Button
                                    onClick={() => moderateReview.mutate({ reviewId: review.id, action: 'approve' })}
                                    disabled={moderateReview.isPending}
                                    className="bg-green-600 hover:bg-green-700"
                                  >
                                    <CheckCircle className="mr-1 h-4 w-4" />
                                    Approve
                                  </Button>
                                  <Button
                                    onClick={() => moderateReview.mutate({ reviewId: review.id, action: 'reject' })}
                                    disabled={moderateReview.isPending}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    <XCircle className="mr-1 h-4 w-4" />
                                    Reject
                                  </Button>
                                </div>
                              )}
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-3 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {reviews.length} of {reviewsData?.data?.total || 0} reviews
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page - 1)}
                  disabled={page === 1}
                >
                  Previous
                </Button>
                <span className="px-3 py-1 text-sm">
                  Page {page} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={page === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
