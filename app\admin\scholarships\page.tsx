'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Search, Filter, Eye, CheckCircle, XCircle, Clock, DollarSign, User, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { useAuthToken } from '@/hooks/use-local-storage';
import { ClientOnly } from '@/components/client-only';
import { TableSkeleton } from '@/components/ui/loading-skeleton';

interface ScholarshipApplication {
  id: string;
  name: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  gender: string;
  category: string;
  familyIncome: number;
  courseAppliedFor: string;
  status: 'received' | 'under_review' | 'shortlisted' | 'approved' | 'rejected' | 'disbursed';
  scholarshipAmount: number;
  disbursementStatus: string;
  reviewNotes: string;
  createdAt: string;
  updatedAt: string;
}

interface ScholarshipsResponse {
  success: boolean;
  data: {
    applications: ScholarshipApplication[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export default function ScholarshipsPage() {
  const [mounted, setMounted] = useState(false);
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState<string>('all');
  const [selectedApplication, setSelectedApplication] = useState<ScholarshipApplication | null>(null);
  const [reviewNotes, setReviewNotes] = useState('');
  const [newStatus, setNewStatus] = useState('');
  const [scholarshipAmount, setScholarshipAmount] = useState('');
  const limit = 10;

  const queryClient = useQueryClient();
  const { getAuthHeaders } = useAuthToken();

  // All hooks must be called before any conditional returns
  const { data: scholarshipsData, isLoading } = useQuery<ScholarshipsResponse>({
    queryKey: ['admin-scholarships', page, search, status],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
        ...(status && status !== 'all' && { status }),
      });

      const response = await fetch(`/api/scholarships?${params}`, {
        headers: getAuthHeaders(),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch scholarship applications');
      }

      return response.json();
    },
  });

  const updateApplicationMutation = useMutation({
    mutationFn: async (data: {
      applicationId: string;
      status: string;
      reviewNotes?: string;
      scholarshipAmount?: number;
    }) => {
      const response = await fetch(`/api/scholarships/${data.applicationId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        },
        credentials: 'include',
        body: JSON.stringify({
          status: data.status,
          notes: data.reviewNotes,
          disbursementAmount: data.scholarshipAmount,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update scholarship application');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-scholarships'] });
      toast.success('Scholarship application updated successfully');
      setSelectedApplication(null);
      setReviewNotes('');
      setNewStatus('');
      setScholarshipAmount('');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update scholarship application');
    },
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'received':
        return <Badge className="bg-blue-100 text-blue-800">Received</Badge>;
      case 'under_review':
        return <Badge className="bg-yellow-100 text-yellow-800">Under Review</Badge>;
      case 'shortlisted':
        return <Badge className="bg-purple-100 text-purple-800">Shortlisted</Badge>;
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800">Rejected</Badge>;
      case 'disbursed':
        return <Badge className="bg-emerald-100 text-emerald-800">Disbursed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const handleUpdateApplication = () => {
    if (!selectedApplication || !newStatus) return;

    updateApplicationMutation.mutate({
      applicationId: selectedApplication.id,
      status: newStatus,
      reviewNotes: reviewNotes,
      scholarshipAmount: scholarshipAmount ? parseInt(scholarshipAmount) : undefined,
    });
  };

  const applications = scholarshipsData?.data?.applications || [];
  const totalPages = scholarshipsData?.data?.totalPages || 1;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Scholarship Applications</h1>
          <p className="text-muted-foreground">
            Review and manage scholarship applications
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search applications..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="received">Received</SelectItem>
            <SelectItem value="under_review">Under Review</SelectItem>
            <SelectItem value="shortlisted">Shortlisted</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
            <SelectItem value="disbursed">Disbursed</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Applications Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Applicant</TableHead>
              <TableHead>Course</TableHead>
              <TableHead>Family Income</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Applied Date</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  Loading applications...
                </TableCell>
              </TableRow>
            ) : applications.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  No applications found
                </TableCell>
              </TableRow>
            ) : (
              applications.map((application) => (
                <TableRow key={application.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium flex items-center">
                        <User className="mr-2 h-4 w-4 text-muted-foreground" />
                        {application.name}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {application.email} • {application.category}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Age: {calculateAge(application.dateOfBirth)} • {application.gender}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{application.courseAppliedFor}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <DollarSign className="mr-1 h-4 w-4 text-muted-foreground" />
                      {formatCurrency(application.familyIncome)}
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(application.status)}</TableCell>
                  <TableCell>
                    {application.scholarshipAmount ? (
                      <div className="flex items-center text-green-600">
                        <DollarSign className="mr-1 h-4 w-4" />
                        {formatCurrency(application.scholarshipAmount)}
                      </div>
                    ) : (
                      <span className="text-muted-foreground">Not set</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Calendar className="mr-1 h-4 w-4 text-muted-foreground" />
                      {formatDate(application.createdAt)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedApplication(application);
                              setNewStatus(application.status);
                              setReviewNotes(application.reviewNotes || '');
                              setScholarshipAmount(application.scholarshipAmount?.toString() || '');
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Review Scholarship Application</DialogTitle>
                            <DialogDescription>
                              Review and update the status of {selectedApplication?.name}'s application
                            </DialogDescription>
                          </DialogHeader>
                          {selectedApplication && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <Label className="text-sm font-medium">Name</Label>
                                  <p className="text-sm">{selectedApplication.name}</p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium">Email</Label>
                                  <p className="text-sm">{selectedApplication.email}</p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium">Phone</Label>
                                  <p className="text-sm">{selectedApplication.phone}</p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium">Category</Label>
                                  <p className="text-sm">{selectedApplication.category}</p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium">Family Income</Label>
                                  <p className="text-sm">{formatCurrency(selectedApplication.familyIncome)}</p>
                                </div>
                                <div>
                                  <Label className="text-sm font-medium">Course Applied For</Label>
                                  <p className="text-sm">{selectedApplication.courseAppliedFor}</p>
                                </div>
                              </div>

                              <div className="space-y-2">
                                <Label htmlFor="status">Status</Label>
                                <Select value={newStatus} onValueChange={setNewStatus}>
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="received">Received</SelectItem>
                                    <SelectItem value="under_review">Under Review</SelectItem>
                                    <SelectItem value="shortlisted">Shortlisted</SelectItem>
                                    <SelectItem value="approved">Approved</SelectItem>
                                    <SelectItem value="rejected">Rejected</SelectItem>
                                    <SelectItem value="disbursed">Disbursed</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="space-y-2">
                                <Label htmlFor="amount">Scholarship Amount (₹)</Label>
                                <Input
                                  id="amount"
                                  type="number"
                                  placeholder="Enter scholarship amount"
                                  value={scholarshipAmount}
                                  onChange={(e) => setScholarshipAmount(e.target.value)}
                                />
                              </div>

                              <div className="space-y-2">
                                <Label htmlFor="notes">Review Notes</Label>
                                <Textarea
                                  id="notes"
                                  placeholder="Add review notes..."
                                  value={reviewNotes}
                                  onChange={(e) => setReviewNotes(e.target.value)}
                                  rows={3}
                                />
                              </div>
                            </div>
                          )}
                          <DialogFooter>
                            <Button
                              onClick={handleUpdateApplication}
                              disabled={updateApplicationMutation.isPending}
                            >
                              {updateApplicationMutation.isPending ? 'Updating...' : 'Update Application'}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>

                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-green-600"
                        onClick={() =>
                          updateApplicationMutation.mutate({
                            applicationId: application.id,
                            status: 'approved',
                          })
                        }
                        disabled={application.status === 'approved' || application.status === 'disbursed'}
                      >
                        <CheckCircle className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-600"
                        onClick={() =>
                          updateApplicationMutation.mutate({
                            applicationId: application.id,
                            status: 'rejected',
                          })
                        }
                        disabled={application.status === 'rejected' || application.status === 'disbursed'}
                      >
                        <XCircle className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {applications.length} of {scholarshipsData?.data?.total || 0} applications
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
            >
              Previous
            </Button>
            <span className="text-sm">
              Page {page} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}


