'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Save, Settings, Globe, Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';

interface WebsiteSetting {
  id: string;
  key: string;
  value: any;
  description: string;
  category: string;
  isPublic: boolean;
}

interface SettingsResponse {
  success: boolean;
  data: WebsiteSetting[];
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<Record<string, any>>({});
  const queryClient = useQueryClient();

  const { data: settingsData, isLoading } = useQuery<SettingsResponse>({
    queryKey: ['admin-settings'],
    queryFn: async () => {
      const response = await fetch('/api/admin/settings', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }

      return response.json();
    },
  });

  // Handle data transformation when data changes
  useEffect(() => {
    if (data?.data) {
      const settingsMap: Record<string, any> = {};
      data.data.forEach((setting: any) => {
        settingsMap[setting.key] = setting.value;
      });
      setSettings(settingsMap);
    }
  }, [data]);

  const updateSettingsMutation = useMutation({
    mutationFn: async (updatedSettings: Record<string, any>) => {
      const response = await fetch('/api/admin/settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(updatedSettings),
      });

      if (!response.ok) {
        throw new Error('Failed to update settings');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-settings'] });
      toast.success('Settings updated successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update settings');
    },
  });

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSave = () => {
    updateSettingsMutation.mutate(settings);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Settings className="mx-auto h-8 w-8 animate-spin text-muted-foreground" />
          <p className="mt-2 text-muted-foreground">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Website Settings</h1>
          <p className="text-muted-foreground">
            Configure your website settings and preferences
          </p>
        </div>
        <Button onClick={handleSave} disabled={updateSettingsMutation.isPending}>
          <Save className="mr-2 h-4 w-4" />
          {updateSettingsMutation.isPending ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="contact">Contact</TabsTrigger>
          <TabsTrigger value="social">Social Media</TabsTrigger>
          <TabsTrigger value="seo">SEO</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>
                Basic website information and branding
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="site_name">Site Name</Label>
                  <Input
                    id="site_name"
                    value={settings.site_name || ''}
                    onChange={(e) => handleSettingChange('site_name', e.target.value)}
                    placeholder="College Campus"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="site_tagline">Site Tagline</Label>
                  <Input
                    id="site_tagline"
                    value={settings.site_tagline || ''}
                    onChange={(e) => handleSettingChange('site_tagline', e.target.value)}
                    placeholder="Find Your Perfect College"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="site_description">Site Description</Label>
                <Textarea
                  id="site_description"
                  value={settings.site_description || ''}
                  onChange={(e) => handleSettingChange('site_description', e.target.value)}
                  placeholder="A comprehensive platform for college information and admissions"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="site_logo">Site Logo URL</Label>
                  <Input
                    id="site_logo"
                    value={settings.site_logo || ''}
                    onChange={(e) => handleSettingChange('site_logo', e.target.value)}
                    placeholder="https://example.com/logo.png"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="site_favicon">Favicon URL</Label>
                  <Input
                    id="site_favicon"
                    value={settings.site_favicon || ''}
                    onChange={(e) => handleSettingChange('site_favicon', e.target.value)}
                    placeholder="https://example.com/favicon.ico"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contact" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
              <CardDescription>
                Contact details displayed on your website
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="contact_email">Email Address</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="contact_email"
                      type="email"
                      className="pl-10"
                      value={settings.contact_email || ''}
                      onChange={(e) => handleSettingChange('contact_email', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact_phone">Phone Number</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="contact_phone"
                      className="pl-10"
                      value={settings.contact_phone || ''}
                      onChange={(e) => handleSettingChange('contact_phone', e.target.value)}
                      placeholder="+91 12345 67890"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="contact_address">Address</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
                  <Textarea
                    id="contact_address"
                    className="pl-10"
                    value={settings.contact_address || ''}
                    onChange={(e) => handleSettingChange('contact_address', e.target.value)}
                    placeholder="123 Education Street, Knowledge City, State - 123456"
                    rows={3}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="business_hours">Business Hours</Label>
                  <Input
                    id="business_hours"
                    value={settings.business_hours || ''}
                    onChange={(e) => handleSettingChange('business_hours', e.target.value)}
                    placeholder="Mon-Fri: 9:00 AM - 6:00 PM"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="support_email">Support Email</Label>
                  <Input
                    id="support_email"
                    type="email"
                    value={settings.support_email || ''}
                    onChange={(e) => handleSettingChange('support_email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="social" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Social Media Links</CardTitle>
              <CardDescription>
                Social media profiles and links
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="facebook_url">Facebook</Label>
                  <div className="relative">
                    <Facebook className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="facebook_url"
                      className="pl-10"
                      value={settings.facebook_url || ''}
                      onChange={(e) => handleSettingChange('facebook_url', e.target.value)}
                      placeholder="https://facebook.com/collegecampus"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="twitter_url">Twitter</Label>
                  <div className="relative">
                    <Twitter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="twitter_url"
                      className="pl-10"
                      value={settings.twitter_url || ''}
                      onChange={(e) => handleSettingChange('twitter_url', e.target.value)}
                      placeholder="https://twitter.com/collegecampus"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="instagram_url">Instagram</Label>
                  <div className="relative">
                    <Instagram className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="instagram_url"
                      className="pl-10"
                      value={settings.instagram_url || ''}
                      onChange={(e) => handleSettingChange('instagram_url', e.target.value)}
                      placeholder="https://instagram.com/collegecampus"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="linkedin_url">LinkedIn</Label>
                  <div className="relative">
                    <Linkedin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="linkedin_url"
                      className="pl-10"
                      value={settings.linkedin_url || ''}
                      onChange={(e) => handleSettingChange('linkedin_url', e.target.value)}
                      placeholder="https://linkedin.com/company/collegecampus"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seo" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>SEO Settings</CardTitle>
              <CardDescription>
                Search engine optimization settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="meta_title">Default Meta Title</Label>
                <Input
                  id="meta_title"
                  value={settings.meta_title || ''}
                  onChange={(e) => handleSettingChange('meta_title', e.target.value)}
                  placeholder="College Campus - Find Your Perfect College"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="meta_description">Default Meta Description</Label>
                <Textarea
                  id="meta_description"
                  value={settings.meta_description || ''}
                  onChange={(e) => handleSettingChange('meta_description', e.target.value)}
                  placeholder="Discover the best colleges and universities. Get detailed information, reviews, and admission guidance."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="meta_keywords">Default Meta Keywords</Label>
                <Input
                  id="meta_keywords"
                  value={settings.meta_keywords || ''}
                  onChange={(e) => handleSettingChange('meta_keywords', e.target.value)}
                  placeholder="college, university, admission, education, courses"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="google_analytics_id">Google Analytics ID</Label>
                  <Input
                    id="google_analytics_id"
                    value={settings.google_analytics_id || ''}
                    onChange={(e) => handleSettingChange('google_analytics_id', e.target.value)}
                    placeholder="GA-XXXXXXXXX-X"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="google_search_console">Google Search Console</Label>
                  <Input
                    id="google_search_console"
                    value={settings.google_search_console || ''}
                    onChange={(e) => handleSettingChange('google_search_console', e.target.value)}
                    placeholder="Verification code"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="features" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Feature Settings</CardTitle>
              <CardDescription>
                Enable or disable website features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>User Registration</Label>
                  <p className="text-sm text-muted-foreground">Allow users to register on the website</p>
                </div>
                <Switch
                  checked={settings.enable_user_registration || false}
                  onCheckedChange={(checked) => handleSettingChange('enable_user_registration', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Reviews</Label>
                  <p className="text-sm text-muted-foreground">Allow users to submit college reviews</p>
                </div>
                <Switch
                  checked={settings.enable_reviews || false}
                  onCheckedChange={(checked) => handleSettingChange('enable_reviews', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Scholarship Applications</Label>
                  <p className="text-sm text-muted-foreground">Enable scholarship application feature</p>
                </div>
                <Switch
                  checked={settings.enable_scholarships || false}
                  onCheckedChange={(checked) => handleSettingChange('enable_scholarships', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Contact Form</Label>
                  <p className="text-sm text-muted-foreground">Enable contact form submissions</p>
                </div>
                <Switch
                  checked={settings.enable_contact_form || false}
                  onCheckedChange={(checked) => handleSettingChange('enable_contact_form', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Newsletter</Label>
                  <p className="text-sm text-muted-foreground">Enable newsletter subscription</p>
                </div>
                <Switch
                  checked={settings.enable_newsletter || false}
                  onCheckedChange={(checked) => handleSettingChange('enable_newsletter', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Maintenance Mode</Label>
                  <p className="text-sm text-muted-foreground">Put website in maintenance mode</p>
                </div>
                <Switch
                  checked={settings.maintenance_mode || false}
                  onCheckedChange={(checked) => handleSettingChange('maintenance_mode', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
