import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import bcrypt from 'bcryptjs';
import { jwtVerify } from 'jose';
import { db } from '@/lib/db';
import {
  adminUsers,
  adminActivityLogs,
  colleges,
  courses,
  reviews,
  contactInquiries,
  scholarshipApplications,
  studentAdmissions,
  articles
} from '@/lib/db/schema';
import { eq, and, count, desc } from 'drizzle-orm';

const adminRoutes = new Hono();

// JWT Secret
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');

// Middleware to verify admin authentication
async function verifyAdminAuth(c: any, next: any) {
  try {
    const authHeader = c.req.header('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'No token provided' }, 401);
    }

    const token = authHeader.substring(7);
    const { payload } = await jwtVerify(token, JWT_SECRET);

    if (!payload) {
      return c.json({ error: 'Invalid token' }, 401);
    }

    // Get admin user
    const [admin] = await db
      .select()
      .from(adminUsers)
      .where(and(
        eq(adminUsers.id, payload.id as string),
        eq(adminUsers.isActive, true)
      ))
      .limit(1);

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401);
    }

    c.set('admin', admin);
    await next();
  } catch (error) {
    console.error('Auth verification error:', error);
    return c.json({ error: 'Unauthorized' }, 401);
  }
}

// Middleware to check permissions
function requirePermission(permission: string) {
  return async (c: any, next: any) => {
    const admin = c.get('admin') as any;

    // Super admin has all permissions
    if (admin.role === 'super_admin') {
      await next();
      return;
    }

    // Check role-based permissions
    const rolePermissions: Record<string, string[]> = {
      'content_manager': ['manage_colleges', 'manage_courses', 'manage_articles', 'manage_seo'],
      'review_moderator': ['manage_reviews', 'view_reviews'],
      'lead_manager': ['manage_leads', 'manage_scholarships', 'view_leads'],
      'finance_officer': ['manage_admissions', 'view_financials', 'manage_scholarships'],
      'seo_specialist': ['manage_seo', 'view_analytics'],
    };

    const userPermissions = rolePermissions[admin.role] || [];

    if (!userPermissions.includes(permission)) {
      return c.json({ error: 'Insufficient permissions' }, 403);
    }

    await next();
  };
}

// Apply auth middleware to all admin routes
adminRoutes.use('*', verifyAdminAuth);

// Dashboard Stats
adminRoutes.get('/dashboard/stats', async (c) => {
  try {
    // const admin = c.get('admin') as any;

    // Get various counts
    const [
      collegesCount,
      coursesCount,
      reviewsCount,
      pendingReviewsCount,
      leadsCount,
      newLeadsCount,
      scholarshipAppsCount,
      admissionsCount,
      articlesCount,
    ] = await Promise.all([
      db.select({ count: count() }).from(colleges),
      db.select({ count: count() }).from(courses),
      db.select({ count: count() }).from(reviews),
      db.select({ count: count() }).from(reviews).where(eq(reviews.status, 'pending')),
      db.select({ count: count() }).from(contactInquiries),
      db.select({ count: count() }).from(contactInquiries).where(eq(contactInquiries.status, 'new')),
      db.select({ count: count() }).from(scholarshipApplications),
      db.select({ count: count() }).from(studentAdmissions),
      db.select({ count: count() }).from(articles),
    ]);

    // Recent activity
    const recentActivity = await db
      .select({
        id: adminActivityLogs.id,
        action: adminActivityLogs.action,
        resource: adminActivityLogs.resource,
        createdAt: adminActivityLogs.createdAt,
        adminName: adminUsers.name,
      })
      .from(adminActivityLogs)
      .leftJoin(adminUsers, eq(adminActivityLogs.adminId, adminUsers.id))
      .orderBy(desc(adminActivityLogs.createdAt))
      .limit(10);

    return c.json({
      success: true,
      data: {
        stats: {
          colleges: collegesCount[0].count,
          courses: coursesCount[0].count,
          reviews: reviewsCount[0].count,
          pendingReviews: pendingReviewsCount[0].count,
          leads: leadsCount[0].count,
          newLeads: newLeadsCount[0].count,
          scholarshipApplications: scholarshipAppsCount[0].count,
          admissions: admissionsCount[0].count,
          articles: articlesCount[0].count,
        },
        recentActivity,
      },
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return c.json({ error: 'Failed to fetch dashboard stats' }, 500);
  }
});

// Admin User Management
const createAdminSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  role: z.enum(['super_admin', 'content_manager', 'review_moderator', 'lead_manager', 'finance_officer', 'seo_specialist']),
});

// Create Admin User (Super Admin only)
adminRoutes.post('/users', requirePermission('manage_admins'), zValidator('json', createAdminSchema), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const { name, email, password, role } = c.req.valid('json');

    // Only super admin can create other admins
    if (admin.role !== 'super_admin') {
      return c.json({ error: 'Only super admin can create admin users' }, 403);
    }

    // Check if email already exists
    const [existingAdmin] = await db
      .select()
      .from(adminUsers)
      .where(eq(adminUsers.email, email))
      .limit(1);

    if (existingAdmin) {
      return c.json({ error: 'Email already exists' }, 400);
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create admin user
    const [newAdmin] = await db
      .insert(adminUsers)
      .values({
        name,
        email,
        password: hashedPassword,
        role,
        createdBy: admin.id,
      })
      .returning({
        id: adminUsers.id,
        name: adminUsers.name,
        email: adminUsers.email,
        role: adminUsers.role,
        isActive: adminUsers.isActive,
        createdAt: adminUsers.createdAt,
      });

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'create_admin',
      resource: 'admin_users',
      resourceId: newAdmin.id,
      details: { name, email, role },
    });

    return c.json({
      success: true,
      data: newAdmin,
      message: 'Admin user created successfully',
    });
  } catch (error) {
    console.error('Create admin error:', error);
    return c.json({ error: 'Failed to create admin user' }, 500);
  }
});

// Get All Admin Users
adminRoutes.get('/users', requirePermission('manage_admins'), async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const offset = (page - 1) * limit;

    const [admins, totalCount] = await Promise.all([
      db.select({
        id: adminUsers.id,
        name: adminUsers.name,
        email: adminUsers.email,
        role: adminUsers.role,
        isActive: adminUsers.isActive,
        lastLogin: adminUsers.lastLogin,
        createdAt: adminUsers.createdAt,
        createdBy: adminUsers.createdBy,
      }).from(adminUsers).orderBy(desc(adminUsers.createdAt)).limit(limit).offset(offset),
      db.select({ count: count() }).from(adminUsers),
    ]);

    const totalPages = Math.ceil(totalCount[0].count / limit);

    return c.json({
      success: true,
      data: admins,
      meta: {
        page,
        limit,
        total: totalCount[0].count,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('Get admins error:', error);
    return c.json({ error: 'Failed to fetch admin users' }, 500);
  }
});

// Update Admin User Status
adminRoutes.patch('/users/:id/status', requirePermission('manage_admins'), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const userId = c.req.param('id');
    const { isActive } = await c.req.json();

    // Only super admin can update admin status
    if (admin.role !== 'super_admin') {
      return c.json({ error: 'Only super admin can update admin status' }, 403);
    }

    // Cannot deactivate self
    if (userId === admin.id) {
      return c.json({ error: 'Cannot deactivate your own account' }, 400);
    }

    await db
      .update(adminUsers)
      .set({
        isActive,
        updatedAt: new Date()
      })
      .where(eq(adminUsers.id, userId));

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: isActive ? 'activate_admin' : 'deactivate_admin',
      resource: 'admin_users',
      resourceId: userId,
    });

    return c.json({
      success: true,
      message: `Admin ${isActive ? 'activated' : 'deactivated'} successfully`,
    });
  } catch (error) {
    console.error('Update admin status error:', error);
    return c.json({ error: 'Failed to update admin status' }, 500);
  }
});

// Get Activity Logs
adminRoutes.get('/activity-logs', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const offset = (page - 1) * limit;

    const [logs, totalCount] = await Promise.all([
      db.select({
        id: adminActivityLogs.id,
        action: adminActivityLogs.action,
        resource: adminActivityLogs.resource,
        resourceId: adminActivityLogs.resourceId,
        details: adminActivityLogs.details,
        ipAddress: adminActivityLogs.ipAddress,
        createdAt: adminActivityLogs.createdAt,
        adminName: adminUsers.name,
        adminEmail: adminUsers.email,
      })
      .from(adminActivityLogs)
      .leftJoin(adminUsers, eq(adminActivityLogs.adminId, adminUsers.id))
      .orderBy(desc(adminActivityLogs.createdAt))
      .limit(limit)
      .offset(offset),
      db.select({ count: count() }).from(adminActivityLogs),
    ]);

    const totalPages = Math.ceil(totalCount[0].count / limit);

    return c.json({
      success: true,
      data: logs,
      meta: {
        page,
        limit,
        total: totalCount[0].count,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('Get activity logs error:', error);
    return c.json({ error: 'Failed to fetch activity logs' }, 500);
  }
});

export { adminRoutes };
