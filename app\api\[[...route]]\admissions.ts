import { Hono } from 'hono';
import { db } from '@/lib/db';
import { studentAdmissions, colleges, courses, adminUsers } from '@/lib/db/schema';
import { eq, and, like, desc, count, sql } from 'drizzle-orm';
// Import auth middleware from auth routes
import { jwtVerify } from 'jose';

const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');

// Middleware to verify admin authentication
async function verifyAdminAuth(c: any, next: any) {
  try {
    const authHeader = c.req.header('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'No token provided' }, 401);
    }

    const token = authHeader.substring(7);
    const { payload } = await jwtVerify(token, JWT_SECRET);

    if (!payload) {
      return c.json({ error: 'Invalid token' }, 401);
    }

    // Get admin user
    const [admin] = await db
      .select()
      .from(adminUsers)
      .where(and(
        eq(adminUsers.id, payload.id as string),
        eq(adminUsers.isActive, true)
      ))
      .limit(1);

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401);
    }

    c.set('adminUser', admin);
    await next();
  } catch (error) {
    console.error('Auth verification error:', error);
    return c.json({ error: 'Unauthorized' }, 401);
  }
}

const app = new Hono();

// Get all student admissions with pagination and filters
app.get('/', verifyAdminAuth, async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const collegeId = c.req.query('collegeId') || '';
    const courseId = c.req.query('courseId') || '';
    const year = c.req.query('year') || '';

    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];

    if (search) {
      conditions.push(
        sql`(${studentAdmissions.name} ILIKE ${`%${search}%`} OR ${studentAdmissions.email} ILIKE ${`%${search}%`} OR ${studentAdmissions.studentId} ILIKE ${`%${search}%`})`
      );
    }

    if (status) {
      conditions.push(eq(studentAdmissions.overallStatus, status));
    }

    if (collegeId) {
      conditions.push(eq(studentAdmissions.collegeId, collegeId));
    }

    if (courseId) {
      conditions.push(eq(studentAdmissions.courseId, courseId));
    }

    if (year) {
      conditions.push(eq(studentAdmissions.yearOfAdmission, year));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get admissions with college and course details
    const query = db
      .select({
        id: studentAdmissions.id,
        studentId: studentAdmissions.studentId,
        name: studentAdmissions.name,
        email: studentAdmissions.email,
        phone: studentAdmissions.phone,
        address: studentAdmissions.address,
        collegeId: studentAdmissions.collegeId,
        collegeName: colleges.name,
        courseId: studentAdmissions.courseId,
        courseName: courses.name,
        yearOfAdmission: studentAdmissions.yearOfAdmission,
        admissionDate: studentAdmissions.admissionDate,
        collegeFirstYearFee: studentAdmissions.collegeFirstYearFee,
        scholarshipAmountAwarded: studentAdmissions.scholarshipAmountAwarded,
        scholarshipDisbursementStatus: studentAdmissions.scholarshipDisbursementStatus,
        commissionAmount: studentAdmissions.commissionAmount,
        commissionStatus: studentAdmissions.commissionStatus,
        overallStatus: studentAdmissions.overallStatus,
        createdAt: studentAdmissions.createdAt,
        updatedAt: studentAdmissions.updatedAt,
      })
      .from(studentAdmissions)
      .leftJoin(colleges, eq(studentAdmissions.collegeId, colleges.id))
      .leftJoin(courses, eq(studentAdmissions.courseId, courses.id))
      .where(whereClause);

    const [admissionsList, totalCount] = await Promise.all([
      query.orderBy(desc(studentAdmissions.createdAt)).limit(limit).offset(offset),
      db.select({ count: count() }).from(studentAdmissions).where(whereClause),
    ]);

    const total = totalCount[0].count;
    const totalPages = Math.ceil(total / limit);

    return c.json({
      success: true,
      data: {
        admissions: admissionsList,
        total,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Get admissions error:', error);
    return c.json({ error: 'Failed to fetch admissions' }, 500);
  }
});

// Get admission statistics
app.get('/stats', verifyAdminAuth, async (c) => {
  try {
    const [
      totalAdmissions,
      activeStudents,
      completedStudents,
      withdrawnStudents,
      totalCommission,
      totalScholarships,
      admissionsByYear,
      admissionsByStatus,
    ] = await Promise.all([
      db.select({ count: count() }).from(studentAdmissions),
      db.select({ count: count() }).from(studentAdmissions).where(eq(studentAdmissions.overallStatus, 'active_student')),
      db.select({ count: count() }).from(studentAdmissions).where(eq(studentAdmissions.overallStatus, 'course_completed')),
      db.select({ count: count() }).from(studentAdmissions).where(eq(studentAdmissions.overallStatus, 'withdrew_admission')),
      db.select({
        total: sql<number>`COALESCE(SUM(${studentAdmissions.commissionAmount}), 0)`
      }).from(studentAdmissions),
      db.select({
        total: sql<number>`COALESCE(SUM(${studentAdmissions.scholarshipAmountAwarded}), 0)`
      }).from(studentAdmissions),
      db.select({
        year: studentAdmissions.yearOfAdmission,
        count: count(),
      }).from(studentAdmissions).groupBy(studentAdmissions.yearOfAdmission).orderBy(studentAdmissions.yearOfAdmission),
      db.select({
        status: studentAdmissions.overallStatus,
        count: count(),
      }).from(studentAdmissions).groupBy(studentAdmissions.overallStatus),
    ]);

    return c.json({
      success: true,
      data: {
        total: totalAdmissions[0].count,
        active: activeStudents[0].count,
        completed: completedStudents[0].count,
        withdrawn: withdrawnStudents[0].count,
        totalCommission: totalCommission[0].total,
        totalScholarships: totalScholarships[0].total,
        byYear: admissionsByYear,
        byStatus: admissionsByStatus,
      },
    });
  } catch (error) {
    console.error('Get admission stats error:', error);
    return c.json({ error: 'Failed to fetch admission statistics' }, 500);
  }
});

// Get single admission
app.get('/:id', verifyAdminAuth, async (c) => {
  try {
    const id = c.req.param('id');

    const admission = await db
      .select({
        id: studentAdmissions.id,
        studentId: studentAdmissions.studentId,
        leadId: studentAdmissions.leadId,
        name: studentAdmissions.name,
        email: studentAdmissions.email,
        phone: studentAdmissions.phone,
        address: studentAdmissions.address,
        collegeId: studentAdmissions.collegeId,
        collegeName: colleges.name,
        courseId: studentAdmissions.courseId,
        courseName: courses.name,
        yearOfAdmission: studentAdmissions.yearOfAdmission,
        admissionDate: studentAdmissions.admissionDate,
        collegeFirstYearFee: studentAdmissions.collegeFirstYearFee,
        scholarshipApplicationId: studentAdmissions.scholarshipApplicationId,
        scholarshipAmountAwarded: studentAdmissions.scholarshipAmountAwarded,
        scholarshipDisbursementStatus: studentAdmissions.scholarshipDisbursementStatus,
        scholarshipDisbursementDate: studentAdmissions.scholarshipDisbursementDate,
        scholarshipTransactionId: studentAdmissions.scholarshipTransactionId,
        scholarshipNotes: studentAdmissions.scholarshipNotes,
        commissionAmount: studentAdmissions.commissionAmount,
        commissionPercentage: studentAdmissions.commissionPercentage,
        commissionStatus: studentAdmissions.commissionStatus,
        commissionReceivedDate: studentAdmissions.commissionReceivedDate,
        commissionTransactionId: studentAdmissions.commissionTransactionId,
        commissionNotes: studentAdmissions.commissionNotes,
        overallStatus: studentAdmissions.overallStatus,
        internalNotes: studentAdmissions.internalNotes,
        createdAt: studentAdmissions.createdAt,
        updatedAt: studentAdmissions.updatedAt,
      })
      .from(studentAdmissions)
      .leftJoin(colleges, eq(studentAdmissions.collegeId, colleges.id))
      .leftJoin(courses, eq(studentAdmissions.courseId, courses.id))
      .where(eq(studentAdmissions.id, id))
      .limit(1);

    if (admission.length === 0) {
      return c.json({ error: 'Admission not found' }, 404);
    }

    return c.json({
      success: true,
      data: admission[0],
    });
  } catch (error) {
    console.error('Get admission error:', error);
    return c.json({ error: 'Failed to fetch admission' }, 500);
  }
});

// Create new admission
app.post('/', verifyAdminAuth, async (c) => {
  try {
    const body = await c.req.json();
    const adminUser = c.get('adminUser');

    const newAdmission = await db.insert(studentAdmissions).values({
      ...body,
      createdBy: adminUser.id,
      lastUpdatedBy: adminUser.id,
    }).returning();

    return c.json({
      success: true,
      data: newAdmission[0],
    });
  } catch (error) {
    console.error('Create admission error:', error);
    return c.json({ error: 'Failed to create admission' }, 500);
  }
});

// Update admission
app.patch('/:id', verifyAdminAuth, async (c) => {
  try {
    const id = c.req.param('id');
    const body = await c.req.json();
    const adminUser = c.get('adminUser');

    const updatedAdmission = await db
      .update(studentAdmissions)
      .set({
        ...body,
        lastUpdatedBy: adminUser.id,
        updatedAt: new Date(),
      })
      .where(eq(studentAdmissions.id, id))
      .returning();

    if (updatedAdmission.length === 0) {
      return c.json({ error: 'Admission not found' }, 404);
    }

    return c.json({
      success: true,
      data: updatedAdmission[0],
    });
  } catch (error) {
    console.error('Update admission error:', error);
    return c.json({ error: 'Failed to update admission' }, 500);
  }
});

// Delete admission
app.delete('/:id', verifyAdminAuth, async (c) => {
  try {
    const id = c.req.param('id');

    const deletedAdmission = await db
      .delete(studentAdmissions)
      .where(eq(studentAdmissions.id, id))
      .returning();

    if (deletedAdmission.length === 0) {
      return c.json({ error: 'Admission not found' }, 404);
    }

    return c.json({
      success: true,
      message: 'Admission deleted successfully',
    });
  } catch (error) {
    console.error('Delete admission error:', error);
    return c.json({ error: 'Failed to delete admission' }, 500);
  }
});

export const admissionsRoutes = app;
export default app;
