import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { jwtVerify } from 'jose';
import { db } from '@/lib/db';
import { articles, adminUsers, adminActivityLogs } from '@/lib/db/schema';
import { eq, desc, count, and, like, sql } from 'drizzle-orm';

// Define context variables type
type Variables = {
  admin: any;
  adminUser: any;
};

const articlesRoutes = new Hono<{ Variables: Variables }>();

// JWT Secret
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');

// Auth middleware
const verifyAdminAuth = async (c: any, next: any) => {
  try {
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const token = authHeader.substring(7);
    const { payload } = await jwtVerify(token, JWT_SECRET);

    const [admin] = await db
      .select()
      .from(adminUsers)
      .where(eq(adminUsers.id, payload.id as string))
      .limit(1);

    if (!admin || !admin.isActive) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    c.set('admin', admin);
    await next();
  } catch (error) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
};

// Permission check middleware
function requirePermission(permission: string) {
  return async (c: any, next: any) => {
    const admin = c.get('admin') as any;

    // Super admin has all permissions
    if (admin.role === 'super_admin') {
      await next();
      return;
    }

    // Check role-based permissions
    const rolePermissions: Record<string, string[]> = {
      'content_manager': ['manage_articles', 'view_articles'],
      'review_moderator': ['view_articles'],
      'lead_manager': ['view_articles'],
      'finance_officer': ['view_articles'],
      'seo_specialist': ['manage_articles', 'view_articles'],
    };

    const userPermissions = rolePermissions[admin.role] || [];
    if (!userPermissions.includes(permission)) {
      return c.json({ error: 'Insufficient permissions' }, 403);
    }

    await next();
  };
}

// Article validation schema
const articleSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  slug: z.string().optional(),
  content: z.string().min(1, 'Content is required'),
  excerpt: z.string().optional(),
  featuredImage: z.string().url().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  status: z.enum(['draft', 'published', 'archived']).default('draft'),
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.string().optional(),
  publishedAt: z.string().optional(),
});

// Get all articles
articlesRoutes.get('/', verifyAdminAuth, requirePermission('view_articles'), async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const category = c.req.query('category') || '';
    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];

    if (status) {
      conditions.push(eq(articles.status, status));
    }

    if (category) {
      conditions.push(eq(articles.category, category));
    }

    if (search) {
      conditions.push(
        sql`(${articles.title} ILIKE ${`%${search}%`} OR ${articles.content} ILIKE ${`%${search}%`})`
      );
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    const [articlesList, totalCount] = await Promise.all([
      db
        .select()
        .from(articles)
        .where(whereClause)
        .orderBy(desc(articles.createdAt))
        .limit(limit)
        .offset(offset),

      db
        .select({ count: count() })
        .from(articles)
        .where(whereClause)
    ]);

    const totalPages = Math.ceil(totalCount[0].count / limit);

    return c.json({
      success: true,
      data: {
        articles: articlesList,
        total: totalCount[0].count,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Get articles error:', error);
    return c.json({ error: 'Failed to fetch articles' }, 500);
  }
});

// Create new article
articlesRoutes.post('/', verifyAdminAuth, requirePermission('manage_articles'), zValidator('json', articleSchema), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const articleData = c.req.valid('json');

    // Generate slug from title if not provided
    const slug = articleData.slug || articleData.title.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    // Check if slug already exists
    const [existingArticle] = await db
      .select()
      .from(articles)
      .where(eq(articles.slug, slug))
      .limit(1);

    if (existingArticle) {
      return c.json({ error: 'Article with this slug already exists' }, 400);
    }

    const [newArticle] = await db
      .insert(articles)
      .values({
        ...articleData,
        slug,
        authorId: admin.id,
        publishedAt: articleData.status === 'published' ? new Date() : null,
      })
      .returning();

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'create_article',
      resource: 'articles',
      resourceId: newArticle.id,
      details: { title: newArticle.title, status: newArticle.status },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
    });

    return c.json({
      success: true,
      data: newArticle,
    });
  } catch (error) {
    console.error('Create article error:', error);
    return c.json({ error: 'Failed to create article' }, 500);
  }
});

// Update article
articlesRoutes.put('/:id', verifyAdminAuth, requirePermission('manage_articles'), zValidator('json', articleSchema.partial()), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const articleId = c.req.param('id');
    const updateData = c.req.valid('json');

    // If title is being updated, regenerate slug
    if (updateData.title && !updateData.slug) {
      const newSlug = updateData.title.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');

      // Check if new slug conflicts with existing article
      const [existingArticle] = await db
        .select()
        .from(articles)
        .where(and(
          eq(articles.slug, newSlug),
          sql`${articles.id} != ${articleId}`
        ))
        .limit(1);

      if (existingArticle) {
        return c.json({ error: 'Article with this slug already exists' }, 400);
      }

      updateData.slug = newSlug;
    }

    // Prepare the final update object with proper types
    const finalUpdateData: any = {
      ...updateData,
      updatedAt: new Date(),
    };

    // Set publishedAt if status is being changed to published
    if (updateData.status === 'published') {
      finalUpdateData.publishedAt = new Date();
    }

    const [updatedArticle] = await db
      .update(articles)
      .set(finalUpdateData)
      .where(eq(articles.id, articleId))
      .returning();

    if (!updatedArticle) {
      return c.json({ error: 'Article not found' }, 404);
    }

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'update_article',
      resource: 'articles',
      resourceId: articleId,
      details: { title: updatedArticle.title, changes: Object.keys(updateData) },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
    });

    return c.json({
      success: true,
      data: updatedArticle,
    });
  } catch (error) {
    console.error('Update article error:', error);
    return c.json({ error: 'Failed to update article' }, 500);
  }
});

// Delete article
articlesRoutes.delete('/:id', verifyAdminAuth, requirePermission('manage_articles'), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const articleId = c.req.param('id');

    const [deletedArticle] = await db
      .delete(articles)
      .where(eq(articles.id, articleId))
      .returning();

    if (!deletedArticle) {
      return c.json({ error: 'Article not found' }, 404);
    }

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'delete_article',
      resource: 'articles',
      resourceId: articleId,
      details: { title: deletedArticle.title },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
    });

    return c.json({
      success: true,
      message: 'Article deleted successfully',
    });
  } catch (error) {
    console.error('Delete article error:', error);
    return c.json({ error: 'Failed to delete article' }, 500);
  }
});

export { articlesRoutes };
