import { <PERSON>o } from 'hono';
import { z<PERSON>alidator } from '@hono/zod-validator';
import { z } from 'zod';
import bcrypt from 'bcryptjs';
import { SignJWT, jwtVerify } from 'jose';
import { db } from '@/lib/db';
import { adminUsers, adminActivityLogs } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

const authRoutes = new Hono();

// JWT Secret
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');

// Validation schemas
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

const changePasswordSchema = z.object({
  currentPassword: z.string().min(6),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
});

// Helper function to create JWT token
async function createToken(payload: any) {
  return await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('24h')
    .sign(JWT_SECRET);
}

// Helper function to verify JWT token
async function verifyToken(token: string) {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload;
  } catch (error) {
    return null;
  }
}

// Helper function to log admin activity
async function logActivity(adminId: string, action: string, resource: string, resourceId?: string, details?: any, ipAddress?: string, userAgent?: string) {
  try {
    await db.insert(adminActivityLogs).values({
      adminId,
      action,
      resource,
      resourceId,
      details,
      ipAddress,
      userAgent,
    });
  } catch (error) {
    console.error('Failed to log activity:', error);
  }
}

// Admin Login
authRoutes.post('/admin/login', zValidator('json', loginSchema), async (c) => {
  try {
    const { email, password } = c.req.valid('json');
    const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown';
    const userAgent = c.req.header('user-agent') || 'unknown';

    // Find admin user
    const [admin] = await db
      .select()
      .from(adminUsers)
      .where(and(
        eq(adminUsers.email, email),
        eq(adminUsers.isActive, true)
      ))
      .limit(1);

    if (!admin) {
      await logActivity('unknown', 'login_failed', 'admin_auth', undefined, { email, reason: 'user_not_found' }, ipAddress, userAgent);
      return c.json({ error: 'Invalid credentials' }, 401);
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, admin.password);
    if (!isValidPassword) {
      await logActivity(admin.id, 'login_failed', 'admin_auth', undefined, { reason: 'invalid_password' }, ipAddress, userAgent);
      return c.json({ error: 'Invalid credentials' }, 401);
    }

    // Update last login
    await db
      .update(adminUsers)
      .set({ lastLogin: new Date() })
      .where(eq(adminUsers.id, admin.id));

    // Create JWT token
    const token = await createToken({
      id: admin.id,
      email: admin.email,
      name: admin.name,
      role: admin.role,
    });

    // Log successful login
    await logActivity(admin.id, 'login_success', 'admin_auth', undefined, undefined, ipAddress, userAgent);

    return c.json({
      success: true,
      token,
      user: {
        id: admin.id,
        email: admin.email,
        name: admin.name,
        role: admin.role,
        lastLogin: admin.lastLogin,
      },
    });
  } catch (error) {
    console.error('Login error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Verify Token / Get Current User
authRoutes.get('/admin/me', async (c) => {
  try {
    const authHeader = c.req.header('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'No token provided' }, 401);
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);

    if (!payload) {
      return c.json({ error: 'Invalid token' }, 401);
    }

    // Get fresh user data
    const [admin] = await db
      .select({
        id: adminUsers.id,
        email: adminUsers.email,
        name: adminUsers.name,
        role: adminUsers.role,
        isActive: adminUsers.isActive,
        lastLogin: adminUsers.lastLogin,
      })
      .from(adminUsers)
      .where(and(
        eq(adminUsers.id, payload.id as string),
        eq(adminUsers.isActive, true)
      ))
      .limit(1);

    if (!admin) {
      return c.json({ error: 'User not found' }, 401);
    }

    return c.json({
      success: true,
      user: admin,
    });
  } catch (error) {
    console.error('Token verification error:', error);
    return c.json({ error: 'Invalid token' }, 401);
  }
});

// Change Password
authRoutes.post('/admin/change-password', zValidator('json', changePasswordSchema), async (c) => {
  try {
    const authHeader = c.req.header('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'No token provided' }, 401);
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);

    if (!payload) {
      return c.json({ error: 'Invalid token' }, 401);
    }

    const { currentPassword, newPassword } = c.req.valid('json');
    const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown';
    const userAgent = c.req.header('user-agent') || 'unknown';

    // Get current admin
    const [admin] = await db
      .select()
      .from(adminUsers)
      .where(eq(adminUsers.id, payload.id as string))
      .limit(1);

    if (!admin) {
      return c.json({ error: 'User not found' }, 401);
    }

    // Verify current password
    const isValidPassword = await bcrypt.compare(currentPassword, admin.password);
    if (!isValidPassword) {
      await logActivity(admin.id, 'password_change_failed', 'admin_auth', undefined, { reason: 'invalid_current_password' }, ipAddress, userAgent);
      return c.json({ error: 'Current password is incorrect' }, 400);
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    // Update password
    await db
      .update(adminUsers)
      .set({
        password: hashedPassword,
        updatedAt: new Date()
      })
      .where(eq(adminUsers.id, admin.id));

    // Log password change
    await logActivity(admin.id, 'password_changed', 'admin_auth', undefined, undefined, ipAddress, userAgent);

    return c.json({
      success: true,
      message: 'Password changed successfully',
    });
  } catch (error) {
    console.error('Change password error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Logout (client-side token removal, but we can log the activity)
authRoutes.post('/admin/logout', async (c) => {
  try {
    const authHeader = c.req.header('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const payload = await verifyToken(token);

      if (payload) {
        const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown';
        const userAgent = c.req.header('user-agent') || 'unknown';
        await logActivity(payload.id as string, 'logout', 'admin_auth', undefined, undefined, ipAddress, userAgent);
      }
    }

    return c.json({
      success: true,
      message: 'Logged out successfully',
    });
  } catch (error) {
    console.error('Logout error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

export { authRoutes };
