import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { jwtVerify } from 'jose';
import { db } from '@/lib/db';
import { courses, adminUsers, adminActivityLogs } from '@/lib/db/schema';
import { eq, desc, count, and, like, sql } from 'drizzle-orm';

const coursesRoutes = new Hono();

// JWT Secret
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');

// Auth middleware for admin routes
const verifyAdminAuth = async (c: any, next: any) => {
  try {
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const token = authHeader.substring(7);
    const { payload } = await jwtVerify(token, JWT_SECRET);

    const [admin] = await db
      .select()
      .from(adminUsers)
      .where(eq(adminUsers.id, payload.id as string))
      .limit(1);

    if (!admin || !admin.isActive) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    c.set('admin', admin);
    await next();
  } catch (error) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
};

// Course validation schema
const courseSchema = z.object({
  name: z.string().min(1, 'Course name is required'),
  shortName: z.string().optional(),
  slug: z.string().optional(),
  level: z.string().min(1, 'Level is required'),
  duration: z.string().optional(),
  stream: z.string().min(1, 'Stream is required'),
  description: z.string().optional(),
  detailedDescription: z.string().optional(),
  averageFees: z.number().optional(),
  averageSalary: z.number().optional(),
  isPopular: z.boolean().default(false),
  isPublished: z.boolean().default(false),
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.string().optional(),
  eligibility: z.object({
    minPercentage: z.number().optional(),
    qualification: z.string().optional(),
    ageLimit: z.string().optional(),
    entranceExams: z.array(z.string()).optional(),
  }).optional(),
  syllabus: z.object({
    coreSubjects: z.array(z.string()).optional(),
    electiveSubjects: z.array(z.string()).optional(),
    practicalComponents: z.array(z.string()).optional(),
    projectWork: z.string().optional(),
  }).optional(),
  careerProspects: z.object({
    jobRoles: z.array(z.string()).optional(),
    industries: z.array(z.string()).optional(),
    higherStudyOptions: z.array(z.string()).optional(),
    skillsDeveloped: z.array(z.string()).optional(),
  }).optional(),
});

// Get all courses (public route)
coursesRoutes.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '12');
    const search = c.req.query('search') || '';
    const level = c.req.query('level') || '';
    const stream = c.req.query('stream') || '';
    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];

    if (search) {
      conditions.push(
        sql`(${courses.name} ILIKE ${`%${search}%`} OR ${courses.description} ILIKE ${`%${search}%`})`
      );
    }

    if (level) {
      conditions.push(eq(courses.level, level));
    }

    if (stream) {
      conditions.push(eq(courses.stream, stream));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    const [coursesList, totalCount] = await Promise.all([
      db
        .select()
        .from(courses)
        .where(whereClause)
        .orderBy(desc(courses.createdAt))
        .limit(limit)
        .offset(offset),

      db
        .select({ count: count() })
        .from(courses)
        .where(whereClause)
    ]);

    const totalPages = Math.ceil(totalCount[0].count / limit);

    return c.json({
      success: true,
      data: coursesList,
      meta: {
        page,
        limit,
        total: totalCount[0].count,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('Get courses error:', error);
    return c.json({ error: 'Failed to fetch courses' }, 500);
  }
});

// Create new course (admin only)
coursesRoutes.post('/', verifyAdminAuth, zValidator('json', courseSchema), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const courseData = c.req.valid('json');

    // Generate slug from name if not provided
    const slug = courseData.slug || courseData.name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    // Check if slug already exists
    const [existingCourse] = await db
      .select()
      .from(courses)
      .where(eq(courses.slug, slug))
      .limit(1);

    if (existingCourse) {
      return c.json({ error: 'Course with this slug already exists' }, 400);
    }

    const [newCourse] = await db
      .insert(courses)
      .values({
        ...courseData,
        slug,
        createdBy: admin.id,
      })
      .returning();

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'create_course',
      resource: 'courses',
      resourceId: newCourse.id,
      details: { name: newCourse.name, level: newCourse.level },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
    });

    return c.json({
      success: true,
      data: newCourse,
    });
  } catch (error) {
    console.error('Create course error:', error);
    return c.json({ error: 'Failed to create course' }, 500);
  }
});

// Update course (admin only)
coursesRoutes.put('/:id', verifyAdminAuth, zValidator('json', courseSchema.partial()), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const courseId = c.req.param('id');
    const updateData = c.req.valid('json');

    // If name is being updated, regenerate slug
    if (updateData.name && !updateData.slug) {
      const newSlug = updateData.name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');

      // Check if new slug conflicts with existing course
      const [existingCourse] = await db
        .select()
        .from(courses)
        .where(and(
          eq(courses.slug, newSlug),
          sql`${courses.id} != ${courseId}`
        ))
        .limit(1);

      if (existingCourse) {
        return c.json({ error: 'Course with this slug already exists' }, 400);
      }

      updateData.slug = newSlug;
    }

    const [updatedCourse] = await db
      .update(courses)
      .set({
        ...updateData,
        updatedAt: new Date(),
      })
      .where(eq(courses.id, courseId))
      .returning();

    if (!updatedCourse) {
      return c.json({ error: 'Course not found' }, 404);
    }

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'update_course',
      resource: 'courses',
      resourceId: courseId,
      details: { name: updatedCourse.name, changes: Object.keys(updateData) },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
    });

    return c.json({
      success: true,
      data: updatedCourse,
    });
  } catch (error) {
    console.error('Update course error:', error);
    return c.json({ error: 'Failed to update course' }, 500);
  }
});

// Delete course (admin only)
coursesRoutes.delete('/:id', verifyAdminAuth, async (c) => {
  try {
    const admin = c.get('admin') as any;
    const courseId = c.req.param('id');

    const [deletedCourse] = await db
      .delete(courses)
      .where(eq(courses.id, courseId))
      .returning();

    if (!deletedCourse) {
      return c.json({ error: 'Course not found' }, 404);
    }

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'delete_course',
      resource: 'courses',
      resourceId: courseId,
      details: { name: deletedCourse.name },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
    });

    return c.json({
      success: true,
      message: 'Course deleted successfully',
    });
  } catch (error) {
    console.error('Delete course error:', error);
    return c.json({ error: 'Failed to delete course' }, 500);
  }
});

// Get course by ID or slug
coursesRoutes.get('/:identifier', async (c) => {
  try {
    const identifier = c.req.param('identifier');

    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(identifier);

    const [course] = await db
      .select()
      .from(courses)
      .where(isUUID ? eq(courses.id, identifier) : eq(courses.slug, identifier))
      .limit(1);

    if (!course) {
      return c.json({ error: 'Course not found' }, 404);
    }

    return c.json({
      success: true,
      data: course,
    });
  } catch (error) {
    console.error('Get course error:', error);
    return c.json({ error: 'Failed to fetch course' }, 500);
  }
});

export { coursesRoutes };
