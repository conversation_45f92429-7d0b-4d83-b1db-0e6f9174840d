import { <PERSON>o } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { jwtVerify } from 'jose';
import { db } from '@/lib/db';
import { contactInquiries, adminUsers, adminActivityLogs } from '@/lib/db/schema';
import { eq, desc, count, and, like, sql } from 'drizzle-orm';

const leadsRoutes = new Hono();

// JWT Secret
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');

// Auth middleware
const verifyAdminAuth = async (c: any, next: any) => {
  try {
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const token = authHeader.substring(7);
    const { payload } = await jwtVerify(token, JWT_SECRET);

    const [admin] = await db
      .select()
      .from(adminUsers)
      .where(eq(adminUsers.id, payload.id as string))
      .limit(1);

    if (!admin || !admin.isActive) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    c.set('admin', admin);
    await next();
  } catch (error) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
};

// Permission check middleware
function requirePermission(permission: string) {
  return async (c: any, next: any) => {
    const admin = c.get('admin') as any;

    // Super admin has all permissions
    if (admin.role === 'super_admin') {
      await next();
      return;
    }

    // Check role-based permissions
    const rolePermissions: Record<string, string[]> = {
      'content_manager': ['view_leads'],
      'review_moderator': ['view_leads'],
      'lead_manager': ['manage_leads', 'view_leads'],
      'finance_officer': ['view_leads'],
      'seo_specialist': ['view_leads'],
    };

    const userPermissions = rolePermissions[admin.role] || [];
    if (!userPermissions.includes(permission)) {
      return c.json({ error: 'Insufficient permissions' }, 403);
    }

    await next();
  };
}

// Lead validation schema
const leadSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Valid email is required'),
  phone: z.string().optional(),
  subject: z.string().optional(),
  message: z.string().min(1, 'Message is required'),
  source: z.string().default('website'),
  status: z.enum(['new', 'open', 'counselling_completed', 'admission_confirmed', 'closed', 'rejected', 'follow_up_required']).default('new'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  interestedCourses: z.array(z.string()).optional(),
  interestedColleges: z.array(z.string()).optional(),
  notes: z.string().optional(),
  followUpDate: z.string().optional(),
});

// Get all leads/contact inquiries
leadsRoutes.get('/', verifyAdminAuth, requirePermission('view_leads'), async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const priority = c.req.query('priority') || '';
    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];

    if (status) {
      conditions.push(eq(contactInquiries.status, status));
    }

    if (priority) {
      conditions.push(eq(contactInquiries.priority, priority));
    }

    if (search) {
      conditions.push(
        sql`(${contactInquiries.name} ILIKE ${`%${search}%`} OR ${contactInquiries.email} ILIKE ${`%${search}%`} OR ${contactInquiries.subject} ILIKE ${`%${search}%`})`
      );
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    const [leads, totalCount] = await Promise.all([
      db
        .select()
        .from(contactInquiries)
        .where(whereClause)
        .orderBy(desc(contactInquiries.createdAt))
        .limit(limit)
        .offset(offset),

      db
        .select({ count: count() })
        .from(contactInquiries)
        .where(whereClause)
    ]);

    const totalPages = Math.ceil(totalCount[0].count / limit);

    return c.json({
      success: true,
      data: {
        leads,
        total: totalCount[0].count,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Get leads error:', error);
    return c.json({ error: 'Failed to fetch leads' }, 500);
  }
});

// Create new lead
leadsRoutes.post('/', verifyAdminAuth, requirePermission('manage_leads'), zValidator('json', leadSchema), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const leadData = c.req.valid('json');

    const [newLead] = await db
      .insert(contactInquiries)
      .values({
        ...leadData,
        assignedTo: admin.id,
      })
      .returning();

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'create_lead',
      resource: 'leads',
      resourceId: newLead.id,
      details: { name: newLead.name, email: newLead.email },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
    });

    return c.json({
      success: true,
      data: newLead,
    });
  } catch (error) {
    console.error('Create lead error:', error);
    return c.json({ error: 'Failed to create lead' }, 500);
  }
});

// Update lead
leadsRoutes.patch('/:id', verifyAdminAuth, requirePermission('manage_leads'), zValidator('json', leadSchema.partial()), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const leadId = c.req.param('id');
    const updateData = c.req.valid('json');

    const [updatedLead] = await db
      .update(contactInquiries)
      .set({
        ...updateData,
        updatedAt: new Date(),
      })
      .where(eq(contactInquiries.id, leadId))
      .returning();

    if (!updatedLead) {
      return c.json({ error: 'Lead not found' }, 404);
    }

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'update_lead',
      resource: 'leads',
      resourceId: leadId,
      details: { name: updatedLead.name, changes: Object.keys(updateData) },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
    });

    return c.json({
      success: true,
      data: updatedLead,
    });
  } catch (error) {
    console.error('Update lead error:', error);
    return c.json({ error: 'Failed to update lead' }, 500);
  }
});

// Get lead statistics
leadsRoutes.get('/stats', verifyAdminAuth, requirePermission('view_leads'), async (c) => {
  try {
    const [
      totalLeads,
      newLeads,
      openLeads,
      closedLeads,
      convertedLeads
    ] = await Promise.all([
      db.select({ count: count() }).from(contactInquiries),
      db.select({ count: count() }).from(contactInquiries).where(eq(contactInquiries.status, 'new')),
      db.select({ count: count() }).from(contactInquiries).where(eq(contactInquiries.status, 'open')),
      db.select({ count: count() }).from(contactInquiries).where(eq(contactInquiries.status, 'closed')),
      db.select({ count: count() }).from(contactInquiries).where(eq(contactInquiries.status, 'admission_confirmed'))
    ]);

    return c.json({
      success: true,
      data: {
        total: totalLeads[0].count,
        new: newLeads[0].count,
        open: openLeads[0].count,
        closed: closedLeads[0].count,
        converted: convertedLeads[0].count,
        conversionRate: totalLeads[0].count > 0 ? (convertedLeads[0].count / totalLeads[0].count * 100).toFixed(2) : 0,
      },
    });
  } catch (error) {
    console.error('Get lead stats error:', error);
    return c.json({ error: 'Failed to fetch lead statistics' }, 500);
  }
});

export { leadsRoutes };
