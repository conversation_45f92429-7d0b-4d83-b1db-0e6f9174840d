import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { jwtVerify } from 'jose';
import { db } from '@/lib/db';
import { reviews, colleges, studentUsers, courses, adminUsers, adminActivityLogs } from '@/lib/db/schema';
import { eq, desc, count, and, like, sql } from 'drizzle-orm';

// Define context variables type
type Variables = {
  admin: any;
  adminUser: any;
};

const reviewsRoutes = new Hono<{ Variables: Variables }>();

// JWT Secret
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');

// Auth middleware
const verifyAdminAuth = async (c: any, next: any) => {
  try {
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const token = authHeader.substring(7);
    const { payload } = await jwtVerify(token, JWT_SECRET);

    const [admin] = await db
      .select()
      .from(adminUsers)
      .where(eq(adminUsers.id, payload.id as string))
      .limit(1);

    if (!admin || !admin.isActive) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    c.set('admin', admin);
    await next();
  } catch (error) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
};

// Permission check middleware
function requirePermission(permission: string) {
  return async (c: any, next: any) => {
    const admin = c.get('admin') as any;

    // Super admin has all permissions
    if (admin.role === 'super_admin') {
      await next();
      return;
    }

    // Check role-based permissions
    const rolePermissions: Record<string, string[]> = {
      'content_manager': ['manage_reviews', 'view_reviews'],
      'review_moderator': ['manage_reviews', 'view_reviews'],
      'lead_manager': ['view_reviews'],
      'finance_officer': ['view_reviews'],
      'seo_specialist': ['view_reviews'],
    };

    const userPermissions = rolePermissions[admin.role] || [];
    if (!userPermissions.includes(permission)) {
      return c.json({ error: 'Insufficient permissions' }, 403);
    }

    await next();
  };
}

// Get all reviews with enhanced data
reviewsRoutes.get('/', verifyAdminAuth, requirePermission('view_reviews'), async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];

    if (status) {
      conditions.push(eq(reviews.status, status));
    }

    if (search) {
      conditions.push(
        sql`(${reviews.title} ILIKE ${`%${search}%`} OR ${reviews.review} ILIKE ${`%${search}%`})`
      );
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get reviews with joined data
    const [reviewsList, totalCount] = await Promise.all([
      db
        .select({
          id: reviews.id,
          title: reviews.title,
          review: reviews.review,
          overallRating: reviews.overallRating,
          ratings: reviews.ratings,
          pros: reviews.pros,
          cons: reviews.cons,
          status: reviews.status,
          studentId: reviews.studentId,
          collegeId: reviews.collegeId,
          courseId: reviews.courseId,
          yearOfStudy: reviews.yearOfStudy,
          graduationYear: reviews.graduationYear,
          isAnonymous: reviews.isAnonymous,
          helpfulCount: reviews.helpfulCount,
          reportCount: reviews.reportCount,
          moderatedBy: reviews.moderatedBy,
          moderatedAt: reviews.moderatedAt,
          moderationNotes: reviews.moderationNotes,
          createdAt: reviews.createdAt,
          updatedAt: reviews.updatedAt,
          collegeName: colleges.name,
          studentName: studentUsers.name,
        })
        .from(reviews)
        .leftJoin(colleges, eq(reviews.collegeId, colleges.id))
        .leftJoin(studentUsers, eq(reviews.studentId, studentUsers.id))
        .where(whereClause)
        .orderBy(desc(reviews.createdAt))
        .limit(limit)
        .offset(offset),

      db
        .select({ count: count() })
        .from(reviews)
        .where(whereClause)
    ]);

    const totalPages = Math.ceil(totalCount[0].count / limit);

    return c.json({
      success: true,
      data: {
        reviews: reviewsList,
        total: totalCount[0].count,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Get reviews error:', error);
    return c.json({ error: 'Failed to fetch reviews' }, 500);
  }
});

// Moderate review
reviewsRoutes.post('/:id/moderate', verifyAdminAuth, requirePermission('manage_reviews'), zValidator('json', z.object({
  action: z.enum(['approve', 'reject']),
  notes: z.string().optional(),
})), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const reviewId = c.req.param('id');
    const { action, notes } = c.req.valid('json');

    const [updatedReview] = await db
      .update(reviews)
      .set({
        status: action === 'approve' ? 'approved' : 'rejected',
        moderatedBy: admin.id,
        moderatedAt: new Date(),
        moderationNotes: notes,
        updatedAt: new Date(),
      })
      .where(eq(reviews.id, reviewId))
      .returning();

    if (!updatedReview) {
      return c.json({ error: 'Review not found' }, 404);
    }

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: `${action}_review`,
      resource: 'reviews',
      resourceId: reviewId,
      details: { action, notes },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
    });

    return c.json({
      success: true,
      data: updatedReview,
    });
  } catch (error) {
    console.error('Moderate review error:', error);
    return c.json({ error: 'Failed to moderate review' }, 500);
  }
});

// Delete review
reviewsRoutes.delete('/:id', verifyAdminAuth, requirePermission('manage_reviews'), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const reviewId = c.req.param('id');

    const [deletedReview] = await db
      .delete(reviews)
      .where(eq(reviews.id, reviewId))
      .returning();

    if (!deletedReview) {
      return c.json({ error: 'Review not found' }, 404);
    }

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'delete_review',
      resource: 'reviews',
      resourceId: reviewId,
      details: { title: deletedReview.title },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
    });

    return c.json({
      success: true,
      message: 'Review deleted successfully',
    });
  } catch (error) {
    console.error('Delete review error:', error);
    return c.json({ error: 'Failed to delete review' }, 500);
  }
});

export { reviewsRoutes };
