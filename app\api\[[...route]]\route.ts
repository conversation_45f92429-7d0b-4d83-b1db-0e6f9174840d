import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { authRoutes } from './auth';
import { adminRoutes } from './admin';
import { collegesRoutes } from './colleges';
import { coursesRoutes } from './courses';
import { reviewsRoutes } from './reviews';
import { scholarshipsRoutes } from './scholarships';
import { leadsRoutes } from './leads';
import { articlesRoutes } from './articles';
import { admissionsRoutes } from './admissions';
import { settingsRoutes } from './settings';

// Create the main Hono app
const app = new Hono().basePath('/api');

// Add middleware
app.use('*', cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://collegecampus.com', 'https://www.collegecampus.com']
    : ['http://localhost:3000'],
  credentials: true,
}));

app.use('*', logger());
app.use('*', prettyJSON());

// Health check endpoint
app.get('/health', (c) => {
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API Routes
app.route('/auth', authRoutes);
app.route('/admin', adminRoutes);
app.route('/admin/colleges', collegesRoutes);
app.route('/admin/courses', coursesRoutes);
app.route('/admin/reviews', reviewsRoutes);
app.route('/admin/scholarships', scholarshipsRoutes);
app.route('/admin/leads', leadsRoutes);
app.route('/admin/articles', articlesRoutes);
app.route('/admin/admissions', admissionsRoutes);
app.route('/admin/settings', settingsRoutes);
app.route('/colleges', collegesRoutes);
app.route('/courses', coursesRoutes);
app.route('/reviews', reviewsRoutes);
app.route('/scholarships', scholarshipsRoutes);
app.route('/leads', leadsRoutes);
app.route('/articles', articlesRoutes);
app.route('/settings', settingsRoutes);

// 404 handler
app.notFound((c) => {
  return c.json({ error: 'Not Found', message: 'The requested endpoint does not exist' }, 404);
});

// Error handler
app.onError((err, c) => {
  console.error('API Error:', err);

  if (err.message.includes('Unauthorized')) {
    return c.json({ error: 'Unauthorized', message: 'Authentication required' }, 401);
  }

  if (err.message.includes('Forbidden')) {
    return c.json({ error: 'Forbidden', message: 'Insufficient permissions' }, 403);
  }

  if (err.message.includes('Validation')) {
    return c.json({ error: 'Validation Error', message: err.message }, 400);
  }

  return c.json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'production'
      ? 'Something went wrong'
      : err.message
  }, 500);
});

export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const DELETE = handle(app);
export const PATCH = handle(app);