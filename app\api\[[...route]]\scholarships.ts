import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { jwtVerify } from 'jose';
import { db } from '@/lib/db';
import { scholarshipApplications, adminUsers, adminActivityLogs } from '@/lib/db/schema';
import { eq, desc, count, and, sql } from 'drizzle-orm';

// Define context variables type
type Variables = {
  admin: any;
  adminUser: any;
};

const scholarshipsRoutes = new Hono<{ Variables: Variables }>();

// JWT Secret
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');

// Auth middleware
const verifyAdminAuth = async (c: any, next: any) => {
  try {
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const token = authHeader.substring(7);
    const { payload } = await jwtVerify(token, JWT_SECRET);

    const [admin] = await db
      .select()
      .from(adminUsers)
      .where(eq(adminUsers.id, payload.id as string))
      .limit(1);

    if (!admin || !admin.isActive) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    c.set('admin', admin);
    await next();
  } catch (error) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
};

// Permission check middleware
function requirePermission(permission: string) {
  return async (c: any, next: any) => {
    const admin = c.get('admin') as any;

    // Super admin has all permissions
    if (admin.role === 'super_admin') {
      await next();
      return;
    }

    // Check role-based permissions
    const rolePermissions: Record<string, string[]> = {
      'content_manager': ['view_scholarships'],
      'review_moderator': ['view_scholarships'],
      'lead_manager': ['manage_scholarships', 'view_scholarships'],
      'finance_officer': ['manage_scholarships', 'view_scholarships'],
      'seo_specialist': ['view_scholarships'],
    };

    const userPermissions = rolePermissions[admin.role] || [];
    if (!userPermissions.includes(permission)) {
      return c.json({ error: 'Insufficient permissions' }, 403);
    }

    await next();
  };
}

// Get all scholarship applications
scholarshipsRoutes.get('/', verifyAdminAuth, requirePermission('view_scholarships'), async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];

    if (status) {
      conditions.push(eq(scholarshipApplications.status, status));
    }

    if (search) {
      conditions.push(
        sql`(${scholarshipApplications.name} ILIKE ${`%${search}%`} OR ${scholarshipApplications.email} ILIKE ${`%${search}%`})`
      );
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    const [applications, totalCount] = await Promise.all([
      db
        .select()
        .from(scholarshipApplications)
        .where(whereClause)
        .orderBy(desc(scholarshipApplications.createdAt))
        .limit(limit)
        .offset(offset),

      db
        .select({ count: count() })
        .from(scholarshipApplications)
        .where(whereClause)
    ]);

    const totalPages = Math.ceil(totalCount[0].count / limit);

    return c.json({
      success: true,
      data: {
        applications,
        total: totalCount[0].count,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Get scholarship applications error:', error);
    return c.json({ error: 'Failed to fetch scholarship applications' }, 500);
  }
});

// Update scholarship application status
scholarshipsRoutes.patch('/:id', verifyAdminAuth, requirePermission('manage_scholarships'), zValidator('json', z.object({
  status: z.enum(['pending', 'approved', 'rejected', 'disbursed']),
  notes: z.string().optional(),
  disbursementAmount: z.number().optional(),
  disbursementDate: z.string().optional(),
})), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const applicationId = c.req.param('id');
    const updateData = c.req.valid('json');

    // Prepare the update data with proper types
    const finalUpdateData: any = {
      ...updateData,
      updatedAt: new Date(),
    };

    // Convert disbursementDate string to Date if present
    if (finalUpdateData.disbursementDate && typeof finalUpdateData.disbursementDate === 'string') {
      finalUpdateData.disbursementDate = new Date(finalUpdateData.disbursementDate);
    }

    const [updatedApplication] = await db
      .update(scholarshipApplications)
      .set(finalUpdateData)
      .where(eq(scholarshipApplications.id, applicationId))
      .returning();

    if (!updatedApplication) {
      return c.json({ error: 'Scholarship application not found' }, 404);
    }

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'update_scholarship',
      resource: 'scholarships',
      resourceId: applicationId,
      details: { status: updateData.status, notes: updateData.notes },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
    });

    return c.json({
      success: true,
      data: updatedApplication,
    });
  } catch (error) {
    console.error('Update scholarship application error:', error);
    return c.json({ error: 'Failed to update scholarship application' }, 500);
  }
});

// Get scholarship statistics
scholarshipsRoutes.get('/stats', verifyAdminAuth, requirePermission('view_scholarships'), async (c) => {
  try {
    const [
      totalApplications,
      pendingApplications,
      approvedApplications,
      rejectedApplications,
      disbursedApplications,
      totalDisbursed
    ] = await Promise.all([
      db.select({ count: count() }).from(scholarshipApplications),
      db.select({ count: count() }).from(scholarshipApplications).where(eq(scholarshipApplications.status, 'pending')),
      db.select({ count: count() }).from(scholarshipApplications).where(eq(scholarshipApplications.status, 'approved')),
      db.select({ count: count() }).from(scholarshipApplications).where(eq(scholarshipApplications.status, 'rejected')),
      db.select({ count: count() }).from(scholarshipApplications).where(eq(scholarshipApplications.status, 'disbursed')),
      db.select({
        total: sql<number>`COALESCE(SUM(${scholarshipApplications.scholarshipAmount}), 0)`
      }).from(scholarshipApplications).where(eq(scholarshipApplications.status, 'disbursed'))
    ]);

    return c.json({
      success: true,
      data: {
        total: totalApplications[0].count,
        pending: pendingApplications[0].count,
        approved: approvedApplications[0].count,
        rejected: rejectedApplications[0].count,
        disbursed: disbursedApplications[0].count,
        totalDisbursedAmount: totalDisbursed[0].total,
      },
    });
  } catch (error) {
    console.error('Get scholarship stats error:', error);
    return c.json({ error: 'Failed to fetch scholarship statistics' }, 500);
  }
});

export { scholarshipsRoutes };
