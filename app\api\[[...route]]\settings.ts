import { Hono } from 'hono';
import { db } from '@/lib/db';
import { websiteSettings } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

const settingsRoutes = new Hono();

// Get all settings
settingsRoutes.get('/', async (c) => {
  try {
    const settings = await db.select().from(websiteSettings);

    return c.json({
      success: true,
      data: settings,
    });
  } catch (error) {
    console.error('Get settings error:', error);
    return c.json({ error: 'Failed to fetch settings' }, 500);
  }
});

// Get setting by key
settingsRoutes.get('/:key', async (c) => {
  try {
    const key = c.req.param('key');
    
    const [setting] = await db
      .select()
      .from(websiteSettings)
      .where(eq(websiteSettings.key, key))
      .limit(1);

    if (!setting) {
      return c.json({ error: 'Setting not found' }, 404);
    }

    return c.json({
      success: true,
      data: setting,
    });
  } catch (error) {
    console.error('Get setting error:', error);
    return c.json({ error: 'Failed to fetch setting' }, 500);
  }
});

export { settingsRoutes };
