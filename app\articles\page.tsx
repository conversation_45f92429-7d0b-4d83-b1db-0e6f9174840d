import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Education Articles & Guides | CollegeCampus Blog',
  description: 'Read the latest articles, guides, and tips about college admissions, career guidance, study abroad, and educational trends.',
  keywords: 'education articles, college guides, career tips, study abroad, admission tips, educational blog',
};

export default function ArticlesPage() {
  const featuredArticles = [
    {
      id: 1,
      title: 'Complete Guide to Engineering Entrance Exams 2024',
      excerpt: 'Everything you need to know about JEE Main, JEE Advanced, and other engineering entrance exams.',
      category: 'Entrance Exams',
      readTime: '8 min read',
      publishDate: '2024-01-15',
      image: '/placeholder-article1.jpg',
    },
    {
      id: 2,
      title: 'Top 10 Career Options After 12th Science',
      excerpt: 'Explore diverse career paths available for science students beyond traditional engineering and medical fields.',
      category: 'Career Guidance',
      readTime: '6 min read',
      publishDate: '2024-01-12',
      image: '/placeholder-article2.jpg',
    },
    {
      id: 3,
      title: 'How to Choose the Right College: A Step-by-Step Guide',
      excerpt: 'Factors to consider when selecting a college that aligns with your goals and preferences.',
      category: 'College Selection',
      readTime: '10 min read',
      publishDate: '2024-01-10',
      image: '/placeholder-article3.jpg',
    },
  ];

  const categories = [
    { name: 'Entrance Exams', count: 25, color: 'bg-blue-100 text-blue-800' },
    { name: 'Career Guidance', count: 18, color: 'bg-green-100 text-green-800' },
    { name: 'College Selection', count: 22, color: 'bg-purple-100 text-purple-800' },
    { name: 'Study Abroad', count: 15, color: 'bg-orange-100 text-orange-800' },
    { name: 'Scholarships', count: 12, color: 'bg-pink-100 text-pink-800' },
    { name: 'Campus Life', count: 20, color: 'bg-indigo-100 text-indigo-800' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Education Articles & Guides</h1>
            <p className="text-xl text-indigo-100 max-w-3xl mx-auto">
              Stay informed with the latest insights, tips, and guides to help you navigate your educational journey.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Categories</h3>
              <div className="space-y-3">
                {categories.map((category) => (
                  <button
                    key={category.name}
                    className="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                  >
                    <span className="font-medium text-gray-900">{category.name}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${category.color}`}>
                      {category.count}
                    </span>
                  </button>
                ))}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Newsletter</h3>
              <p className="text-gray-600 text-sm mb-4">
                Get the latest education articles and guides delivered to your inbox.
              </p>
              <div className="space-y-3">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
                <button className="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-md font-medium transition-colors duration-200">
                  Subscribe
                </button>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Featured Articles */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Featured Articles</h2>
              <div className="space-y-8">
                {featuredArticles.map((article) => (
                  <article key={article.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
                    <div className="md:flex">
                      <div className="md:w-1/3">
                        <div className="h-48 md:h-full bg-gradient-to-br from-indigo-400 to-purple-500"></div>
                      </div>
                      <div className="md:w-2/3 p-6">
                        <div className="flex items-center space-x-2 mb-3">
                          <span className="bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full text-xs font-medium">
                            {article.category}
                          </span>
                          <span className="text-gray-500 text-sm">{article.readTime}</span>
                          <span className="text-gray-500 text-sm">•</span>
                          <span className="text-gray-500 text-sm">{article.publishDate}</span>
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-3 hover:text-indigo-600 cursor-pointer">
                          {article.title}
                        </h3>
                        <p className="text-gray-600 mb-4">{article.excerpt}</p>
                        <button className="text-indigo-600 hover:text-indigo-700 font-medium">
                          Read More →
                        </button>
                      </div>
                    </div>
                  </article>
                ))}
              </div>
            </div>

            {/* Coming Soon Message */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
              <div className="w-20 h-20 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-3xl">📚</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">More Articles Coming Soon!</h3>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                We&apos;re working on creating comprehensive guides and articles to help you with every aspect of your educational journey.
                Stay tuned for expert insights on college admissions, career planning, and much more.
              </p>
              <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                <button className="w-full sm:w-auto bg-indigo-600 hover:bg-indigo-700 text-white py-3 px-6 rounded-lg font-medium transition-colors duration-200">
                  Subscribe for Updates
                </button>
                <button className="w-full sm:w-auto bg-gray-100 hover:bg-gray-200 text-gray-900 py-3 px-6 rounded-lg font-medium transition-colors duration-200">
                  Suggest a Topic
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Popular Topics */}
      <div className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Popular Topics</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
            {[
              'JEE Preparation',
              'NEET Guide',
              'MBA Admissions',
              'Study Abroad',
              'Scholarship Tips',
              'Career Planning',
              'College Life',
              'Placement Prep',
              'Entrance Exams',
              'Course Selection',
              'Education Loans',
              'Campus Reviews',
            ].map((topic) => (
              <button
                key={topic}
                className="bg-gray-50 hover:bg-gray-100 rounded-lg p-4 text-center transition-colors duration-200 border border-gray-200"
              >
                <span className="font-medium text-gray-900 text-sm">{topic}</span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
