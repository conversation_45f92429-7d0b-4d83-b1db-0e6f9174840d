import { Metadata } from 'next';
import { CollegeDetailPage } from '@/components/colleges/CollegeDetailPage';

interface Props {
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  // In real app, fetch college data based on ID
  const { id } = await params;

  return {
    title: `College Details | CollegeCampus`,
    description: `Detailed information about college including courses, fees, placements, reviews, and admission process.`,
    keywords: 'college details, admission, courses, fees, placements, reviews',
  };
}

export default async function CollegeDetailPageRoute({ params }: Props) {
  const { id: collegeId } = await params;
  return <CollegeDetailPage collegeId={collegeId} />;
}
