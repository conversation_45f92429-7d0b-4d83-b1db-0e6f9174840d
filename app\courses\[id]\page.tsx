import { Metadata } from 'next';
import { CourseDetailPage } from '@/components/courses/CourseDetailPage';

interface Props {
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  // In real app, fetch course data based on ID
  const { id: courseId } = await params;

  return {
    title: `Course Details | CollegeCampus`,
    description: `Comprehensive information about the course including syllabus, eligibility, career prospects, and colleges offering this course.`,
    keywords: 'course details, syllabus, eligibility, career prospects, colleges',
  };
}

export default async function CourseDetailPageRoute({ params }: Props) {
  const { id: courseId } = await params;
  return <CourseDetailPage courseId={courseId} />;
}
