import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { QueryProviders } from "@/providers/query-provider";
import { ConditionalLayout } from "@/components/layout/ConditionalLayout";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "CollegeCampus - Find Your Perfect College",
  description: "Discover and compare colleges across India. Get detailed information about courses, fees, placements, reviews, and scholarships. Make informed decisions about your higher education.",
  keywords: "colleges, universities, courses, education, India, admissions, scholarships, placements, reviews",
  authors: [{ name: "CollegeCampus Team" }],
  creator: "CollegeCampus",
  publisher: "CollegeCampus",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || 'https://collegecampus.com'),
  openGraph: {
    type: "website",
    locale: "en_IN",
    url: "/",
    title: "CollegeCampus - Find Your Perfect College",
    description: "Discover and compare colleges across India. Get detailed information about courses, fees, placements, reviews, and scholarships.",
    siteName: "CollegeCampus",
  },
  twitter: {
    card: "summary_large_image",
    title: "CollegeCampus - Find Your Perfect College",
    description: "Discover and compare colleges across India. Get detailed information about courses, fees, placements, reviews, and scholarships.",
    creator: "@collegecampus",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} antialiased`}>
        <QueryProviders>
          <ConditionalLayout>
            {children}
          </ConditionalLayout>
        </QueryProviders>
      </body>
    </html>
  );
}
