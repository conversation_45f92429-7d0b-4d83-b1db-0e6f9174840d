import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'College Reviews | Student Experiences & Ratings | CollegeCampus',
  description: 'Read authentic reviews from students about colleges across India. Get insights into campus life, academics, placements, and facilities.',
  keywords: 'college reviews, student reviews, college ratings, campus life, college experiences',
};

export default function ReviewsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">College Reviews</h1>
            <p className="text-xl text-purple-100 max-w-3xl mx-auto">
              Read authentic reviews from students and alumni to make informed decisions about your college choice.
            </p>
          </div>
        </div>
      </div>

      {/* Coming Soon Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <div className="w-32 h-32 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-8">
            <span className="text-6xl">⭐</span>
          </div>

          <h2 className="text-3xl font-bold text-gray-900 mb-4">Reviews Coming Soon!</h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            We&apos;re building a comprehensive review system where students can share their authentic experiences about colleges, courses, and campus life.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="text-3xl mb-4">📝</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Authentic Reviews</h3>
              <p className="text-gray-600">Verified student reviews with detailed insights about college life</p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="text-3xl mb-4">📊</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Detailed Ratings</h3>
              <p className="text-gray-600">Category-wise ratings for academics, infrastructure, placements, and more</p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="text-3xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Smart Filtering</h3>
              <p className="text-gray-600">Filter reviews by course, year, rating, and other criteria</p>
            </div>
          </div>

          <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <button className="w-full sm:w-auto bg-purple-600 hover:bg-purple-700 text-white py-3 px-8 rounded-lg font-medium transition-colors duration-200">
              Get Notified When Available
            </button>
            <button className="w-full sm:w-auto bg-gray-100 hover:bg-gray-200 text-gray-900 py-3 px-8 rounded-lg font-medium transition-colors duration-200">
              Explore Colleges Instead
            </button>
          </div>
        </div>
      </div>

      {/* Preview Features */}
      <div className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">What to Expect</h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Review Categories</h3>
              <div className="space-y-4">
                {[
                  { category: 'Academics', description: 'Faculty quality, curriculum, and learning experience' },
                  { category: 'Infrastructure', description: 'Campus facilities, labs, library, and hostels' },
                  { category: 'Placements', description: 'Job opportunities, salary packages, and career support' },
                  { category: 'Campus Life', description: 'Social activities, clubs, events, and student community' },
                  { category: 'Value for Money', description: 'Fee structure vs. quality of education and facilities' },
                ].map((item) => (
                  <div key={item.category} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{item.category}</h4>
                      <p className="text-gray-600 text-sm">{item.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Review Features</h3>
              <div className="space-y-4">
                {[
                  'Verified student status',
                  'Anonymous review option',
                  'Photo and video uploads',
                  'Helpful/unhelpful voting',
                  'Response from colleges',
                  'Moderated content',
                ].map((feature) => (
                  <div key={feature} className="flex items-center space-x-3">
                    <span className="text-green-500">✓</span>
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
