import { Metadata } from 'next';
import { Suspense } from 'react';
import { SearchResultsPage } from '@/components/search/SearchResultsPage';

export const metadata: Metadata = {
  title: 'Search Results | CollegeCampus',
  description: 'Search results for colleges, courses, and educational content.',
};

function SearchPageContent() {
  return <SearchResultsPage />;
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    }>
      <SearchPageContent />
    </Suspense>
  );
}
