const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env' });

const sql = neon(process.env.DATABASE_URL);

async function checkCourseSchema() {
  try {
    console.log('🔍 Checking course table schema...');
    
    // Get table structure
    const columns = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'courses' 
      ORDER BY ordinal_position
    `;
    
    console.log('📋 Course table columns:');
    columns.forEach(col => {
      console.log(`  ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    // Get a sample course to see actual data structure
    const [sampleCourse] = await sql`
      SELECT * FROM courses LIMIT 1
    `;
    
    if (sampleCourse) {
      console.log('\n📚 Sample course data:');
      console.log(JSON.stringify(sampleCourse, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Error checking schema:', error);
  }
}

checkCourseSchema();
