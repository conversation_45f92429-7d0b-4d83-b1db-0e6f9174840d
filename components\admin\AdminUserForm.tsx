'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';

interface AdminUserFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: string;
  isActive: boolean;
}

interface AdminUserFormProps {
  adminUser?: any;
  isEditing?: boolean;
}

export function AdminUserForm({ adminUser, isEditing = false }: AdminUserFormProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">
            {isEditing ? 'Edit Admin User' : 'Add New Admin User'}
          </h1>
          <p className="mt-1 text-sm text-gray-600">
            Loading...
          </p>
        </div>
      </div>
    );
  }

  return <AdminUserFormContent adminUser={adminUser} isEditing={isEditing} />;
}

function AdminUserFormContent({ adminUser, isEditing = false }: AdminUserFormProps) {
  const router = useRouter();
  const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<AdminUserFormData>({
    defaultValues: {
      name: adminUser?.name || '',
      email: adminUser?.email || '',
      password: '',
      confirmPassword: '',
      role: adminUser?.role || 'content_manager',
      isActive: adminUser?.isActive ?? true,
    },
  });

  const createMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create admin user');
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success('Admin user created successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const updateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      const response = await fetch(`/api/admin/users/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update admin user');
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success('Admin user updated successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const onSubmit = async (data: AdminUserFormData) => {
    try {
      // Validate password confirmation
      if (!isEditing && data.password !== data.confirmPassword) {
        toast.error('Passwords do not match');
        return;
      }

      // Prepare the payload
      const payload = {
        name: data.name,
        email: data.email,
        role: data.role,
        isActive: data.isActive,
        ...((!isEditing || data.password) && { password: data.password }),
      };

      if (isEditing && adminUser) {
        await updateMutation.mutateAsync({ id: adminUser.id, data: payload });
      } else {
        await createMutation.mutateAsync(payload);
      }

      router.push('/admin/users');
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const roleDescriptions = {
    super_admin: 'Full access to all features and settings',
    content_manager: 'Manage colleges, courses, articles, and SEO',
    review_moderator: 'Moderate reviews and manage content approval',
    lead_manager: 'Manage leads, scholarships, and student inquiries',
    finance_officer: 'Manage admissions, financials, and scholarships',
    seo_specialist: 'Manage SEO settings and analytics',
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">
          {isEditing ? 'Edit Admin User' : 'Add New Admin User'}
        </h1>
        <p className="mt-1 text-sm text-gray-600">
          {isEditing ? 'Update admin user information' : 'Create a new admin user account'}
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Full Name *
              </label>
              <Input
                {...register('name', { required: 'Name is required' })}
                placeholder="Enter full name"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address *
              </label>
              <Input
                type="email"
                {...register('email', { 
                  required: 'Email is required',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Invalid email address'
                  }
                })}
                placeholder="Enter email address"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password {!isEditing && '*'}
              </label>
              <Input
                type="password"
                {...register('password', { 
                  required: !isEditing ? 'Password is required' : false,
                  minLength: {
                    value: 8,
                    message: 'Password must be at least 8 characters'
                  }
                })}
                placeholder={isEditing ? 'Leave blank to keep current password' : 'Enter password'}
              />
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>

            {!isEditing && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Confirm Password *
                </label>
                <Input
                  type="password"
                  {...register('confirmPassword', { 
                    required: 'Please confirm password'
                  })}
                  placeholder="Confirm password"
                />
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm text-red-600">{errors.confirmPassword.message}</p>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Role & Permissions</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Role *
              </label>
              <Select value={watch('role')} onValueChange={(value) => setValue('role', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="content_manager">Content Manager</SelectItem>
                  <SelectItem value="review_moderator">Review Moderator</SelectItem>
                  <SelectItem value="lead_manager">Lead Manager</SelectItem>
                  <SelectItem value="finance_officer">Finance Officer</SelectItem>
                  <SelectItem value="seo_specialist">SEO Specialist</SelectItem>
                  <SelectItem value="super_admin">Super Admin</SelectItem>
                </SelectContent>
              </Select>
              <p className="mt-1 text-sm text-gray-500">
                {roleDescriptions[watch('role') as keyof typeof roleDescriptions]}
              </p>
            </div>

            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div>
                <h3 className="text-sm font-medium text-gray-900">Active Status</h3>
                <p className="text-sm text-gray-600">Allow this user to access the admin panel</p>
              </div>
              <Switch
                checked={watch('isActive')}
                onCheckedChange={(checked) => setValue('isActive', checked)}
              />
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/admin/users')}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {createMutation.isPending || updateMutation.isPending
              ? 'Saving...'
              : isEditing
              ? 'Update User'
              : 'Create User'}
          </Button>
        </div>
      </form>
    </div>
  );
}
