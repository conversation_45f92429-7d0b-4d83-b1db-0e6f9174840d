'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { College, useCreateCollege, useUpdateCollege } from '@/features/api/use-colleges';

interface CollegeFormProps {
  college?: College;
  isEditing?: boolean;
}

interface CollegeFormData {
  name: string;
  shortName: string;
  description: string;
  about: string;
  vision: string;
  mission: string;
  establishedYear: number;
  collegeType: string;
  affiliation: string;
  logo: string;
  nirfRanking: number;
  totalStudents: number;
  facultyCount: number;
  campusArea: string;
  isVerified: boolean;
  isFeatured: boolean;
  isPublished: boolean;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string;
  // Location fields
  address: string;
  city: string;
  state: string;
  country: string;
  pincode: string;
  latitude: string;
  longitude: string;
  // Contact fields
  phone: string;
  email: string;
  website: string;
  // Accreditations & Approvals
  accreditations: string[];
  approvals: string[];
  // Rankings
  nirfRankingYear: number;
  nirfCategory: string;
  otherRankings: string;
  // Infrastructure & Facilities
  hasHostel: boolean;
  hasLibrary: boolean;
  hasGym: boolean;
  hasSportsComplex: boolean;
  hasMedicalCenter: boolean;
  hasCafeteria: boolean;
  hasWifi: boolean;
  hasTransport: boolean;
  hasPlacementCell: boolean;
  hostelCapacity: number;
  libraryBooks: number;
  // Academic Details
  facultyStudentRatio: string;
  phdFacultyPercentage: number;
  // Placement Details
  placementPercentage: number;
  averagePackage: number;
  highestPackage: number;
  medianPackage: number;
  topRecruiters: string[];
  // Admission Details
  applicationStartDate: string;
  applicationEndDate: string;
  entranceExams: string[];
  admissionProcess: string;
  // Gallery & Media
  bannerImages: string[];
  galleryImages: string[];
  virtualTourUrl: string;
}

export function CollegeForm({ college, isEditing = false }: CollegeFormProps) {
  const router = useRouter();
  const createMutation = useCreateCollege();
  const updateMutation = useUpdateCollege();
  const [activeTab, setActiveTab] = useState('basic');

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
  } = useForm<CollegeFormData>({
    defaultValues: {
      name: '',
      shortName: '',
      description: '',
      about: '',
      vision: '',
      mission: '',
      establishedYear: new Date().getFullYear(),
      collegeType: 'Private',
      affiliation: '',
      logo: '',
      nirfRanking: 0,
      totalStudents: 0,
      facultyCount: 0,
      campusArea: '',
      isVerified: false,
      isFeatured: false,
      isPublished: false,
      seoTitle: '',
      seoDescription: '',
      seoKeywords: '',
      address: '',
      city: '',
      state: '',
      country: 'India',
      pincode: '',
      latitude: '',
      longitude: '',
      phone: '',
      email: '',
      website: '',
      accreditations: [],
      approvals: [],
      nirfRankingYear: new Date().getFullYear(),
      nirfCategory: '',
      otherRankings: '',
      hasHostel: false,
      hasLibrary: false,
      hasGym: false,
      hasSportsComplex: false,
      hasMedicalCenter: false,
      hasCafeteria: false,
      hasWifi: false,
      hasTransport: false,
      hasPlacementCell: false,
      hostelCapacity: 0,
      libraryBooks: 0,
      facultyStudentRatio: '',
      phdFacultyPercentage: 0,
      placementPercentage: 0,
      averagePackage: 0,
      highestPackage: 0,
      medianPackage: 0,
      topRecruiters: [],
      applicationStartDate: '',
      applicationEndDate: '',
      entranceExams: [],
      admissionProcess: '',
      bannerImages: [],
      galleryImages: [],
      virtualTourUrl: '',
    },
  });

  // Populate form with existing data when editing
  useEffect(() => {
    if (college && isEditing) {
      setValue('name', college.name);
      setValue('shortName', college.shortName || '');
      setValue('description', college.description || '');
      setValue('about', college.about || '');
      setValue('vision', college.vision || '');
      setValue('mission', college.mission || '');
      setValue('establishedYear', college.establishedYear || new Date().getFullYear());
      setValue('collegeType', college.collegeType || 'Private');
      setValue('affiliation', college.affiliation || '');
      setValue('logo', college.logo || '');
      setValue('nirfRanking', college.nirfRanking || 0);
      setValue('totalStudents', college.totalStudents || 0);
      setValue('facultyCount', college.facultyCount || 0);
      setValue('campusArea', college.campusArea || '');
      setValue('isVerified', college.isVerified);
      setValue('isFeatured', college.isFeatured);
      setValue('isPublished', college.isPublished);
      setValue('seoTitle', college.seoTitle || '');
      setValue('seoDescription', college.seoDescription || '');
      setValue('seoKeywords', college.seoKeywords || '');

      // Location data
      if (college.location) {
        setValue('address', college.location.address || '');
        setValue('city', college.location.city || '');
        setValue('state', college.location.state || '');
        setValue('country', college.location.country || 'India');
        setValue('pincode', college.location.pincode || '');
      }

      // Contact data
      if (college.contactInfo) {
        setValue('phone', college.contactInfo.phone || '');
        setValue('email', college.contactInfo.email || '');
        setValue('website', college.contactInfo.website || '');
      }
    }
  }, [college, isEditing, setValue]);

  const onSubmit = async (data: CollegeFormData) => {
    try {
      // Helper function to convert textarea strings to arrays
      const textareaToArray = (text: string) => {
        return text ? text.split('\n').filter(item => item.trim() !== '') : [];
      };

      // Prepare the payload to match the validation schema
      const payload = {
        name: data.name,
        shortName: data.shortName || undefined,
        description: data.description || undefined,
        about: data.about || undefined,
        vision: data.vision || undefined,
        mission: data.mission || undefined,
        establishedYear: data.establishedYear || undefined,
        collegeType: data.collegeType || undefined,
        affiliation: data.affiliation || undefined,
        logo: data.logo || undefined,
        nirfRanking: data.nirfRanking || undefined,
        totalStudents: data.totalStudents || undefined,
        facultyCount: data.facultyCount || undefined,
        campusArea: data.campusArea || undefined,
        isVerified: data.isVerified || false,
        isFeatured: data.isFeatured || false,
        isPublished: data.isPublished || false,
        seoTitle: data.seoTitle || undefined,
        seoDescription: data.seoDescription || undefined,
        seoKeywords: data.seoKeywords || undefined,
        location: {
          address: data.address || '',
          city: data.city || '',
          state: data.state || '',
          country: data.country || 'India',
          pincode: data.pincode || '',
          ...(data.latitude && data.longitude && {
            coordinates: {
              lat: parseFloat(data.latitude) || 0,
              lng: parseFloat(data.longitude) || 0,
            }
          }),
        },
        contactInfo: {
          phone: data.phone || undefined,
          email: data.email && data.email.includes('@') ? data.email : undefined,
          website: data.website && data.website.startsWith('http') ? data.website : undefined,
        },
        accreditations: data.accreditations || [],
        approvals: data.approvals || [],
        bannerImages: textareaToArray(data.bannerImages as unknown as string),
      };

      console.log('🚀 Form data being sent:', JSON.stringify(payload, null, 2));

      if (isEditing && college) {
        await updateMutation.mutateAsync({ id: college.id, data: payload });
      } else {
        await createMutation.mutateAsync(payload);
      }

      router.push('/admin/colleges');
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const tabs = [
    { id: 'basic', name: 'Basic Info', icon: '📋' },
    { id: 'details', name: 'Details', icon: '📝' },
    { id: 'location', name: 'Location', icon: '📍' },
    { id: 'contact', name: 'Contact', icon: '📞' },
    { id: 'accreditation', name: 'Accreditation', icon: '🏆' },
    { id: 'facilities', name: 'Facilities', icon: '🏢' },
    { id: 'academics', name: 'Academics', icon: '🎓' },
    { id: 'placements', name: 'Placements', icon: '💼' },
    { id: 'admission', name: 'Admission', icon: '📝' },
    { id: 'gallery', name: 'Gallery', icon: '📸' },
    { id: 'seo', name: 'SEO', icon: '🔍' },
    { id: 'settings', name: 'Settings', icon: '⚙️' },
  ];

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">
          {isEditing ? 'Edit College' : 'Add New College'}
        </h1>
        <p className="mt-1 text-sm text-gray-600">
          {isEditing ? 'Update college information' : 'Create a new college listing'}
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                type="button"
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          {/* Basic Info Tab */}
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    College Name *
                  </label>
                  <input
                    type="text"
                    {...register('name', { required: 'College name is required' })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter college name"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Short Name
                  </label>
                  <input
                    type="text"
                    {...register('shortName')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., IIT Delhi"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Established Year
                  </label>
                  <input
                    type="number"
                    {...register('establishedYear', {
                      valueAsNumber: true,
                      min: { value: 1800, message: 'Year must be after 1800' },
                      max: { value: new Date().getFullYear(), message: 'Year cannot be in the future' }
                    })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 1961"
                  />
                  {errors.establishedYear && (
                    <p className="mt-1 text-sm text-red-600">{errors.establishedYear.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    College Type
                  </label>
                  <select
                    {...register('collegeType')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="Government">Government</option>
                    <option value="Private">Private</option>
                    <option value="Deemed">Deemed University</option>
                    <option value="Autonomous">Autonomous</option>
                    <option value="Central">Central University</option>
                    <option value="State">State University</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  {...register('description')}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Brief description of the college"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Logo URL
                </label>
                <input
                  type="url"
                  {...register('logo')}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://example.com/logo.png"
                />
              </div>
            </div>
          )}

          {/* Details Tab */}
          {activeTab === 'details' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  About
                </label>
                <textarea
                  {...register('about')}
                  rows={6}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Detailed information about the college"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Vision
                  </label>
                  <textarea
                    {...register('vision')}
                    rows={4}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="College vision statement"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mission
                  </label>
                  <textarea
                    {...register('mission')}
                    rows={4}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="College mission statement"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    NIRF Ranking
                  </label>
                  <input
                    type="number"
                    {...register('nirfRanking', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 25"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Total Students
                  </label>
                  <input
                    type="number"
                    {...register('totalStudents', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 5000"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Faculty Count
                  </label>
                  <input
                    type="number"
                    {...register('facultyCount', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 300"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Campus Area
                  </label>
                  <input
                    type="text"
                    {...register('campusArea')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 320 acres"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Affiliation
                  </label>
                  <input
                    type="text"
                    {...register('affiliation')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., University of Delhi"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Location Tab */}
          {activeTab === 'location' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Address
                </label>
                <textarea
                  {...register('address')}
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Complete address"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    City
                  </label>
                  <input
                    type="text"
                    {...register('city')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="City name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    State
                  </label>
                  <input
                    type="text"
                    {...register('state')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="State name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Country
                  </label>
                  <input
                    type="text"
                    {...register('country')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Country name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Pincode
                  </label>
                  <input
                    type="text"
                    {...register('pincode')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Postal code"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Contact Tab */}
          {activeTab === 'contact' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone
                  </label>
                  <input
                    type="tel"
                    {...register('phone')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="+91 12345 67890"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    {...register('email')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Website
                  </label>
                  <input
                    type="url"
                    {...register('website')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="https://www.college.edu"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Latitude
                  </label>
                  <input
                    type="text"
                    {...register('latitude')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 28.6139"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Longitude
                  </label>
                  <input
                    type="text"
                    {...register('longitude')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 77.2090"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Accreditation Tab */}
          {activeTab === 'accreditation' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    NIRF Ranking
                  </label>
                  <input
                    type="number"
                    {...register('nirfRanking', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 25"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    NIRF Ranking Year
                  </label>
                  <input
                    type="number"
                    {...register('nirfRankingYear', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 2024"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    NIRF Category
                  </label>
                  <select
                    {...register('nirfCategory')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select Category</option>
                    <option value="Overall">Overall</option>
                    <option value="Engineering">Engineering</option>
                    <option value="Management">Management</option>
                    <option value="Pharmacy">Pharmacy</option>
                    <option value="Medical">Medical</option>
                    <option value="Law">Law</option>
                    <option value="Architecture">Architecture</option>
                    <option value="University">University</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Other Rankings
                </label>
                <textarea
                  {...register('otherRankings')}
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., QS World Ranking: 150, Times Higher Education: 200"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Accreditations
                </label>
                <div className="space-y-2">
                  {['NAAC', 'NBA', 'AICTE', 'UGC', 'NCTE', 'BCI', 'MCI', 'DCI', 'COA', 'PCI'].map((accreditation) => (
                    <label key={accreditation} className="flex items-center">
                      <input
                        type="checkbox"
                        value={accreditation}
                        {...register('accreditations')}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-900">{accreditation}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Government Approvals
                </label>
                <div className="space-y-2">
                  {['UGC Approved', 'AICTE Approved', 'NCTE Approved', 'BCI Approved', 'MCI Approved', 'DCI Approved', 'COA Approved', 'PCI Approved'].map((approval) => (
                    <label key={approval} className="flex items-center">
                      <input
                        type="checkbox"
                        value={approval}
                        {...register('approvals')}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-900">{approval}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Facilities Tab */}
          {activeTab === 'facilities' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Basic Facilities</h3>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register('hasLibrary')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-900">Library</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register('hasWifi')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-900">Wi-Fi</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register('hasCafeteria')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-900">Cafeteria</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register('hasTransport')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-900">Transportation</span>
                  </label>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Accommodation & Health</h3>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register('hasHostel')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-900">Hostel</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register('hasMedicalCenter')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-900">Medical Center</span>
                  </label>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Sports & Recreation</h3>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register('hasGym')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-900">Gymnasium</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register('hasSportsComplex')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-900">Sports Complex</span>
                  </label>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Academic Support</h3>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register('hasPlacementCell')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-900">Placement Cell</span>
                  </label>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hostel Capacity
                  </label>
                  <input
                    type="number"
                    {...register('hostelCapacity', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 2000"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Library Books
                  </label>
                  <input
                    type="number"
                    {...register('libraryBooks', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 50000"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Academics Tab */}
          {activeTab === 'academics' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Faculty Student Ratio
                  </label>
                  <input
                    type="text"
                    {...register('facultyStudentRatio')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 1:15"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    PhD Faculty Percentage
                  </label>
                  <input
                    type="number"
                    {...register('phdFacultyPercentage', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 85"
                    min="0"
                    max="100"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Placements Tab */}
          {activeTab === 'placements' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Placement Percentage
                  </label>
                  <input
                    type="number"
                    {...register('placementPercentage', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 95"
                    min="0"
                    max="100"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Average Package (LPA)
                  </label>
                  <input
                    type="number"
                    {...register('averagePackage', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 12"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Highest Package (LPA)
                  </label>
                  <input
                    type="number"
                    {...register('highestPackage', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 50"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Median Package (LPA)
                  </label>
                  <input
                    type="number"
                    {...register('medianPackage', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 10"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Top Recruiters (one per line)
                </label>
                <textarea
                  {...register('topRecruiters')}
                  rows={6}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Google&#10;Microsoft&#10;Amazon&#10;TCS&#10;Infosys"
                />
                <p className="mt-1 text-sm text-gray-500">Enter each recruiter on a new line</p>
              </div>
            </div>
          )}

          {/* Admission Tab */}
          {activeTab === 'admission' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Application Start Date
                  </label>
                  <input
                    type="date"
                    {...register('applicationStartDate')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Application End Date
                  </label>
                  <input
                    type="date"
                    {...register('applicationEndDate')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Entrance Exams Accepted (one per line)
                </label>
                <textarea
                  {...register('entranceExams')}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="JEE Main&#10;JEE Advanced&#10;BITSAT&#10;State CET"
                />
                <p className="mt-1 text-sm text-gray-500">Enter each exam on a new line</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Admission Process
                </label>
                <textarea
                  {...register('admissionProcess')}
                  rows={6}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Describe the admission process, eligibility criteria, selection process, etc."
                />
              </div>
            </div>
          )}

          {/* Gallery Tab */}
          {activeTab === 'gallery' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Banner Images (one URL per line)
                </label>
                <textarea
                  {...register('bannerImages')}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://example.com/banner1.jpg&#10;https://example.com/banner2.jpg"
                />
                <p className="mt-1 text-sm text-gray-500">Enter each image URL on a new line</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Gallery Images (one URL per line)
                </label>
                <textarea
                  {...register('galleryImages')}
                  rows={6}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://example.com/gallery1.jpg&#10;https://example.com/gallery2.jpg"
                />
                <p className="mt-1 text-sm text-gray-500">Enter each image URL on a new line</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Virtual Tour URL
                </label>
                <input
                  type="url"
                  {...register('virtualTourUrl')}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://example.com/virtual-tour"
                />
              </div>
            </div>
          )}

          {/* SEO Tab */}
          {activeTab === 'seo' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SEO Title
                </label>
                <input
                  type="text"
                  {...register('seoTitle')}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="SEO optimized title"
                />
                <p className="mt-1 text-sm text-gray-500">Recommended: 50-60 characters</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SEO Description
                </label>
                <textarea
                  {...register('seoDescription')}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="SEO meta description"
                />
                <p className="mt-1 text-sm text-gray-500">Recommended: 150-160 characters</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SEO Keywords
                </label>
                <textarea
                  {...register('seoKeywords')}
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="keyword1, keyword2, keyword3"
                />
                <p className="mt-1 text-sm text-gray-500">Separate keywords with commas</p>
              </div>
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === 'settings' && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('isPublished')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    Published
                  </label>
                  <p className="ml-2 text-sm text-gray-500">Make this college visible to public</p>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('isVerified')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    Verified
                  </label>
                  <p className="ml-2 text-sm text-gray-500">Mark as verified college</p>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('isFeatured')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    Featured
                  </label>
                  <p className="ml-2 text-sm text-gray-500">Show in featured colleges section</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting || createMutation.isPending || updateMutation.isPending}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSubmitting || createMutation.isPending || updateMutation.isPending
              ? 'Saving...'
              : isEditing
              ? 'Update College'
              : 'Create College'
            }
          </button>
        </div>
      </form>
    </div>
  );
}
