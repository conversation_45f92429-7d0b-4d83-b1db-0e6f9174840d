'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';

interface CourseFormData {
  name: string;
  shortName: string;
  level: string;
  duration: string;
  stream: string;
  description: string;
  detailedDescription: string;
  averageFees: number;
  averageSalary: number;
  isPopular: boolean;
  isPublished: boolean;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string;
  // Eligibility
  eligibilityMinPercentage: number;
  eligibilityQualification: string;
  eligibilityAgeLimit: string;
  eligibilityEntranceExams: string;
  // Syllabus
  coreSubjects: string;
  electiveSubjects: string;
  practicalComponents: string;
  projectWork: string;
  // Career Prospects
  jobRoles: string;
  industries: string;
  higherStudyOptions: string;
  skillsDeveloped: string;
}

interface CourseFormProps {
  course?: any;
  isEditing?: boolean;
}

export function CourseForm({ course, isEditing = false }: CourseFormProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">
            {isEditing ? 'Edit Course' : 'Add New Course'}
          </h1>
          <p className="mt-1 text-sm text-gray-600">
            Loading...
          </p>
        </div>
      </div>
    );
  }

  return <CourseFormContent course={course} isEditing={isEditing} />;
}

function CourseFormContent({ course, isEditing = false }: CourseFormProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('basic');

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<CourseFormData>({
    defaultValues: {
      name: '',
      shortName: '',
      level: 'Undergraduate',
      duration: '',
      stream: 'Engineering',
      description: '',
      detailedDescription: '',
      averageFees: 0,
      averageSalary: 0,
      isPopular: false,
      isPublished: false,
      seoTitle: '',
      seoDescription: '',
      seoKeywords: '',
      eligibilityMinPercentage: 0,
      eligibilityQualification: '',
      eligibilityAgeLimit: '',
      eligibilityEntranceExams: '',
      coreSubjects: '',
      electiveSubjects: '',
      practicalComponents: '',
      projectWork: '',
      jobRoles: '',
      industries: '',
      higherStudyOptions: '',
      skillsDeveloped: '',
    },
  });

  const createMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await fetch('/api/admin/courses', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create course');
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success('Course created successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-courses'] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const updateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      const response = await fetch(`/api/admin/courses/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update course');
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success('Course updated successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-courses'] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  // Populate form when editing
  useEffect(() => {
    if (isEditing && course) {
      setValue('name', course.name || '');
      setValue('shortName', course.shortName || '');
      setValue('level', course.level || 'Undergraduate');
      setValue('duration', course.duration || '');
      setValue('stream', course.stream || 'Engineering');
      setValue('description', course.description || '');
      setValue('detailedDescription', course.detailedDescription || '');
      setValue('averageFees', course.averageFees || 0);
      setValue('averageSalary', course.averageSalary || 0);
      setValue('isPopular', course.isPopular || false);
      setValue('isPublished', course.isPublished || false);
      setValue('seoTitle', course.seoTitle || '');
      setValue('seoDescription', course.seoDescription || '');
      setValue('seoKeywords', course.seoKeywords || '');

      // Populate eligibility data
      if (course.eligibility) {
        setValue('eligibilityMinPercentage', course.eligibility.minPercentage || 0);
        setValue('eligibilityQualification', course.eligibility.qualification || '');
        setValue('eligibilityAgeLimit', course.eligibility.ageLimit || '');
        setValue('eligibilityEntranceExams', course.eligibility.entranceExams?.join('\n') || '');
      }

      // Populate syllabus data
      if (course.syllabus) {
        setValue('coreSubjects', course.syllabus.coreSubjects?.join('\n') || '');
        setValue('electiveSubjects', course.syllabus.electiveSubjects?.join('\n') || '');
        setValue('practicalComponents', course.syllabus.practicalComponents?.join('\n') || '');
        setValue('projectWork', course.syllabus.projectWork || '');
      }

      // Populate career prospects data
      if (course.careerProspects) {
        setValue('jobRoles', course.careerProspects.jobRoles?.join('\n') || '');
        setValue('industries', course.careerProspects.industries?.join('\n') || '');
        setValue('higherStudyOptions', course.careerProspects.higherStudyOptions?.join('\n') || '');
        setValue('skillsDeveloped', course.careerProspects.skillsDeveloped?.join('\n') || '');
      }
    }
  }, [course, isEditing, setValue]);

  const onSubmit = async (data: CourseFormData) => {
    try {
      // Generate slug from name
      const slug = data.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');

      // Helper function to convert textarea strings to arrays
      const textareaToArray = (text: string) => {
        return text ? text.split('\n').filter(item => item.trim() !== '') : [];
      };

      // Prepare the payload
      const payload = {
        name: data.name,
        shortName: data.shortName,
        slug,
        level: data.level,
        duration: data.duration,
        stream: data.stream,
        description: data.description,
        detailedDescription: data.detailedDescription,
        averageFees: data.averageFees,
        averageSalary: data.averageSalary,
        isPopular: data.isPopular,
        isPublished: data.isPublished,
        seoTitle: data.seoTitle,
        seoDescription: data.seoDescription,
        seoKeywords: data.seoKeywords,
        eligibility: {
          minPercentage: data.eligibilityMinPercentage,
          qualification: data.eligibilityQualification,
          ageLimit: data.eligibilityAgeLimit,
          entranceExams: textareaToArray(data.eligibilityEntranceExams),
        },
        syllabus: {
          coreSubjects: textareaToArray(data.coreSubjects),
          electiveSubjects: textareaToArray(data.electiveSubjects),
          practicalComponents: textareaToArray(data.practicalComponents),
          projectWork: data.projectWork,
        },
        careerProspects: {
          jobRoles: textareaToArray(data.jobRoles),
          industries: textareaToArray(data.industries),
          higherStudyOptions: textareaToArray(data.higherStudyOptions),
          skillsDeveloped: textareaToArray(data.skillsDeveloped),
        },
      };

      if (isEditing && course) {
        await updateMutation.mutateAsync({ id: course.id, data: payload });
      } else {
        await createMutation.mutateAsync(payload);
      }

      router.push('/admin/courses');
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const tabs = [
    { id: 'basic', name: 'Basic Info', icon: '📋' },
    { id: 'details', name: 'Details', icon: '📝' },
    { id: 'eligibility', name: 'Eligibility', icon: '✅' },
    { id: 'syllabus', name: 'Syllabus', icon: '📚' },
    { id: 'career', name: 'Career Prospects', icon: '💼' },
    { id: 'seo', name: 'SEO', icon: '🔍' },
    { id: 'settings', name: 'Settings', icon: '⚙️' },
  ];

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">
          {isEditing ? 'Edit Course' : 'Add New Course'}
        </h1>
        <p className="mt-1 text-sm text-gray-600">
          {isEditing ? 'Update course information' : 'Create a new course listing'}
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                type="button"
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          {/* Basic Info Tab */}
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Course Name *
                  </label>
                  <input
                    type="text"
                    {...register('name', { required: 'Course name is required' })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter course name"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Short Name
                  </label>
                  <input
                    type="text"
                    {...register('shortName')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., B.Tech CSE"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Level *
                  </label>
                  <select
                    {...register('level', { required: 'Level is required' })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="Undergraduate">Undergraduate</option>
                    <option value="Postgraduate">Postgraduate</option>
                    <option value="Doctoral">Doctoral</option>
                    <option value="Diploma">Diploma</option>
                    <option value="Certificate">Certificate</option>
                  </select>
                  {errors.level && (
                    <p className="mt-1 text-sm text-red-600">{errors.level.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Duration
                  </label>
                  <input
                    type="text"
                    {...register('duration')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 4 years"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Stream *
                  </label>
                  <select
                    {...register('stream', { required: 'Stream is required' })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="Engineering">Engineering</option>
                    <option value="Medical">Medical</option>
                    <option value="Management">Management</option>
                    <option value="Arts">Arts</option>
                    <option value="Science">Science</option>
                    <option value="Commerce">Commerce</option>
                    <option value="Law">Law</option>
                    <option value="Architecture">Architecture</option>
                    <option value="Pharmacy">Pharmacy</option>
                    <option value="Agriculture">Agriculture</option>
                  </select>
                  {errors.stream && (
                    <p className="mt-1 text-sm text-red-600">{errors.stream.message}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  {...register('description')}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Brief description of the course"
                />
              </div>
            </div>
          )}

          {/* Details Tab */}
          {activeTab === 'details' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Detailed Description
                </label>
                <textarea
                  {...register('detailedDescription')}
                  rows={8}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Comprehensive description of the course, curriculum overview, learning outcomes, etc."
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Average Fees (₹)
                  </label>
                  <input
                    type="number"
                    {...register('averageFees', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 200000"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Average Salary (₹)
                  </label>
                  <input
                    type="number"
                    {...register('averageSalary', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 800000"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Eligibility Tab */}
          {activeTab === 'eligibility' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Minimum Percentage Required
                  </label>
                  <input
                    type="number"
                    {...register('eligibilityMinPercentage', { valueAsNumber: true })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 75"
                    min="0"
                    max="100"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Age Limit
                  </label>
                  <input
                    type="text"
                    {...register('eligibilityAgeLimit')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., 17-25 years"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Qualification Required
                </label>
                <textarea
                  {...register('eligibilityQualification')}
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., 10+2 with Physics, Chemistry, Mathematics from a recognized board"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Entrance Exams (one per line)
                </label>
                <textarea
                  {...register('eligibilityEntranceExams')}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="JEE Main&#10;JEE Advanced&#10;BITSAT&#10;State CET"
                />
                <p className="mt-1 text-sm text-gray-500">Enter each exam on a new line</p>
              </div>
            </div>
          )}

          {/* Syllabus Tab */}
          {activeTab === 'syllabus' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Core Subjects (one per line)
                </label>
                <textarea
                  {...register('coreSubjects')}
                  rows={6}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Mathematics&#10;Physics&#10;Programming Fundamentals&#10;Data Structures&#10;Computer Networks"
                />
                <p className="mt-1 text-sm text-gray-500">Enter each subject on a new line</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Elective Subjects (one per line)
                </label>
                <textarea
                  {...register('electiveSubjects')}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Machine Learning&#10;Cybersecurity&#10;Mobile App Development&#10;Cloud Computing"
                />
                <p className="mt-1 text-sm text-gray-500">Enter each subject on a new line</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Practical Components (one per line)
                </label>
                <textarea
                  {...register('practicalComponents')}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Programming Labs&#10;Hardware Labs&#10;Project Work&#10;Industrial Training"
                />
                <p className="mt-1 text-sm text-gray-500">Enter each component on a new line</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Project Work & Thesis
                </label>
                <textarea
                  {...register('projectWork')}
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Describe the project work, thesis requirements, capstone projects, etc."
                />
              </div>
            </div>
          )}

          {/* Career Prospects Tab */}
          {activeTab === 'career' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Job Roles (one per line)
                </label>
                <textarea
                  {...register('jobRoles')}
                  rows={6}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Software Engineer&#10;Data Scientist&#10;System Administrator&#10;Product Manager&#10;Technical Consultant"
                />
                <p className="mt-1 text-sm text-gray-500">Enter each job role on a new line</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Industries (one per line)
                </label>
                <textarea
                  {...register('industries')}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Information Technology&#10;Banking & Finance&#10;Healthcare&#10;E-commerce&#10;Telecommunications"
                />
                <p className="mt-1 text-sm text-gray-500">Enter each industry on a new line</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Higher Study Options (one per line)
                </label>
                <textarea
                  {...register('higherStudyOptions')}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="M.Tech in Computer Science&#10;MBA&#10;MS in Computer Science&#10;PhD in Computer Science"
                />
                <p className="mt-1 text-sm text-gray-500">Enter each option on a new line</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Skills Developed (one per line)
                </label>
                <textarea
                  {...register('skillsDeveloped')}
                  rows={6}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Programming&#10;Problem Solving&#10;Analytical Thinking&#10;Project Management&#10;Team Collaboration"
                />
                <p className="mt-1 text-sm text-gray-500">Enter each skill on a new line</p>
              </div>
            </div>
          )}

          {/* SEO Tab */}
          {activeTab === 'seo' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SEO Title
                </label>
                <input
                  type="text"
                  {...register('seoTitle')}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="SEO optimized title for search engines"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SEO Description
                </label>
                <textarea
                  {...register('seoDescription')}
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Meta description for search engines (150-160 characters)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SEO Keywords
                </label>
                <textarea
                  {...register('seoKeywords')}
                  rows={2}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Comma-separated keywords for SEO"
                />
              </div>
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === 'settings' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Popular Course</h3>
                  <p className="text-sm text-gray-600">Mark this course as popular to highlight it</p>
                </div>
                <Switch
                  checked={watch('isPopular')}
                  onCheckedChange={(checked) => setValue('isPopular', checked)}
                />
              </div>

              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Published</h3>
                  <p className="text-sm text-gray-600">Make this course visible to the public</p>
                </div>
                <Switch
                  checked={watch('isPublished')}
                  onCheckedChange={(checked) => setValue('isPublished', checked)}
                />
              </div>
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/admin/courses')}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {createMutation.isPending || updateMutation.isPending
              ? 'Saving...'
              : isEditing
              ? 'Update Course'
              : 'Create Course'}
          </Button>
        </div>
      </form>
    </div>
  );
}
