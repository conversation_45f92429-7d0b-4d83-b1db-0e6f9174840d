import Link from 'next/link';
import { formatCurrency } from '@/lib/utils';

interface College {
  id: string;
  name: string;
  shortName: string;
  location: string;
  logo: string;
  bannerImage: string;
  rating: number;
  reviewCount: number;
  establishedYear: number;
  collegeType: string;
  nirfRanking: number;
  placementPercentage: number;
  averagePackage: number;
  highestPackage: number;
  totalStudents: number;
  accreditations: string[];
  topCourses: string[];
  isVerified: boolean;
  isFeatured: boolean;
  keyHighlight?: string;
}

interface Props {
  college: College;
  viewMode: 'grid' | 'list';
  isSelected: boolean;
  onSelect: () => void;
}

export function CollegeCard({ college, viewMode, isSelected, onSelect }: Props) {
  if (viewMode === 'list') {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
        <div className="p-6">
          <div className="flex items-start space-x-4">
            {/* College Logo */}
            <div className="flex-shrink-0">
              <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 font-bold text-lg">
                  {college.shortName.charAt(0)}
                </span>
              </div>
            </div>

            {/* College Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      <Link href={`/colleges/${college.id}`} className="hover:text-blue-600">
                        {college.name}
                      </Link>
                    </h3>
                    {college.isVerified && (
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                        ✓ Verified
                      </span>
                    )}
                    {college.isFeatured && (
                      <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                        Featured
                      </span>
                    )}
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-2">{college.location}</p>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                    <span>Est. {college.establishedYear}</span>
                    <span>•</span>
                    <span>{college.collegeType}</span>
                    <span>•</span>
                    <span>NIRF #{college.nirfRanking}</span>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-3">
                    {college.accreditations.slice(0, 3).map((accreditation) => (
                      <span
                        key={accreditation}
                        className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
                      >
                        {accreditation}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Rating and Actions */}
                <div className="flex flex-col items-end space-y-3">
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center space-x-1">
                      <span className="text-yellow-500">★</span>
                      <span className="font-semibold text-gray-900">{college.rating}</span>
                      <span className="text-gray-500 text-sm">({college.reviewCount})</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={onSelect}
                      className={`px-3 py-1 rounded text-sm font-medium transition-colors duration-200 ${
                        isSelected
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {isSelected ? 'Selected' : 'Compare'}
                    </button>
                    <Link
                      href={`/colleges/${college.id}`}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200"
                    >
                      View Details
                    </Link>
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 mt-4 pt-4 border-t border-gray-100">
                <div className="text-center">
                  <p className="text-lg font-semibold text-blue-600">{college.placementPercentage}%</p>
                  <p className="text-xs text-gray-500">Placement Rate</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-semibold text-green-600">{formatCurrency(college.averagePackage)}</p>
                  <p className="text-xs text-gray-500">Avg. Package</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-semibold text-purple-600">{college.totalStudents.toLocaleString()}</p>
                  <p className="text-xs text-gray-500">Students</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Grid view
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-300 overflow-hidden">
      {/* College Banner */}
      <div className="relative h-48 bg-gradient-to-r from-blue-500 to-purple-600">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute top-4 left-4 flex items-center space-x-2">
          {college.isVerified && (
            <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
              ✓ Verified
            </span>
          )}
          {college.isFeatured && (
            <span className="bg-yellow-500 text-gray-900 px-2 py-1 rounded-full text-xs font-medium">
              Featured
            </span>
          )}
        </div>
        <div className="absolute top-4 right-4">
          <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
            <div className="flex items-center space-x-1">
              <span className="text-yellow-500">★</span>
              <span className="font-semibold text-gray-900">{college.rating}</span>
              <span className="text-gray-600 text-sm">({college.reviewCount})</span>
            </div>
          </div>
        </div>
        {college.keyHighlight && (
          <div className="absolute bottom-4 left-4">
            <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
              {college.keyHighlight}
            </span>
          </div>
        )}
      </div>

      {/* College Info */}
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-xl font-bold text-gray-900 mb-1">
              <Link href={`/colleges/${college.id}`} className="hover:text-blue-600">
                {college.name}
              </Link>
            </h3>
            <p className="text-gray-600 text-sm mb-2">{college.location}</p>
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span>Est. {college.establishedYear}</span>
              <span>•</span>
              <span>{college.collegeType}</span>
              <span>•</span>
              <span>NIRF #{college.nirfRanking}</span>
            </div>
          </div>
        </div>

        {/* Key Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-blue-50 rounded-lg p-3">
            <p className="text-blue-600 font-semibold text-sm">Placement Rate</p>
            <p className="text-blue-900 font-bold">{college.placementPercentage}%</p>
          </div>
          <div className="bg-green-50 rounded-lg p-3">
            <p className="text-green-600 font-semibold text-sm">Avg. Package</p>
            <p className="text-green-900 font-bold">{formatCurrency(college.averagePackage)}</p>
          </div>
        </div>

        {/* Accreditations */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            {college.accreditations.map((accreditation) => (
              <span
                key={accreditation}
                className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs font-medium"
              >
                {accreditation}
              </span>
            ))}
          </div>
        </div>

        {/* Top Courses */}
        <div className="mb-6">
          <p className="text-sm text-gray-600 mb-2">Popular Courses:</p>
          <div className="flex flex-wrap gap-2">
            {college.topCourses.map((course) => (
              <span
                key={course}
                className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium"
              >
                {course}
              </span>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={onSelect}
            className={`flex-1 text-center py-2 px-4 rounded-lg font-medium transition-colors duration-200 ${
              isSelected
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
            }`}
          >
            {isSelected ? 'Selected' : 'Compare'}
          </button>
          <Link
            href={`/colleges/${college.id}`}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors duration-200"
          >
            View Details
          </Link>
        </div>
      </div>
    </div>
  );
}
