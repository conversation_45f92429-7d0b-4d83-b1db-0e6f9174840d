'use client';

import { useState, useEffect } from 'react';
import { usePublicCollege } from '@/features/api/use-public-colleges';
import { CollegeHeader } from './detail/CollegeHeader';
import { CollegeNavigation } from './detail/CollegeNavigation';
import { OverviewTab } from './detail/OverviewTab';
import { CoursesTab } from './detail/CoursesTab';
import { AdmissionTab } from './detail/AdmissionTab';
import { PlacementsTab } from './detail/PlacementsTab';
import { ReviewsTab } from './detail/ReviewsTab';
import { FacilitiesTab } from './detail/FacilitiesTab';
import { GalleryTab } from './detail/GalleryTab';

// Mock college data - In real app, this would come from API
const mockCollege = {
  id: '1',
  name: 'Indian Institute of Technology Delhi',
  shortName: 'IIT Delhi',
  tagline: 'Excellence in Engineering and Technology',
  location: 'Hauz Khas, New Delhi, Delhi 110016',
  establishedYear: 1961,
  collegeType: 'Government',
  affiliation: 'Autonomous',
  approvals: ['AICTE', 'UGC', 'MHRD'],
  accreditations: ['NAAC A++', 'NBA', 'NIRF'],
  nirfRanking: 2,
  overallRating: 4.8,
  totalReviews: 2450,
  logo: '/placeholder-logo.png',
  bannerImages: ['/placeholder-college.jpg'],
  website: 'https://www.iitd.ac.in',
  phone: '+91-11-2659-1000',
  email: '<EMAIL>',
  about: 'IIT Delhi is one of the premier engineering institutions in India...',
  vision: 'To be a world-class institution...',
  mission: 'To generate knowledge and impart education...',
  totalStudents: 8500,
  facultyCount: 650,
  campusArea: '325 acres',
  placementStats: {
    placementPercentage: 95,
    averagePackage: 1800000,
    highestPackage: 5500000,
    medianPackage: 1600000,
    topRecruiters: ['Google', 'Microsoft', 'Amazon', 'Goldman Sachs'],
  },
};

interface Props {
  collegeId: string;
}

export function CollegeDetailPage({ collegeId }: Props) {
  const [activeTab, setActiveTab] = useState('overview');
  const [mount, setMount] = useState(false);

  // Fetch college data based on collegeId
  const { data: collegeResponse, isLoading, error } = usePublicCollege(collegeId);

  useEffect(() => {
    setMount(true);
  }, []);

  if (!mount) {
    return null; // Prevent hydration errors
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading college details...</p>
        </div>
      </div>
    );
  }

  if (error || !collegeResponse?.success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">College Not Found</h2>
          <p className="text-gray-600 mb-6">The college you're looking for doesn't exist or has been removed.</p>
          <a href="/colleges" className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
            Browse All Colleges
          </a>
        </div>
      </div>
    );
  }

  const college = collegeResponse.data;

  // Transform college data to match expected format
  const transformedCollege = {
    id: college.id,
    name: college.name,
    shortName: college.shortName || college.name,
    tagline: college.description || 'Excellence in Education',
    location: `${college.location?.address || ''}, ${college.location?.city || ''}, ${college.location?.state || ''} ${college.location?.pincode || ''}`.trim(),
    establishedYear: college.establishedYear,
    collegeType: college.collegeType,
    affiliation: college.affiliation,
    approvals: college.approvals || [],
    accreditations: college.accreditations || [],
    nirfRanking: college.nirfRanking,
    overallRating: 4.5, // Default rating - would come from reviews in real app
    totalReviews: 0, // Would come from reviews count
    logo: college.logo || '/placeholder-logo.png',
    bannerImages: college.bannerImages || ['/placeholder-college.jpg'],
    website: college.contactInfo?.website,
    phone: college.contactInfo?.phone,
    email: college.contactInfo?.email,
    about: college.about || college.description,
    vision: college.vision,
    mission: college.mission,
    totalStudents: college.totalStudents,
    facultyCount: college.facultyCount,
    campusArea: college.campusArea,
    placementStats: {
      placementPercentage: 85, // Default - would come from placement data
      averagePackage: 1200000,
      highestPackage: 3500000,
      medianPackage: 1000000,
      topRecruiters: ['TCS', 'Infosys', 'Wipro', 'Accenture'],
    },
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <OverviewTab college={transformedCollege} />;
      case 'courses':
        return <CoursesTab collegeId={collegeId} />;
      case 'admission':
        return <AdmissionTab collegeId={collegeId} />;
      case 'placements':
        return <PlacementsTab college={transformedCollege} />;
      case 'reviews':
        return <ReviewsTab collegeId={collegeId} />;
      case 'facilities':
        return <FacilitiesTab collegeId={collegeId} />;
      case 'gallery':
        return <GalleryTab collegeId={collegeId} />;
      default:
        return <OverviewTab college={transformedCollege} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* College Header */}
      <CollegeHeader college={transformedCollege} />

      {/* Navigation */}
      <CollegeNavigation activeTab={activeTab} onTabChange={setActiveTab} />

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderTabContent()}
      </div>
    </div>
  );
}
