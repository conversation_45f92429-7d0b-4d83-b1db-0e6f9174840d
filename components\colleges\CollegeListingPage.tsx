'use client';

import { useState, useEffect } from 'react';
import { SearchAndFilters } from './SearchAndFilters';
import { CollegeCard } from './CollegeCard';
import { MapView } from './MapView';
import { Pagination } from '@/components/ui/Pagination';
import { usePublicColleges, type PublicCollege } from '@/features/api/use-public-colleges';
import { ClientOnlyWrapper } from '@/components/ClientOnlyWrapper';

// Adapter function to transform API data to CollegeCard expected format
function transformCollegeData(college: PublicCollege) {
  return {
    id: college.id,
    name: college.name,
    shortName: college.shortName || college.name.split(' ').map(word => word[0]).join(''),
    location: college.location ? `${college.location.city}, ${college.location.state}` : 'Location not specified',
    logo: college.logo || '/placeholder-logo.png',
    bannerImage: college.bannerImages?.[0] || '/placeholder-college.jpg',
    rating: college.overallRating || 4.0,
    reviewCount: college.totalReviews || 0,
    establishedYear: college.establishedYear || new Date().getFullYear(),
    collegeType: college.collegeType || 'Private',
    nirfRanking: college.nirfRanking || 999,
    placementPercentage: 85, // Default value - this should come from API in future
    averagePackage: 800000, // Default value - this should come from API in future
    highestPackage: 2000000, // Default value - this should come from API in future
    totalStudents: 5000, // Default value - this should come from API in future
    accreditations: ['NAAC', 'UGC'], // Default values - this should come from API in future
    topCourses: ['B.Tech', 'MBA', 'M.Tech'], // Default values - this should come from API in future
    isVerified: college.isVerified,
    isFeatured: college.isFeatured,
    keyHighlight: college.isFeatured ? 'Featured College' : undefined,
  };
}

export function CollegeListingPage() {
  return (
    <ClientOnlyWrapper
      fallback={
        <div className="min-h-screen bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading colleges...</span>
            </div>
          </div>
        </div>
      }
    >
      <CollegeListingPageContent />
    </ClientOnlyWrapper>
  );
}

function CollegeListingPageContent() {
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'map'>('grid');
  const [selectedColleges, setSelectedColleges] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    location: '',
    stream: '',
    collegeType: '',
    fees: '',
    ranking: '',
    sortBy: 'name',
  });

  // Fetch colleges using the public API
  const { data, isLoading, error } = usePublicColleges({
    page: currentPage,
    limit: 12,
    search: filters.search,
    collegeType: filters.collegeType,
    sortBy: filters.sortBy === 'relevance' ? 'name' : filters.sortBy,
    sortOrder: 'asc',
  });

  const colleges = data?.data || [];
  const meta = data?.meta;
  const totalPages = meta?.totalPages || 1;

  // Transform college data for CollegeCard component
  const transformedColleges = colleges.map(transformCollegeData);

  const handleCollegeSelect = (collegeId: string) => {
    setSelectedColleges(prev =>
      prev.includes(collegeId)
        ? prev.filter(id => id !== collegeId)
        : [...prev, collegeId].slice(0, 3) // Max 3 colleges for comparison
    );
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading colleges...</span>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="text-red-600 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Colleges</h3>
            <p className="text-gray-600 mb-4">{error.message}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Find Your Perfect College</h1>
              <p className="mt-2 text-gray-600">
                Discover and compare {meta?.total?.toLocaleString() || '0'} colleges across India
              </p>
            </div>
            
            {/* View Mode Toggle */}
            <div className="mt-4 lg:mt-0 flex items-center space-x-2">
              <span className="text-sm text-gray-500">View:</span>
              <div className="flex rounded-lg border border-gray-300">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`px-3 py-2 text-sm font-medium rounded-l-lg ${
                    viewMode === 'grid'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Grid
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`px-3 py-2 text-sm font-medium border-l border-gray-300 ${
                    viewMode === 'list'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  List
                </button>
                <button
                  onClick={() => setViewMode('map')}
                  className={`px-3 py-2 text-sm font-medium rounded-r-lg border-l border-gray-300 ${
                    viewMode === 'map'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Map
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="lg:grid lg:grid-cols-4 lg:gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <SearchAndFilters 
              filters={filters}
              onFiltersChange={setFilters}
            />
          </div>

          {/* Main Content */}
          <div className="mt-8 lg:mt-0 lg:col-span-3">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
              <div>
                <p className="text-sm text-gray-600">
                  Showing {meta ? ((meta.page - 1) * meta.limit + 1) : 1}-{meta ? Math.min(meta.page * meta.limit, meta.total) : colleges.length} of {meta?.total || colleges.length} colleges
                </p>
              </div>
              
              {/* Compare Button */}
              {selectedColleges.length > 0 && (
                <div className="mt-4 sm:mt-0">
                  <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                    Compare {selectedColleges.length} College{selectedColleges.length > 1 ? 's' : ''}
                  </button>
                </div>
              )}
            </div>

            {/* Content based on view mode */}
            {viewMode === 'map' ? (
              <MapView colleges={transformedColleges} />
            ) : (
              <>
                {/* College Grid/List */}
                {transformedColleges.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                      <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No colleges found</h3>
                    <p className="text-gray-600">Try adjusting your search criteria or filters.</p>
                  </div>
                ) : (
                  <div className={
                    viewMode === 'grid'
                      ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                      : 'space-y-6'
                  }>
                    {transformedColleges.map((college) => (
                      <CollegeCard
                        key={college.id}
                        college={college}
                        viewMode={viewMode}
                        isSelected={selectedColleges.includes(college.id)}
                        onSelect={() => handleCollegeSelect(college.id)}
                      />
                    ))}
                  </div>
                )}

                {/* Pagination */}
                <div className="mt-12">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={setCurrentPage}
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
