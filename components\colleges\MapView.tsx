'use client';

interface College {
  id: string;
  name: string;
  shortName: string;
  location: string;
  rating: number;
  reviewCount: number;
  nirfRanking: number;
  placementPercentage: number;
  averagePackage: number;
  isVerified: boolean;
  isFeatured: boolean;
}

interface Props {
  colleges: College[];
}

export function MapView({ colleges }: Props) {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Map Container */}
      <div className="relative h-96 bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Interactive Map View</h3>
          <p className="text-gray-600 mb-4">
            Map integration will be implemented with Google Maps or Mapbox
          </p>
          <div className="text-sm text-gray-500">
            This will show college locations with interactive markers and detailed popups
          </div>
        </div>
      </div>

      {/* College List Below Map */}
      <div className="p-6 border-t border-gray-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">
          Colleges in this area ({colleges.length})
        </h4>
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {colleges.map((college) => (
            <div key={college.id} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <span className="text-blue-600 font-bold">
                  {college.shortName.charAt(0)}
                </span>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <h5 className="font-semibold text-gray-900 truncate">{college.name}</h5>
                  {college.isVerified && (
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                      ✓
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-1">{college.location}</p>
                <div className="flex items-center space-x-4 text-xs text-gray-500">
                  <span>NIRF #{college.nirfRanking}</span>
                  <span>•</span>
                  <span>{college.placementPercentage}% Placement</span>
                </div>
              </div>

              <div className="flex flex-col items-end space-y-1">
                <div className="flex items-center space-x-1">
                  <span className="text-yellow-500">★</span>
                  <span className="font-semibold text-gray-900">{college.rating}</span>
                </div>
                <span className="text-xs text-gray-500">({college.reviewCount} reviews)</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
