'use client';

import { useState } from 'react';

interface Filters {
  search: string;
  location: string;
  stream: string;
  collegeType: string;
  fees: string;
  ranking: string;
  sortBy: string;
}

interface Props {
  filters: Filters;
  onFiltersChange: (filters: Filters) => void;
}

const streams = [
  'Engineering',
  'Medical',
  'Management',
  'Arts',
  'Science',
  'Commerce',
  'Law',
  'Architecture',
  'Pharmacy',
  'Agriculture',
];

const collegeTypes = [
  'Government',
  'Private',
  'Deemed',
  'Autonomous',
  'Central University',
  'State University',
];

const feeRanges = [
  'Under ₹1 Lakh',
  '₹1-3 Lakhs',
  '₹3-5 Lakhs',
  '₹5-10 Lakhs',
  '₹10+ Lakhs',
];

const rankingFilters = [
  'NIRF Top 50',
  'NIRF Top 100',
  'NAAC A++',
  'NAAC A+',
  'NBA Accredited',
];

const sortOptions = [
  { value: 'relevance', label: 'Relevance' },
  { value: 'ranking', label: 'Ranking' },
  { value: 'fees_low', label: 'Fees: Low to High' },
  { value: 'fees_high', label: 'Fees: High to Low' },
  { value: 'rating', label: 'Rating' },
  { value: 'placement', label: 'Placement %' },
];

export function SearchAndFilters({ filters, onFiltersChange }: Props) {
  const [isExpanded, setIsExpanded] = useState(false);

  const updateFilter = (key: keyof Filters, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      search: '',
      location: '',
      stream: '',
      collegeType: '',
      fees: '',
      ranking: '',
      sortBy: 'relevance',
    });
  };

  const hasActiveFilters = Object.entries(filters).some(([key, value]) => 
    key !== 'sortBy' && value !== ''
  );

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* Search */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Search Colleges
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            value={filters.search}
            onChange={(e) => updateFilter('search', e.target.value)}
            placeholder="Search by college name, location..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Sort By */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Sort By
        </label>
        <select
          value={filters.sortBy}
          onChange={(e) => updateFilter('sortBy', e.target.value)}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          {sortOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Mobile Filter Toggle */}
      <div className="lg:hidden mb-4">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full flex items-center justify-between px-4 py-2 bg-gray-50 rounded-md text-sm font-medium text-gray-700"
        >
          <span>Filters</span>
          <svg
            className={`w-5 h-5 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>

      {/* Filters */}
      <div className={`space-y-6 ${!isExpanded ? 'hidden lg:block' : ''}`}>
        {/* Location */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Location
          </label>
          <input
            type="text"
            value={filters.location}
            onChange={(e) => updateFilter('location', e.target.value)}
            placeholder="Enter city or state"
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Stream */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Stream
          </label>
          <select
            value={filters.stream}
            onChange={(e) => updateFilter('stream', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Streams</option>
            {streams.map((stream) => (
              <option key={stream} value={stream.toLowerCase()}>
                {stream}
              </option>
            ))}
          </select>
        </div>

        {/* College Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            College Type
          </label>
          <select
            value={filters.collegeType}
            onChange={(e) => updateFilter('collegeType', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Types</option>
            {collegeTypes.map((type) => (
              <option key={type} value={type.toLowerCase()}>
                {type}
              </option>
            ))}
          </select>
        </div>

        {/* Fees */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Annual Fees
          </label>
          <select
            value={filters.fees}
            onChange={(e) => updateFilter('fees', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Any Range</option>
            {feeRanges.map((range) => (
              <option key={range} value={range}>
                {range}
              </option>
            ))}
          </select>
        </div>

        {/* Ranking/Accreditation */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ranking & Accreditation
          </label>
          <select
            value={filters.ranking}
            onChange={(e) => updateFilter('ranking', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Any Ranking</option>
            {rankingFilters.map((ranking) => (
              <option key={ranking} value={ranking}>
                {ranking}
              </option>
            ))}
          </select>
        </div>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="w-full px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors duration-200"
          >
            Clear All Filters
          </button>
        )}
      </div>

      {/* Quick Filters */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Quick Filters</h4>
        <div className="space-y-2">
          <button className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md">
            Top Engineering Colleges
          </button>
          <button className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md">
            Medical Colleges
          </button>
          <button className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md">
            MBA Colleges
          </button>
          <button className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md">
            Government Colleges
          </button>
          <button className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md">
            Low Fee Colleges
          </button>
        </div>
      </div>
    </div>
  );
}
