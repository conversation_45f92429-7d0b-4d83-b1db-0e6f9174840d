interface Props {
  collegeId: string;
}

export function AdmissionTab({ collegeId }: Props) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Admission Process</h2>
        <p className="text-gray-600">
          Complete information about admission requirements, important dates, and application process.
        </p>
      </div>

      {/* Important Dates */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Important Dates 2024-25</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <h4 className="font-semibold text-blue-900">Application Start</h4>
            <p className="text-blue-700 font-medium">March 15, 2024</p>
          </div>
          <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
            <h4 className="font-semibold text-yellow-900">Application End</h4>
            <p className="text-yellow-700 font-medium">May 30, 2024</p>
          </div>
          <div className="bg-green-50 rounded-lg p-4 border border-green-200">
            <h4 className="font-semibold text-green-900">Entrance Exam</h4>
            <p className="text-green-700 font-medium">June 15, 2024</p>
          </div>
          <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
            <h4 className="font-semibold text-purple-900">Result Declaration</h4>
            <p className="text-purple-700 font-medium">July 10, 2024</p>
          </div>
        </div>
      </div>

      {/* Admission Process Steps */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-6">Admission Process</h3>
        <div className="space-y-6">
          {[
            {
              step: 1,
              title: 'Online Application',
              description: 'Fill the online application form with required details and upload documents.',
              status: 'completed',
            },
            {
              step: 2,
              title: 'Entrance Examination',
              description: 'Appear for JEE Main/Advanced or other relevant entrance examinations.',
              status: 'current',
            },
            {
              step: 3,
              title: 'Counseling Process',
              description: 'Participate in counseling process based on entrance exam ranks.',
              status: 'upcoming',
            },
            {
              step: 4,
              title: 'Document Verification',
              description: 'Submit original documents for verification at the college.',
              status: 'upcoming',
            },
            {
              step: 5,
              title: 'Fee Payment',
              description: 'Pay the admission fee to confirm your seat.',
              status: 'upcoming',
            },
          ].map((item) => (
            <div key={item.step} className="flex items-start space-x-4">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm ${
                item.status === 'completed' ? 'bg-green-500 text-white' :
                item.status === 'current' ? 'bg-blue-500 text-white' :
                'bg-gray-300 text-gray-600'
              }`}>
                {item.step}
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900">{item.title}</h4>
                <p className="text-gray-600 text-sm mt-1">{item.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Eligibility Criteria */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Eligibility Criteria</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">For B.Tech Programs</h4>
            <ul className="list-disc list-inside text-gray-700 space-y-1">
              <li>Passed 10+2 with Physics, Chemistry, and Mathematics</li>
              <li>Minimum 75% marks in 10+2 (65% for SC/ST candidates)</li>
              <li>Valid JEE Main and JEE Advanced scores</li>
              <li>Age limit: Maximum 25 years (30 years for SC/ST)</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">For M.Tech Programs</h4>
            <ul className="list-disc list-inside text-gray-700 space-y-1">
              <li>Bachelor&apos;s degree in Engineering/Technology with 60% marks</li>
              <li>Valid GATE score in relevant discipline</li>
              <li>No age limit</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Required Documents */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Required Documents</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Academic Documents</h4>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-center space-x-2">
                <span className="text-green-500">✓</span>
                <span>10th Mark Sheet & Certificate</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="text-green-500">✓</span>
                <span>12th Mark Sheet & Certificate</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="text-green-500">✓</span>
                <span>JEE Main/Advanced Score Card</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="text-green-500">✓</span>
                <span>Transfer Certificate</span>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Personal Documents</h4>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-center space-x-2">
                <span className="text-green-500">✓</span>
                <span>Birth Certificate</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="text-green-500">✓</span>
                <span>Caste Certificate (if applicable)</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="text-green-500">✓</span>
                <span>Income Certificate</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="text-green-500">✓</span>
                <span>Passport Size Photographs</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Application Form */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Apply Now</h3>
        <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
          <div className="text-center">
            <h4 className="text-lg font-semibold text-blue-900 mb-2">Ready to Apply?</h4>
            <p className="text-blue-700 mb-4">Start your application process today</p>
            <div className="space-y-3">
              <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-medium transition-colors duration-200">
                Start Online Application
              </button>
              <button className="w-full bg-white hover:bg-gray-50 text-blue-600 py-3 px-6 rounded-lg font-medium border border-blue-600 transition-colors duration-200">
                Download Application Form
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
