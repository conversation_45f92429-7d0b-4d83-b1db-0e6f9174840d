import Link from 'next/link';

interface College {
  id: string;
  name: string;
  shortName: string;
  tagline: string;
  location: string;
  establishedYear: number;
  collegeType: string;
  affiliation: string;
  approvals: string[];
  accreditations: string[];
  nirfRanking: number;
  overallRating: number;
  totalReviews: number;
  logo: string;
  bannerImages: string[];
  website: string;
  phone: string;
  email: string;
}

interface Props {
  college: College;
}

export function CollegeHeader({ college }: Props) {
  return (
    <div className="relative">
      {/* Banner Image */}
      <div className="h-80 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
        
        {/* Breadcrumb */}
        <div className="absolute top-6 left-0 right-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link href="/" className="text-white/80 hover:text-white text-sm">
                    Home
                  </Link>
                </li>
                <li>
                  <svg className="flex-shrink-0 h-4 w-4 text-white/60" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </li>
                <li>
                  <Link href="/colleges" className="text-white/80 hover:text-white text-sm">
                    Colleges
                  </Link>
                </li>
                <li>
                  <svg className="flex-shrink-0 h-4 w-4 text-white/60" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </li>
                <li>
                  <span className="text-white text-sm font-medium">{college.shortName}</span>
                </li>
              </ol>
            </nav>
          </div>
        </div>

        {/* College Info */}
        <div className="absolute bottom-0 left-0 right-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
            <div className="flex flex-col lg:flex-row lg:items-end lg:justify-between">
              <div className="flex items-end space-x-6">
                {/* College Logo */}
                <div className="w-24 h-24 bg-white rounded-xl shadow-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-blue-600 font-bold text-2xl">
                    {college.shortName.split(' ').map(word => word.charAt(0)).join('')}
                  </span>
                </div>

                {/* College Details */}
                <div className="text-white">
                  <h1 className="text-3xl lg:text-4xl font-bold mb-2">{college.name}</h1>
                  <p className="text-blue-100 text-lg mb-3">{college.tagline}</p>
                  <div className="flex flex-wrap items-center gap-4 text-sm">
                    <div className="flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                      </svg>
                      <span>{college.location}</span>
                    </div>
                    <span>•</span>
                    <span>Est. {college.establishedYear}</span>
                    <span>•</span>
                    <span>{college.collegeType}</span>
                    <span>•</span>
                    <span>NIRF Rank #{college.nirfRanking}</span>
                  </div>
                </div>
              </div>

              {/* Rating and Actions */}
              <div className="mt-6 lg:mt-0 flex flex-col lg:items-end space-y-4">
                {/* Rating */}
                <div className="bg-white/10 backdrop-blur-sm rounded-lg px-4 py-3">
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center space-x-1">
                      <span className="text-yellow-400 text-lg">★</span>
                      <span className="text-white font-bold text-xl">{college.overallRating}</span>
                    </div>
                    <div className="text-white/80 text-sm">
                      ({college.totalReviews.toLocaleString()} reviews)
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <button className="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 border border-white/30">
                    Add to Compare
                  </button>
                  <button className="bg-yellow-500 hover:bg-yellow-600 text-gray-900 px-6 py-2 rounded-lg font-medium transition-colors duration-200">
                    Apply Now
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Info Bar */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-wrap items-center justify-between gap-4">
            {/* Quick Stats */}
            <div className="flex flex-wrap items-center gap-6 text-sm">
              <div className="flex items-center space-x-2">
                <span className="text-gray-500">Affiliation:</span>
                <span className="font-medium text-gray-900">{college.affiliation}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-gray-500">Approvals:</span>
                <div className="flex space-x-1">
                  {college.approvals.map((approval) => (
                    <span key={approval} className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                      {approval}
                    </span>
                  ))}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-gray-500">Accreditations:</span>
                <div className="flex space-x-1">
                  {college.accreditations.map((accreditation) => (
                    <span key={accreditation} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">
                      {accreditation}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Contact Info */}
            <div className="flex items-center space-x-4 text-sm">
              <a
                href={`tel:${college.phone}`}
                className="flex items-center space-x-1 text-blue-600 hover:text-blue-700"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                </svg>
                <span>{college.phone}</span>
              </a>
              <a
                href={college.website}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-1 text-blue-600 hover:text-blue-700"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.56-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.56.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd" />
                </svg>
                <span>Visit Website</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
