'use client';

import { useEffect, useState } from 'react';

interface Props {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const tabs = [
  { id: 'overview', label: 'Overview', icon: '📋' },
  { id: 'courses', label: 'Courses & Fees', icon: '📚' },
  { id: 'admission', label: 'Admission', icon: '📝' },
  { id: 'placements', label: 'Placements', icon: '💼' },
  { id: 'reviews', label: 'Reviews', icon: '⭐' },
  { id: 'facilities', label: 'Facilities', icon: '🏢' },
  { id: 'gallery', label: 'Gallery', icon: '📸' },
];

export function CollegeNavigation({ activeTab, onTabChange }: Props) {
  const [isSticky, setIsSticky] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      setIsSticky(offset > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className={`bg-white border-b border-gray-200 transition-all duration-300 ${
      isSticky ? 'sticky top-16 z-40 shadow-md' : ''
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav className="flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="text-lg">{tab.icon}</span>
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
}
