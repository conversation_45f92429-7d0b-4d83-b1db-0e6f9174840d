interface Props {
  collegeId: string;
}

// Mock courses data
const mockCourses = [
  {
    id: '1',
    name: 'Bachelor of Technology (B.Tech)',
    duration: '4 Years',
    level: 'Undergraduate',
    specializations: ['Computer Science', 'Mechanical', 'Electrical', 'Civil', 'Electronics'],
    eligibility: '10+2 with Physics, Chemistry, Mathematics',
    selectionProcess: 'JEE Main + JEE Advanced',
    totalFee: 800000,
    annualFee: 200000,
    seats: 120,
    isPopular: true,
  },
  {
    id: '2',
    name: 'Master of Technology (M.Tech)',
    duration: '2 Years',
    level: 'Postgraduate',
    specializations: ['Computer Science', 'VLSI Design', 'Power Systems', 'Structural Engineering'],
    eligibility: 'B.Tech/B.E. with 60% marks',
    selectionProcess: 'GATE + Interview',
    totalFee: 400000,
    annualFee: 200000,
    seats: 80,
    isPopular: true,
  },
  {
    id: '3',
    name: 'Doctor of Philosophy (PhD)',
    duration: '3-5 Years',
    level: 'Doctoral',
    specializations: ['All Engineering Disciplines', 'Science', 'Management'],
    eligibility: 'M.Tech/M.E./M.Sc. with 60% marks',
    selectionProcess: 'Written Test + Interview',
    totalFee: 150000,
    annualFee: 50000,
    seats: 40,
    isPopular: false,
  },
];

export function CoursesTab({ collegeId }: Props) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Courses & Fees</h2>
        <p className="text-gray-600">
          Comprehensive information about all courses offered with detailed fee structure and admission requirements.
        </p>
      </div>

      {/* Course Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-wrap gap-4">
          <select className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option>All Levels</option>
            <option>Undergraduate</option>
            <option>Postgraduate</option>
            <option>Doctoral</option>
          </select>
          <select className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option>All Streams</option>
            <option>Engineering</option>
            <option>Science</option>
            <option>Management</option>
          </select>
          <input
            type="text"
            placeholder="Search courses..."
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Courses List */}
      <div className="space-y-6">
        {mockCourses.map((course) => (
          <div key={course.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="p-6">
              <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <h3 className="text-xl font-bold text-gray-900">{course.name}</h3>
                    {course.isPopular && (
                      <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                        Popular
                      </span>
                    )}
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                      {course.level}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Course Details</h4>
                      <div className="space-y-1 text-sm text-gray-600">
                        <div><span className="font-medium">Duration:</span> {course.duration}</div>
                        <div><span className="font-medium">Total Seats:</span> {course.seats}</div>
                        <div><span className="font-medium">Selection:</span> {course.selectionProcess}</div>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Eligibility</h4>
                      <p className="text-sm text-gray-600">{course.eligibility}</p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-900 mb-2">Specializations</h4>
                    <div className="flex flex-wrap gap-2">
                      {course.specializations.map((spec) => (
                        <span
                          key={spec}
                          className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm"
                        >
                          {spec}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="lg:ml-8 mt-4 lg:mt-0">
                  <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <h4 className="font-semibold text-blue-900 mb-3">Fee Structure</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-blue-700">Annual Fee:</span>
                        <span className="font-bold text-blue-900">₹{course.annualFee.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-blue-700">Total Fee:</span>
                        <span className="font-bold text-blue-900">₹{course.totalFee.toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="mt-4 space-y-2">
                      <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors duration-200">
                        Apply Now
                      </button>
                      <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-900 py-2 px-4 rounded-lg font-medium transition-colors duration-200">
                        Download Brochure
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Expandable Details */}
            <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
              <button className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 font-medium">
                <span>View Detailed Syllabus & Curriculum</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Fee Payment Options */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Fee Payment & Financial Aid</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="text-2xl mb-2">💳</div>
            <h4 className="font-semibold text-green-900 mb-2">Installment Options</h4>
            <p className="text-green-700 text-sm">Pay fees in easy installments</p>
          </div>
          <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="text-2xl mb-2">🎓</div>
            <h4 className="font-semibold text-blue-900 mb-2">Scholarships</h4>
            <p className="text-blue-700 text-sm">Merit & need-based scholarships available</p>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
            <div className="text-2xl mb-2">🏦</div>
            <h4 className="font-semibold text-purple-900 mb-2">Education Loans</h4>
            <p className="text-purple-700 text-sm">Assistance with education loan processing</p>
          </div>
        </div>
      </div>
    </div>
  );
}
