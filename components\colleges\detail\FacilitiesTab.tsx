interface Props {
  collegeId: string;
}

const facilities = [
  {
    category: 'Academic Facilities',
    icon: '📚',
    items: [
      {
        name: 'Central Library',
        description: 'State-of-the-art library with over 500,000 books and digital resources',
        features: ['24/7 Access', 'Digital Library', 'Research Databases', 'Study Rooms'],
        image: '/placeholder-library.jpg',
      },
      {
        name: 'Laboratories',
        description: 'Modern labs equipped with latest technology and equipment',
        features: ['Research Labs', 'Computer Labs', 'Engineering Labs', 'Science Labs'],
        image: '/placeholder-lab.jpg',
      },
      {
        name: 'Classrooms',
        description: 'Smart classrooms with modern teaching aids and technology',
        features: ['Smart Boards', 'Audio-Visual Equipment', 'Air Conditioned', 'Wi-Fi Enabled'],
        image: '/placeholder-classroom.jpg',
      },
    ],
  },
  {
    category: 'Residential Facilities',
    icon: '🏠',
    items: [
      {
        name: 'Hostels',
        description: 'Comfortable accommodation for students with modern amenities',
        features: ['Single/Double Rooms', '24/7 Security', 'Wi-Fi', 'Laundry Service'],
        image: '/placeholder-hostel.jpg',
      },
      {
        name: 'Dining Halls',
        description: 'Hygienic mess facilities serving nutritious meals',
        features: ['Multi-Cuisine', 'Vegetarian Options', 'Clean Environment', 'Affordable Rates'],
        image: '/placeholder-dining.jpg',
      },
    ],
  },
  {
    category: 'Sports & Recreation',
    icon: '⚽',
    items: [
      {
        name: 'Sports Complex',
        description: 'Comprehensive sports facilities for various indoor and outdoor games',
        features: ['Swimming Pool', 'Gymnasium', 'Tennis Courts', 'Basketball Courts'],
        image: '/placeholder-sports.jpg',
      },
      {
        name: 'Recreation Center',
        description: 'Entertainment and relaxation facilities for students',
        features: ['Gaming Zone', 'Music Room', 'Dance Studio', 'Common Areas'],
        image: '/placeholder-recreation.jpg',
      },
    ],
  },
  {
    category: 'Health & Wellness',
    icon: '🏥',
    items: [
      {
        name: 'Medical Center',
        description: '24/7 medical facility with qualified doctors and nurses',
        features: ['Emergency Care', 'Regular Checkups', 'Pharmacy', 'Ambulance Service'],
        image: '/placeholder-medical.jpg',
      },
      {
        name: 'Counseling Center',
        description: 'Mental health and career counseling services',
        features: ['Psychological Support', 'Career Guidance', 'Stress Management', 'Workshops'],
        image: '/placeholder-counseling.jpg',
      },
    ],
  },
  {
    category: 'Other Facilities',
    icon: '🏢',
    items: [
      {
        name: 'Auditorium',
        description: 'Large capacity auditorium for events and conferences',
        features: ['1000+ Seating', 'Audio-Visual Equipment', 'Air Conditioned', 'Stage Facilities'],
        image: '/placeholder-auditorium.jpg',
      },
      {
        name: 'Transportation',
        description: 'Bus service connecting campus to major city locations',
        features: ['Multiple Routes', 'Regular Service', 'Safe Travel', 'Affordable Rates'],
        image: '/placeholder-transport.jpg',
      },
      {
        name: 'Banking & ATM',
        description: 'On-campus banking facilities and ATM services',
        features: ['Bank Branch', 'ATM Services', 'Online Banking', 'Student Accounts'],
        image: '/placeholder-banking.jpg',
      },
    ],
  },
];

export function FacilitiesTab({ collegeId }: Props) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Campus Facilities & Infrastructure</h2>
        <p className="text-gray-600">
          Comprehensive facilities designed to provide the best learning and living experience.
        </p>
      </div>

      {/* Facilities by Category */}
      {facilities.map((category) => (
        <div key={category.category} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-6">
            <span className="text-3xl">{category.icon}</span>
            <h3 className="text-xl font-bold text-gray-900">{category.category}</h3>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {category.items.map((facility) => (
              <div key={facility.name} className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-200">
                {/* Facility Image */}
                <div className="h-48 bg-gradient-to-r from-blue-400 to-purple-500 relative">
                  <div className="absolute inset-0 bg-black/20"></div>
                  <div className="absolute bottom-4 left-4">
                    <h4 className="text-white font-bold text-lg">{facility.name}</h4>
                  </div>
                </div>

                {/* Facility Details */}
                <div className="p-4">
                  <p className="text-gray-700 text-sm mb-4">{facility.description}</p>
                  
                  <div className="space-y-2">
                    <h5 className="font-semibold text-gray-900 text-sm">Key Features:</h5>
                    <div className="grid grid-cols-2 gap-2">
                      {facility.features.map((feature) => (
                        <div key={feature} className="flex items-center space-x-2">
                          <span className="text-green-500 text-xs">✓</span>
                          <span className="text-xs text-gray-600">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}

      {/* Campus Highlights */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-6">Campus Highlights</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="text-3xl mb-2">🌐</div>
            <h4 className="font-semibold text-blue-900 mb-2">Wi-Fi Campus</h4>
            <p className="text-blue-700 text-sm">High-speed internet across entire campus</p>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="text-3xl mb-2">🔒</div>
            <h4 className="font-semibold text-green-900 mb-2">24/7 Security</h4>
            <p className="text-green-700 text-sm">Round-the-clock security and surveillance</p>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
            <div className="text-3xl mb-2">♿</div>
            <h4 className="font-semibold text-purple-900 mb-2">Accessible</h4>
            <p className="text-purple-700 text-sm">Barrier-free campus for all students</p>
          </div>
          <div className="text-center p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="text-3xl mb-2">🌱</div>
            <h4 className="font-semibold text-yellow-900 mb-2">Green Campus</h4>
            <p className="text-yellow-700 text-sm">Eco-friendly and sustainable environment</p>
          </div>
        </div>
      </div>

      {/* Virtual Tour */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white">
        <div className="text-center">
          <h3 className="text-2xl font-bold mb-4">Take a Virtual Campus Tour</h3>
          <p className="text-blue-100 mb-6">
            Explore our beautiful campus from the comfort of your home with our 360° virtual tour
          </p>
          <div className="space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <button className="w-full sm:w-auto bg-white hover:bg-gray-100 text-blue-600 py-3 px-6 rounded-lg font-medium transition-colors duration-200">
              🎥 Start Virtual Tour
            </button>
            <button className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400 text-white py-3 px-6 rounded-lg font-medium transition-colors duration-200">
              📸 View Photo Gallery
            </button>
          </div>
        </div>
      </div>

      {/* Contact for Visit */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Plan Your Campus Visit</h3>
          <p className="text-gray-600 mb-6">
            Schedule a personal campus tour to experience our facilities firsthand
          </p>
          <div className="space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <button className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-medium transition-colors duration-200">
              📅 Schedule Campus Visit
            </button>
            <button className="w-full sm:w-auto bg-gray-100 hover:bg-gray-200 text-gray-900 py-3 px-6 rounded-lg font-medium transition-colors duration-200">
              📞 Contact Admissions
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
