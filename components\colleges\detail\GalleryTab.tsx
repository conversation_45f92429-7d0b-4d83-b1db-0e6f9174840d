interface Props {
  collegeId: string;
}

const galleryCategories = [
  {
    id: 'campus',
    name: 'Campus Life',
    count: 45,
    thumbnail: '/placeholder-campus.jpg',
  },
  {
    id: 'infrastructure',
    name: 'Infrastructure',
    count: 32,
    thumbnail: '/placeholder-infrastructure.jpg',
  },
  {
    id: 'events',
    name: 'Events & Festivals',
    count: 28,
    thumbnail: '/placeholder-events.jpg',
  },
  {
    id: 'labs',
    name: 'Laboratories',
    count: 18,
    thumbnail: '/placeholder-labs.jpg',
  },
  {
    id: 'hostels',
    name: 'Hostels & Accommodation',
    count: 15,
    thumbnail: '/placeholder-hostels.jpg',
  },
  {
    id: 'sports',
    name: 'Sports & Recreation',
    count: 22,
    thumbnail: '/placeholder-sports.jpg',
  },
];

const mockImages = [
  {
    id: '1',
    category: 'campus',
    title: 'Main Campus Building',
    description: 'The iconic main building of the college',
    url: '/placeholder-image1.jpg',
    type: 'image',
  },
  {
    id: '2',
    category: 'infrastructure',
    title: 'Modern Library',
    description: 'State-of-the-art library facility',
    url: '/placeholder-image2.jpg',
    type: 'image',
  },
  {
    id: '3',
    category: 'events',
    title: 'Annual Tech Fest',
    description: 'Students participating in technical festival',
    url: '/placeholder-image3.jpg',
    type: 'image',
  },
  {
    id: '4',
    category: 'campus',
    title: 'Campus Tour Video',
    description: 'Complete campus walkthrough',
    url: '/placeholder-video1.mp4',
    type: 'video',
    thumbnail: '/placeholder-video-thumb1.jpg',
  },
];

export function GalleryTab({ collegeId }: Props) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Photo & Video Gallery</h2>
        <p className="text-gray-600">
          Explore our campus through photos and videos showcasing student life, infrastructure, and events.
        </p>
      </div>

      {/* Gallery Categories */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Browse by Category</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {galleryCategories.map((category) => (
            <button
              key={category.id}
              className="group relative overflow-hidden rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-200"
            >
              {/* Category Thumbnail */}
              <div className="aspect-square bg-gradient-to-br from-blue-400 to-purple-500 relative">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors duration-200"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white">
                    <div className="text-2xl mb-1">
                      {category.id === 'campus' && '🏫'}
                      {category.id === 'infrastructure' && '🏢'}
                      {category.id === 'events' && '🎉'}
                      {category.id === 'labs' && '🔬'}
                      {category.id === 'hostels' && '🏠'}
                      {category.id === 'sports' && '⚽'}
                    </div>
                    <div className="font-semibold text-sm">{category.name}</div>
                    <div className="text-xs opacity-80">{category.count} items</div>
                  </div>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Featured Media */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Featured Photos & Videos</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockImages.map((item) => (
            <div key={item.id} className="group cursor-pointer">
              <div className="relative overflow-hidden rounded-lg border border-gray-200 hover:shadow-lg transition-shadow duration-200">
                {/* Media Thumbnail */}
                <div className="aspect-video bg-gradient-to-br from-gray-300 to-gray-400 relative">
                  <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-colors duration-200"></div>
                  
                  {/* Play button for videos */}
                  {item.type === 'video' && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center group-hover:bg-white transition-colors duration-200">
                        <svg className="w-6 h-6 text-blue-600 ml-1" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z" />
                        </svg>
                      </div>
                    </div>
                  )}

                  {/* Media type indicator */}
                  <div className="absolute top-3 right-3">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      item.type === 'video' 
                        ? 'bg-red-500 text-white' 
                        : 'bg-blue-500 text-white'
                    }`}>
                      {item.type === 'video' ? '📹 Video' : '📸 Photo'}
                    </span>
                  </div>
                </div>

                {/* Media Info */}
                <div className="p-4">
                  <h4 className="font-semibold text-gray-900 mb-1">{item.title}</h4>
                  <p className="text-sm text-gray-600">{item.description}</p>
                  <div className="mt-2">
                    <span className="inline-block bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs capitalize">
                      {item.category.replace(/([A-Z])/g, ' $1').trim()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Load More */}
        <div className="text-center mt-8">
          <button className="bg-gray-100 hover:bg-gray-200 text-gray-900 px-6 py-3 rounded-lg font-medium transition-colors duration-200">
            Load More Media
          </button>
        </div>
      </div>

      {/* Virtual Tour Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white">
        <div className="text-center">
          <h3 className="text-2xl font-bold mb-4">360° Virtual Campus Tour</h3>
          <p className="text-blue-100 mb-6">
            Experience our campus like never before with our immersive virtual reality tour
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="text-2xl mb-2">🏫</div>
              <h4 className="font-semibold mb-1">Academic Buildings</h4>
              <p className="text-sm text-blue-100">Explore classrooms, labs, and libraries</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="text-2xl mb-2">🏠</div>
              <h4 className="font-semibold mb-1">Residential Areas</h4>
              <p className="text-sm text-blue-100">Tour hostels and dining facilities</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="text-2xl mb-2">⚽</div>
              <h4 className="font-semibold mb-1">Recreation Zones</h4>
              <p className="text-sm text-blue-100">Visit sports complex and common areas</p>
            </div>
          </div>
          <button className="bg-white hover:bg-gray-100 text-blue-600 py-3 px-8 rounded-lg font-medium transition-colors duration-200">
            🥽 Start Virtual Tour
          </button>
        </div>
      </div>

      {/* Student Submissions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold text-gray-900">Student Submissions</h3>
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
            Submit Your Photo
          </button>
        </div>
        <p className="text-gray-600 mb-6">
          Photos and videos shared by our students showcasing their college experience
        </p>
        
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {[1, 2, 3, 4, 5, 6].map((index) => (
            <div key={index} className="aspect-square bg-gradient-to-br from-gray-200 to-gray-300 rounded-lg relative overflow-hidden group cursor-pointer">
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200"></div>
              <div className="absolute bottom-2 left-2 right-2">
                <div className="bg-white/90 backdrop-blur-sm rounded px-2 py-1">
                  <p className="text-xs font-medium text-gray-900 truncate">Student Photo {index}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Download Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Download High-Resolution Images</h3>
          <p className="text-gray-600 mb-6">
            Get official college photos for presentations, reports, or personal use
          </p>
          <div className="space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <button className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-medium transition-colors duration-200">
              📥 Download Photo Pack
            </button>
            <button className="w-full sm:w-auto bg-gray-100 hover:bg-gray-200 text-gray-900 py-3 px-6 rounded-lg font-medium transition-colors duration-200">
              📧 Request Custom Photos
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
