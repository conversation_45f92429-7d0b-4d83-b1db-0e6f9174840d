interface College {
  id: string;
  name: string;
  about: string;
  vision: string;
  mission: string;
  totalStudents: number;
  facultyCount: number;
  campusArea: string;
  establishedYear: number;
  collegeType: string;
  affiliation: string;
  nirfRanking: number;
  placementStats: {
    placementPercentage: number;
    averagePackage: number;
    highestPackage: number;
    medianPackage: number;
    topRecruiters: string[];
  };
}

interface Props {
  college: College;
}

export function OverviewTab({ college }: Props) {
  const keyStats = [
    {
      label: 'Total Students',
      value: college.totalStudents.toLocaleString(),
      icon: '👥',
      color: 'blue',
    },
    {
      label: 'Faculty Members',
      value: college.facultyCount.toString(),
      icon: '👨‍🏫',
      color: 'green',
    },
    {
      label: 'Campus Area',
      value: college.campusArea,
      icon: '🏫',
      color: 'purple',
    },
    {
      label: 'NIRF Ranking',
      value: `#${college.nirfRanking}`,
      icon: '🏆',
      color: 'yellow',
    },
    {
      label: 'Placement Rate',
      value: `${college.placementStats.placementPercentage}%`,
      icon: '💼',
      color: 'indigo',
    },
    {
      label: 'Average Package',
      value: `₹${(college.placementStats.averagePackage / 100000).toFixed(1)}L`,
      icon: '💰',
      color: 'emerald',
    },
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-50 text-blue-700 border-blue-200',
      green: 'bg-green-50 text-green-700 border-green-200',
      purple: 'bg-purple-50 text-purple-700 border-purple-200',
      yellow: 'bg-yellow-50 text-yellow-700 border-yellow-200',
      indigo: 'bg-indigo-50 text-indigo-700 border-indigo-200',
      emerald: 'bg-emerald-50 text-emerald-700 border-emerald-200',
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className="space-y-8">
      {/* Key Statistics */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Statistics</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {keyStats.map((stat) => (
            <div
              key={stat.label}
              className={`p-4 rounded-lg border ${getColorClasses(stat.color)}`}
            >
              <div className="text-2xl mb-2">{stat.icon}</div>
              <div className="font-bold text-lg">{stat.value}</div>
              <div className="text-sm opacity-80">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* About Section */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">About {college.name}</h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed">
                {college.about || `${college.name} is a premier educational institution established in ${college.establishedYear}. Known for its excellence in education and research, the college has been consistently ranked among the top institutions in India. With a strong focus on academic excellence, innovation, and holistic development, the college provides world-class education and prepares students for successful careers in their chosen fields.`}
              </p>
            </div>
          </div>

          {/* Vision & Mission */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <span className="text-2xl mr-2">🎯</span>
                Vision
              </h3>
              <p className="text-gray-700 leading-relaxed">
                {college.vision || 'To be a world-class institution of higher learning, fostering innovation, research, and excellence in education while contributing to the betterment of society.'}
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <span className="text-2xl mr-2">🚀</span>
                Mission
              </h3>
              <p className="text-gray-700 leading-relaxed">
                {college.mission || 'To provide quality education, promote research and innovation, and develop skilled professionals who can contribute to the advancement of technology and society.'}
              </p>
            </div>
          </div>

          {/* Top Recruiters */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
              <span className="text-2xl mr-2">🏢</span>
              Top Recruiters
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {college.placementStats.topRecruiters.map((recruiter) => (
                <div
                  key={recruiter}
                  className="bg-gray-50 rounded-lg p-4 text-center border border-gray-200 hover:shadow-md transition-shadow duration-200"
                >
                  <div className="font-semibold text-gray-900">{recruiter}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Facts */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Quick Facts</h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">Established</span>
                <span className="font-semibold text-gray-900">{college.establishedYear}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">Type</span>
                <span className="font-semibold text-gray-900">{college.collegeType}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">Affiliation</span>
                <span className="font-semibold text-gray-900">{college.affiliation}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">NIRF Ranking</span>
                <span className="font-semibold text-gray-900">#{college.nirfRanking}</span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-gray-600">Campus Size</span>
                <span className="font-semibold text-gray-900">{college.campusArea}</span>
              </div>
            </div>
          </div>

          {/* Placement Highlights */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Placement Highlights</h3>
            <div className="space-y-4">
              <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                <div className="text-green-700 font-semibold text-lg">
                  {college.placementStats.placementPercentage}%
                </div>
                <div className="text-green-600 text-sm">Placement Rate</div>
              </div>
              <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <div className="text-blue-700 font-semibold text-lg">
                  ₹{(college.placementStats.averagePackage / 100000).toFixed(1)}L
                </div>
                <div className="text-blue-600 text-sm">Average Package</div>
              </div>
              <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                <div className="text-purple-700 font-semibold text-lg">
                  ₹{(college.placementStats.highestPackage / 100000).toFixed(1)}L
                </div>
                <div className="text-purple-600 text-sm">Highest Package</div>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Contact Information</h3>
            <div className="space-y-3">
              <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-200">
                📞 Call for Admission
              </button>
              <button className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-200">
                💬 Chat with Counselor
              </button>
              <button className="w-full bg-yellow-500 hover:bg-yellow-600 text-gray-900 py-3 px-4 rounded-lg font-medium transition-colors duration-200">
                📧 Request Brochure
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
