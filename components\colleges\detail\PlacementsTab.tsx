interface College {
  placementStats: {
    placementPercentage: number;
    averagePackage: number;
    highestPackage: number;
    medianPackage: number;
    topRecruiters: string[];
  };
}

interface Props {
  college: College;
}

export function PlacementsTab({ college }: Props) {
  const placementData = [
    { year: '2023-24', placed: 95, avgPackage: 18, highestPackage: 55 },
    { year: '2022-23', placed: 92, avgPackage: 16, highestPackage: 48 },
    { year: '2021-22', placed: 89, avgPackage: 14, highestPackage: 42 },
    { year: '2020-21', placed: 85, avgPackage: 12, highestPackage: 38 },
  ];

  const sectorWiseData = [
    { sector: 'IT & Software', percentage: 45, companies: 120 },
    { sector: 'Core Engineering', percentage: 25, companies: 80 },
    { sector: 'Consulting', percentage: 15, companies: 45 },
    { sector: 'Finance & Banking', percentage: 10, companies: 30 },
    { sector: 'Others', percentage: 5, companies: 25 },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Placement Statistics</h2>
        <p className="text-gray-600">
          Comprehensive placement data and career opportunities for students.
        </p>
      </div>

      {/* Key Placement Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
          <div className="text-3xl font-bold text-green-600 mb-2">
            {college.placementStats.placementPercentage}%
          </div>
          <div className="text-gray-600">Placement Rate</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
          <div className="text-3xl font-bold text-blue-600 mb-2">
            ₹{(college.placementStats.averagePackage / 100000).toFixed(1)}L
          </div>
          <div className="text-gray-600">Average Package</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
          <div className="text-3xl font-bold text-purple-600 mb-2">
            ₹{(college.placementStats.highestPackage / 100000).toFixed(1)}L
          </div>
          <div className="text-gray-600">Highest Package</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
          <div className="text-3xl font-bold text-orange-600 mb-2">
            ₹{(college.placementStats.medianPackage / 100000).toFixed(1)}L
          </div>
          <div className="text-gray-600">Median Package</div>
        </div>
      </div>

      {/* Year-wise Placement Trends */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Placement Trends</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Academic Year</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Placement %</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Avg Package (LPA)</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Highest Package (LPA)</th>
              </tr>
            </thead>
            <tbody>
              {placementData.map((data) => (
                <tr key={data.year} className="border-b border-gray-100">
                  <td className="py-3 px-4 text-gray-900">{data.year}</td>
                  <td className="py-3 px-4">
                    <span className="text-green-600 font-semibold">{data.placed}%</span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-blue-600 font-semibold">₹{data.avgPackage}L</span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-purple-600 font-semibold">₹{data.highestPackage}L</span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Sector-wise Placement */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Sector-wise Placement Distribution</h3>
        <div className="space-y-4">
          {sectorWiseData.map((sector) => (
            <div key={sector.sector} className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                  <span className="font-medium text-gray-900">{sector.sector}</span>
                  <span className="text-sm text-gray-600">{sector.percentage}% ({sector.companies} companies)</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${sector.percentage}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Top Recruiters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Top Recruiters</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {college.placementStats.topRecruiters.map((recruiter) => (
            <div
              key={recruiter}
              className="bg-gray-50 rounded-lg p-4 text-center border border-gray-200 hover:shadow-md transition-shadow duration-200"
            >
              <div className="font-semibold text-gray-900 text-sm">{recruiter}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Placement Process */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Placement Process</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Pre-Placement Activities</h4>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-center space-x-2">
                <span className="text-blue-500">•</span>
                <span>Resume building workshops</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="text-blue-500">•</span>
                <span>Mock interviews and group discussions</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="text-blue-500">•</span>
                <span>Aptitude and technical training</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="text-blue-500">•</span>
                <span>Industry interaction sessions</span>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Placement Support</h4>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-center space-x-2">
                <span className="text-green-500">•</span>
                <span>Dedicated placement cell</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="text-green-500">•</span>
                <span>Industry mentorship programs</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="text-green-500">•</span>
                <span>Alumni network support</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="text-green-500">•</span>
                <span>Career counseling services</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Contact Placement Cell */}
      <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
        <div className="text-center">
          <h3 className="text-xl font-bold text-blue-900 mb-2">Contact Placement Cell</h3>
          <p className="text-blue-700 mb-4">Get in touch for placement-related queries</p>
          <div className="space-y-3">
            <button className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-lg font-medium transition-colors duration-200">
              📧 Email Placement Cell
            </button>
            <button className="ml-3 bg-white hover:bg-gray-50 text-blue-600 py-2 px-6 rounded-lg font-medium border border-blue-600 transition-colors duration-200">
              📞 Call for Information
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
