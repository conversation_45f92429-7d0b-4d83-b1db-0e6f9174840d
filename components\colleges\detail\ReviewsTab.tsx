interface Props {
  collegeId: string;
}

// Mock reviews data
const mockReviews = [
  {
    id: '1',
    studentName: '<PERSON><PERSON>',
    course: 'B.Tech Computer Science',
    year: '2023',
    overallRating: 4.5,
    ratings: {
      academics: 4.5,
      faculty: 4.0,
      infrastructure: 4.5,
      placements: 5.0,
      socialLife: 4.0,
      valueForMoney: 4.0,
    },
    title: 'Excellent college with great placement opportunities',
    pros: 'Great faculty, excellent placement record, modern infrastructure, vibrant campus life',
    cons: 'Heavy workload, competitive environment',
    review: 'IIT Delhi has been an amazing experience. The faculty is world-class and the placement opportunities are excellent. The campus infrastructure is top-notch with modern labs and facilities.',
    helpful: 45,
    date: '2024-01-15',
    verified: true,
  },
  {
    id: '2',
    studentName: '<PERSON>riya M.',
    course: 'M.Tech VLSI Design',
    year: '2022',
    overallRating: 4.2,
    ratings: {
      academics: 4.5,
      faculty: 4.0,
      infrastructure: 4.0,
      placements: 4.5,
      socialLife: 3.5,
      valueForMoney: 4.0,
    },
    title: 'Good for research and higher studies',
    pros: 'Research opportunities, experienced faculty, good lab facilities',
    cons: 'Limited industry exposure for some specializations',
    review: 'The M.Tech program is well-structured with good research opportunities. Faculty members are helpful and knowledgeable.',
    helpful: 32,
    date: '2024-01-10',
    verified: true,
  },
];

export function ReviewsTab({ collegeId }: Props) {
  const averageRatings = {
    overall: 4.4,
    academics: 4.3,
    faculty: 4.1,
    infrastructure: 4.2,
    placements: 4.6,
    socialLife: 3.8,
    valueForMoney: 4.0,
  };

  const ratingDistribution = [
    { stars: 5, count: 1250, percentage: 60 },
    { stars: 4, count: 625, percentage: 30 },
    { stars: 3, count: 125, percentage: 6 },
    { stars: 2, count: 42, percentage: 2 },
    { stars: 1, count: 21, percentage: 1 },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Student Reviews & Ratings</h2>
            <p className="text-gray-600">
              Authentic reviews from current students and alumni
            </p>
          </div>
          <button className="mt-4 lg:mt-0 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
            Write a Review
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Rating Summary */}
        <div className="lg:col-span-1 space-y-6">
          {/* Overall Rating */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Overall Rating</h3>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">{averageRatings.overall}</div>
              <div className="flex justify-center mb-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <span
                    key={star}
                    className={`text-xl ${
                      star <= averageRatings.overall ? 'text-yellow-400' : 'text-gray-300'
                    }`}
                  >
                    ★
                  </span>
                ))}
              </div>
              <p className="text-gray-600">Based on 2,063 reviews</p>
            </div>
          </div>

          {/* Rating Distribution */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Rating Distribution</h3>
            <div className="space-y-3">
              {ratingDistribution.map((item) => (
                <div key={item.stars} className="flex items-center space-x-3">
                  <span className="text-sm font-medium text-gray-700 w-6">{item.stars}★</span>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-yellow-400 h-2 rounded-full"
                      style={{ width: `${item.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 w-12">{item.count}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Category Ratings */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Category Ratings</h3>
            <div className="space-y-3">
              {Object.entries(averageRatings).map(([category, rating]) => {
                if (category === 'overall') return null;
                return (
                  <div key={category} className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700 capitalize">
                      {category.replace(/([A-Z])/g, ' $1').trim()}
                    </span>
                    <div className="flex items-center space-x-2">
                      <div className="flex">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <span
                            key={star}
                            className={`text-sm ${
                              star <= rating ? 'text-yellow-400' : 'text-gray-300'
                            }`}
                          >
                            ★
                          </span>
                        ))}
                      </div>
                      <span className="text-sm font-semibold text-gray-900">{rating}</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Reviews List */}
        <div className="lg:col-span-2 space-y-6">
          {/* Filters */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex flex-wrap gap-4">
              <select className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option>All Courses</option>
                <option>B.Tech</option>
                <option>M.Tech</option>
                <option>PhD</option>
              </select>
              <select className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option>All Ratings</option>
                <option>5 Stars</option>
                <option>4+ Stars</option>
                <option>3+ Stars</option>
              </select>
              <select className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option>Most Recent</option>
                <option>Most Helpful</option>
                <option>Highest Rated</option>
                <option>Lowest Rated</option>
              </select>
            </div>
          </div>

          {/* Individual Reviews */}
          {mockReviews.map((review) => (
            <div key={review.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold">
                      {review.studentName.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h4 className="font-semibold text-gray-900">{review.studentName}</h4>
                      {review.verified && (
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                          ✓ Verified
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{review.course} • Class of {review.year}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-1 mb-1">
                    <span className="text-yellow-400">★</span>
                    <span className="font-semibold text-gray-900">{review.overallRating}</span>
                  </div>
                  <p className="text-xs text-gray-500">{review.date}</p>
                </div>
              </div>

              <h5 className="font-semibold text-gray-900 mb-3">{review.title}</h5>

              <div className="grid grid-cols-3 md:grid-cols-6 gap-2 mb-4">
                {Object.entries(review.ratings).map(([category, rating]) => (
                  <div key={category} className="text-center">
                    <div className="text-xs text-gray-600 capitalize mb-1">
                      {category.replace(/([A-Z])/g, ' $1').trim()}
                    </div>
                    <div className="text-sm font-semibold text-gray-900">{rating}</div>
                  </div>
                ))}
              </div>

              <div className="space-y-3 mb-4">
                <div>
                  <span className="text-sm font-semibold text-green-700">Pros: </span>
                  <span className="text-sm text-gray-700">{review.pros}</span>
                </div>
                <div>
                  <span className="text-sm font-semibold text-red-700">Cons: </span>
                  <span className="text-sm text-gray-700">{review.cons}</span>
                </div>
              </div>

              <p className="text-gray-700 mb-4">{review.review}</p>

              <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                <button className="flex items-center space-x-2 text-gray-600 hover:text-blue-600">
                  <span className="text-sm">👍</span>
                  <span className="text-sm">Helpful ({review.helpful})</span>
                </button>
                <button className="text-gray-600 hover:text-blue-600">
                  <span className="text-sm">Report</span>
                </button>
              </div>
            </div>
          ))}

          {/* Load More */}
          <div className="text-center">
            <button className="bg-gray-100 hover:bg-gray-200 text-gray-900 px-6 py-3 rounded-lg font-medium transition-colors duration-200">
              Load More Reviews
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
