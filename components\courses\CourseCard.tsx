import Link from 'next/link';
import { formatCurrency } from '@/lib/utils';

interface Course {
  id: string;
  name: string;
  shortName: string;
  level: string;
  duration: string;
  stream: string;
  description: string;
  specializations: string[];
  eligibility: string;
  entranceExams: string[];
  averageFees: number;
  averageSalary: number;
  topColleges: string[];
  careerOptions: string[];
  isPopular: boolean;
  demandLevel: string;
  jobOpportunities: string;
}

interface Props {
  course: Course;
}

export function CourseCard({ course }: Props) {
  const getDemandColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'very high':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const getOpportunityColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'excellent':
        return 'bg-green-100 text-green-800';
      case 'very good':
        return 'bg-blue-100 text-blue-800';
      case 'good':
        return 'bg-yellow-100 text-yellow-800';
      case 'average':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-300 overflow-hidden">
      {/* Course Header */}
      <div className="relative bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              {course.isPopular && (
                <span className="bg-yellow-500 text-gray-900 px-2 py-1 rounded-full text-xs font-medium">
                  Popular
                </span>
              )}
              <span className="bg-white/20 backdrop-blur-sm text-white px-2 py-1 rounded-full text-xs font-medium">
                {course.level}
              </span>
            </div>
            <h3 className="text-xl font-bold mb-1">
              <Link href={`/courses/${course.id}`} className="hover:text-yellow-200">
                {course.name}
              </Link>
            </h3>
            <p className="text-blue-100 text-sm">{course.stream} • {course.duration}</p>
          </div>
        </div>
      </div>

      {/* Course Content */}
      <div className="p-6">
        {/* Description */}
        <p className="text-gray-700 text-sm mb-4 line-clamp-2">{course.description}</p>

        {/* Key Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
            <p className="text-blue-600 font-semibold text-sm">Avg. Fees</p>
            <p className="text-blue-900 font-bold">{formatCurrency(course.averageFees)}</p>
          </div>
          <div className="bg-green-50 rounded-lg p-3 border border-green-200">
            <p className="text-green-600 font-semibold text-sm">Avg. Salary</p>
            <p className="text-green-900 font-bold">{formatCurrency(course.averageSalary)}</p>
          </div>
        </div>

        {/* Demand & Opportunities */}
        <div className="flex items-center space-x-2 mb-4">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDemandColor(course.demandLevel)}`}>
            {course.demandLevel} Demand
          </span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getOpportunityColor(course.jobOpportunities)}`}>
            {course.jobOpportunities} Jobs
          </span>
        </div>

        {/* Specializations */}
        <div className="mb-4">
          <h4 className="text-sm font-semibold text-gray-900 mb-2">Popular Specializations:</h4>
          <div className="flex flex-wrap gap-1">
            {course.specializations.slice(0, 3).map((spec) => (
              <span
                key={spec}
                className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
              >
                {spec}
              </span>
            ))}
            {course.specializations.length > 3 && (
              <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                +{course.specializations.length - 3} more
              </span>
            )}
          </div>
        </div>

        {/* Entrance Exams */}
        <div className="mb-4">
          <h4 className="text-sm font-semibold text-gray-900 mb-2">Entrance Exams:</h4>
          <div className="flex flex-wrap gap-1">
            {course.entranceExams.slice(0, 2).map((exam) => (
              <span
                key={exam}
                className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium"
              >
                {exam}
              </span>
            ))}
            {course.entranceExams.length > 2 && (
              <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium">
                +{course.entranceExams.length - 2} more
              </span>
            )}
          </div>
        </div>

        {/* Top Colleges */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-900 mb-2">Top Colleges:</h4>
          <div className="text-sm text-gray-600">
            {course.topColleges.slice(0, 2).join(', ')}
            {course.topColleges.length > 2 && ` +${course.topColleges.length - 2} more`}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Link
            href={`/courses/${course.id}`}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors duration-200 block"
          >
            View Details
          </Link>
          <div className="grid grid-cols-2 gap-2">
            <button className="bg-gray-100 hover:bg-gray-200 text-gray-900 py-2 px-3 rounded-lg text-sm font-medium transition-colors duration-200">
              Compare
            </button>
            <button className="bg-green-100 hover:bg-green-200 text-green-900 py-2 px-3 rounded-lg text-sm font-medium transition-colors duration-200">
              Find Colleges
            </button>
          </div>
        </div>
      </div>

      {/* Quick Info Footer */}
      <div className="bg-gray-50 px-6 py-3 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs text-gray-600">
          <span>Eligibility: {course.eligibility.split('(')[0].trim()}</span>
          <Link
            href={`/courses/${course.id}#eligibility`}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            View Details →
          </Link>
        </div>
      </div>
    </div>
  );
}
