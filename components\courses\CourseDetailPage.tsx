'use client';

import { useState } from 'react';
import Link from 'next/link';
import { formatCurrency } from '@/lib/utils';

// Mock course data
const mockCourse = {
  id: '1',
  name: 'Bachelor of Technology (B.Tech)',
  shortName: 'B.Tech',
  level: 'Undergraduate',
  duration: '4 Years',
  stream: 'Engineering',
  description: 'Bachelor of Technology is a comprehensive 4-year undergraduate engineering program that provides students with strong technical foundation and practical skills in various engineering disciplines.',
  detailedDescription: 'The B.Tech program is designed to develop technical competency, analytical thinking, and problem-solving skills. Students gain hands-on experience through laboratory work, projects, and internships. The curriculum is regularly updated to meet industry requirements and includes emerging technologies.',
  specializations: [
    'Computer Science & Engineering',
    'Mechanical Engineering',
    'Electrical Engineering',
    'Civil Engineering',
    'Electronics & Communication',
    'Chemical Engineering',
    'Aerospace Engineering',
    'Biotechnology',
  ],
  eligibility: {
    academic: '10+2 with Physics, Chemistry, Mathematics',
    percentage: '60% marks (55% for reserved categories)',
    ageLimit: 'Maximum 25 years (30 years for reserved categories)',
  },
  entranceExams: [
    { name: 'JEE <PERSON>', description: 'National level entrance exam' },
    { name: 'JEE Advanced', description: 'For admission to IITs' },
    { name: 'BITSAT', description: 'For BITS Pilani admission' },
    { name: 'State CET', description: 'State level entrance exams' },
  ],
  averageFees: 400000,
  feeRange: { min: 100000, max: 2000000 },
  averageSalary: 800000,
  salaryRange: { min: 300000, max: 5000000 },
  topColleges: [
    'IIT Delhi', 'IIT Bombay', 'IIT Madras', 'IIT Kanpur',
    'NIT Trichy', 'NIT Warangal', 'BITS Pilani', 'VIT Vellore'
  ],
  careerOptions: [
    'Software Engineer', 'Mechanical Engineer', 'Civil Engineer',
    'Electronics Engineer', 'Data Scientist', 'Product Manager',
    'Research Scientist', 'Entrepreneur'
  ],
  jobOpportunities: 'Excellent',
  demandLevel: 'High',
  syllabus: {
    'Year 1': [
      'Engineering Mathematics',
      'Engineering Physics',
      'Engineering Chemistry',
      'Programming Fundamentals',
      'Engineering Graphics',
      'Communication Skills'
    ],
    'Year 2': [
      'Data Structures & Algorithms',
      'Digital Logic Design',
      'Computer Organization',
      'Discrete Mathematics',
      'Object-Oriented Programming',
      'Database Management Systems'
    ],
    'Year 3': [
      'Operating Systems',
      'Computer Networks',
      'Software Engineering',
      'Compiler Design',
      'Machine Learning',
      'Web Technologies'
    ],
    'Year 4': [
      'Artificial Intelligence',
      'Distributed Systems',
      'Cybersecurity',
      'Mobile App Development',
      'Final Year Project',
      'Industry Internship'
    ]
  },
  skills: [
    'Programming Languages', 'Problem Solving', 'Analytical Thinking',
    'Project Management', 'Technical Communication', 'Teamwork'
  ],
  industries: [
    'Information Technology', 'Manufacturing', 'Automotive',
    'Aerospace', 'Telecommunications', 'Consulting'
  ]
};

interface Props {
  courseId: string;
}

export function CourseDetailPage({ courseId }: Props) {
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'eligibility', label: 'Eligibility' },
    { id: 'syllabus', label: 'Syllabus' },
    { id: 'colleges', label: 'Top Colleges' },
    { id: 'careers', label: 'Career Prospects' },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4">About {mockCourse.name}</h3>
              <p className="text-gray-700 leading-relaxed mb-4">{mockCourse.detailedDescription}</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Key Skills Developed</h4>
                  <div className="space-y-2">
                    {mockCourse.skills.map((skill) => (
                      <div key={skill} className="flex items-center space-x-2">
                        <span className="text-green-500">✓</span>
                        <span className="text-gray-700">{skill}</span>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Industry Sectors</h4>
                  <div className="space-y-2">
                    {mockCourse.industries.map((industry) => (
                      <div key={industry} className="flex items-center space-x-2">
                        <span className="text-blue-500">•</span>
                        <span className="text-gray-700">{industry}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      
      case 'eligibility':
        return (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Eligibility Criteria</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Academic Qualification</h4>
                  <p className="text-gray-700">{mockCourse.eligibility.academic}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Minimum Percentage</h4>
                  <p className="text-gray-700">{mockCourse.eligibility.percentage}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Age Limit</h4>
                  <p className="text-gray-700">{mockCourse.eligibility.ageLimit}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Entrance Examinations</h3>
              <div className="space-y-4">
                {mockCourse.entranceExams.map((exam) => (
                  <div key={exam.name} className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900">{exam.name}</h4>
                    <p className="text-gray-600 text-sm">{exam.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      
      case 'syllabus':
        return (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Course Syllabus</h3>
              <div className="space-y-6">
                {Object.entries(mockCourse.syllabus).map(([year, subjects]) => (
                  <div key={year} className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">{year}</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {subjects.map((subject) => (
                        <div key={subject} className="flex items-center space-x-2">
                          <span className="text-blue-500">•</span>
                          <span className="text-gray-700">{subject}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      
      case 'colleges':
        return (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Top Colleges Offering {mockCourse.shortName}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {mockCourse.topColleges.map((college) => (
                  <div key={college} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                    <h4 className="font-semibold text-gray-900">{college}</h4>
                    <p className="text-gray-600 text-sm">Premier engineering institution</p>
                    <Link href="/colleges" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                      View Details →
                    </Link>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      
      case 'careers':
        return (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Career Opportunities</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {mockCourse.careerOptions.map((career) => (
                  <div key={career} className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <h4 className="font-semibold text-blue-900">{career}</h4>
                    <p className="text-blue-700 text-sm">Excellent growth prospects</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Course Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="flex-1">
              <nav className="flex mb-4" aria-label="Breadcrumb">
                <ol className="flex items-center space-x-4">
                  <li>
                    <Link href="/" className="text-blue-200 hover:text-white text-sm">Home</Link>
                  </li>
                  <li>
                    <svg className="flex-shrink-0 h-4 w-4 text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  </li>
                  <li>
                    <Link href="/courses" className="text-blue-200 hover:text-white text-sm">Courses</Link>
                  </li>
                  <li>
                    <svg className="flex-shrink-0 h-4 w-4 text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  </li>
                  <li>
                    <span className="text-white text-sm font-medium">{mockCourse.shortName}</span>
                  </li>
                </ol>
              </nav>
              
              <h1 className="text-4xl font-bold mb-4">{mockCourse.name}</h1>
              <p className="text-xl text-blue-100 mb-4">{mockCourse.description}</p>
              
              <div className="flex flex-wrap items-center gap-4 text-sm">
                <span className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full">
                  {mockCourse.level}
                </span>
                <span className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full">
                  {mockCourse.duration}
                </span>
                <span className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full">
                  {mockCourse.stream}
                </span>
              </div>
            </div>

            <div className="mt-6 lg:mt-0 lg:ml-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold">{formatCurrency(mockCourse.averageFees)}</div>
                    <div className="text-blue-200 text-sm">Average Fees</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{formatCurrency(mockCourse.averageSalary)}</div>
                    <div className="text-blue-200 text-sm">Average Salary</div>
                  </div>
                </div>
                <button className="w-full mt-4 bg-yellow-500 hover:bg-yellow-600 text-gray-900 py-3 px-6 rounded-lg font-medium transition-colors duration-200">
                  Find Colleges
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b border-gray-200 sticky top-16 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8 overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderTabContent()}
      </div>
    </div>
  );
}
