'use client';

import { useState } from 'react';

interface Filters {
  search: string;
  level: string;
  stream: string;
  duration: string;
  fees: string;
  sortBy: string;
}

interface Props {
  filters: Filters;
  onFiltersChange: (filters: Filters) => void;
}

const levels = [
  'Undergraduate',
  'Postgraduate',
  'Doctoral',
  'Diploma',
  'Certificate',
];

const streams = [
  'Engineering',
  'Medical',
  'Management',
  'Arts',
  'Science',
  'Commerce',
  'Law',
  'Architecture',
  'Pharmacy',
  'Agriculture',
  'Design',
  'Mass Communication',
];

const durations = [
  '1 Year',
  '2 Years',
  '3 Years',
  '4 Years',
  '5+ Years',
];

const feeRanges = [
  'Under ₹1 Lakh',
  '₹1-3 Lakhs',
  '₹3-5 Lakhs',
  '₹5-10 Lakhs',
  '₹10+ Lakhs',
];

const sortOptions = [
  { value: 'popularity', label: 'Popularity' },
  { value: 'fees_low', label: 'Fees: Low to High' },
  { value: 'fees_high', label: 'Fees: High to Low' },
  { value: 'duration', label: 'Duration' },
  { value: 'salary', label: 'Average Salary' },
  { value: 'alphabetical', label: 'Alphabetical' },
];

export function CourseFilters({ filters, onFiltersChange }: Props) {
  const [isExpanded, setIsExpanded] = useState(false);

  const updateFilter = (key: keyof Filters, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      search: '',
      level: '',
      stream: '',
      duration: '',
      fees: '',
      sortBy: 'popularity',
    });
  };

  const hasActiveFilters = Object.entries(filters).some(([key, value]) => 
    key !== 'sortBy' && value !== ''
  );

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* Search */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Search Courses
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            value={filters.search}
            onChange={(e) => updateFilter('search', e.target.value)}
            placeholder="Search by course name..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Sort By */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Sort By
        </label>
        <select
          value={filters.sortBy}
          onChange={(e) => updateFilter('sortBy', e.target.value)}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          {sortOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Mobile Filter Toggle */}
      <div className="lg:hidden mb-4">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full flex items-center justify-between px-4 py-2 bg-gray-50 rounded-md text-sm font-medium text-gray-700"
        >
          <span>Filters</span>
          <svg
            className={`w-5 h-5 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>

      {/* Filters */}
      <div className={`space-y-6 ${!isExpanded ? 'hidden lg:block' : ''}`}>
        {/* Level */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Course Level
          </label>
          <select
            value={filters.level}
            onChange={(e) => updateFilter('level', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Levels</option>
            {levels.map((level) => (
              <option key={level} value={level.toLowerCase()}>
                {level}
              </option>
            ))}
          </select>
        </div>

        {/* Stream */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Stream
          </label>
          <select
            value={filters.stream}
            onChange={(e) => updateFilter('stream', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Streams</option>
            {streams.map((stream) => (
              <option key={stream} value={stream.toLowerCase()}>
                {stream}
              </option>
            ))}
          </select>
        </div>

        {/* Duration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Duration
          </label>
          <select
            value={filters.duration}
            onChange={(e) => updateFilter('duration', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Any Duration</option>
            {durations.map((duration) => (
              <option key={duration} value={duration}>
                {duration}
              </option>
            ))}
          </select>
        </div>

        {/* Fees */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Average Fees
          </label>
          <select
            value={filters.fees}
            onChange={(e) => updateFilter('fees', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Any Range</option>
            {feeRanges.map((range) => (
              <option key={range} value={range}>
                {range}
              </option>
            ))}
          </select>
        </div>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="w-full px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors duration-200"
          >
            Clear All Filters
          </button>
        )}
      </div>

      {/* Quick Filters */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Popular Courses</h4>
        <div className="space-y-2">
          <button className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md">
            Engineering Courses
          </button>
          <button className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md">
            Medical Courses
          </button>
          <button className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md">
            MBA Programs
          </button>
          <button className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md">
            Computer Science
          </button>
          <button className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md">
            Data Science
          </button>
        </div>
      </div>

      {/* Course Predictor */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Course Predictor</h4>
        <p className="text-xs text-gray-600 mb-3">
          Find courses based on your interests and career goals
        </p>
        <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-sm font-medium transition-colors duration-200">
          Take Assessment
        </button>
      </div>
    </div>
  );
}
