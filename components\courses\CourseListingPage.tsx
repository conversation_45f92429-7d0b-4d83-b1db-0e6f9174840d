'use client';

import { useState, useEffect } from 'react';
import { CourseCard } from './CourseCard';
import { CourseFilters } from './CourseFilters';
import { Pagination } from '@/components/ui/Pagination';
import { usePublicCourses, type PublicCourse } from '@/features/api/use-public-courses';

// Adapter function to transform API data to CourseCard expected format
function transformCourseData(course: PublicCourse) {
  return {
    id: course.id,
    name: course.name,
    shortName: course.shortName || course.name.split(' ').map(word => word[0]).join(''),
    level: course.level,
    duration: course.duration || 'Duration not specified',
    stream: course.stream,
    description: course.description || 'Course description not available',
    specializations: [], // This should come from API in future
    eligibility: course.eligibility?.qualification || 'Eligibility criteria not specified',
    entranceExams: course.eligibility?.entranceExams || [],
    averageFees: course.averageFees || 0,
    averageSalary: course.averageSalary || 0,
    topColleges: [], // This should come from API in future
    careerOptions: course.careerProspects?.jobRoles || [],
    isPopular: course.isPopular,
    demandLevel: course.isPopular ? 'High' : 'Medium',
    jobOpportunities: course.careerProspects?.jobRoles?.length ? 'Good' : 'Average',
  };
}

export function CourseListingPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading courses...</span>
          </div>
        </div>
      </div>
    );
  }

  return <CourseListingPageContent />;
}

function CourseListingPageContent() {
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    level: '',
    stream: '',
    duration: '',
    fees: '',
    sortBy: 'name',
  });

  // Fetch courses using the public API
  const { data, isLoading, error } = usePublicCourses({
    page: currentPage,
    limit: 9,
    search: filters.search,
    level: filters.level,
    stream: filters.stream,
    sortBy: filters.sortBy === 'popularity' ? 'name' : filters.sortBy,
    sortOrder: 'asc',
  });

  const courses = data?.data || [];
  const meta = data?.meta;
  const totalPages = meta?.totalPages || 1;

  // Transform course data for CourseCard component
  const transformedCourses = courses.map(transformCourseData);



  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading courses...</span>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="text-red-600 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Courses</h3>
            <p className="text-gray-600 mb-4">{error.message}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Explore Courses</h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover detailed information about various courses, their eligibility criteria, 
              career prospects, and top colleges offering these programs.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="lg:grid lg:grid-cols-4 lg:gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <CourseFilters 
              filters={filters}
              onFiltersChange={setFilters}
            />
          </div>

          {/* Main Content */}
          <div className="mt-8 lg:mt-0 lg:col-span-3">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
              <div>
                <p className="text-sm text-gray-600">
                  Showing {meta ? ((meta.page - 1) * meta.limit + 1) : 1}-{meta ? Math.min(meta.page * meta.limit, meta.total) : transformedCourses.length} of {meta?.total || transformedCourses.length} courses
                </p>
              </div>
            </div>

            {/* Course Grid */}
            {transformedCourses.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No courses found</h3>
                <p className="text-gray-600">Try adjusting your search criteria or filters.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {transformedCourses.map((course) => (
                  <CourseCard
                    key={course.id}
                    course={course}
                  />
                ))}
              </div>
            )}

            {/* Pagination */}
            <div className="mt-12">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Popular Streams Section */}
      <div className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">Popular Streams</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
            {[
              { name: 'Engineering', icon: '⚙️', courses: 150 },
              { name: 'Medical', icon: '🏥', courses: 45 },
              { name: 'Management', icon: '💼', courses: 80 },
              { name: 'Arts', icon: '🎨', courses: 120 },
              { name: 'Science', icon: '🔬', courses: 95 },
              { name: 'Commerce', icon: '💰', courses: 70 },
            ].map((stream) => (
              <div
                key={stream.name}
                className="bg-gray-50 hover:bg-gray-100 rounded-lg p-6 text-center cursor-pointer transition-colors duration-200 border border-gray-200"
              >
                <div className="text-3xl mb-3">{stream.icon}</div>
                <h3 className="font-semibold text-gray-900 mb-1">{stream.name}</h3>
                <p className="text-sm text-gray-600">{stream.courses} courses</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Course Comparison Tool */}
      <div className="bg-blue-50 border-t border-blue-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Compare Courses</h2>
            <p className="text-xl text-gray-600 mb-8">
              Not sure which course to choose? Use our comparison tool to make an informed decision.
            </p>
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200">
              Start Course Comparison
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
