import Link from 'next/link';
import { formatCurrency } from '@/lib/utils';

// Mock data - In real app, this would come from the database
const featuredColleges = [
  {
    id: '1',
    name: 'Indian Institute of Technology Delhi',
    shortName: 'IIT Delhi',
    location: 'New Delhi, Delhi',
    logo: '/placeholder-logo.png',
    bannerImage: '/placeholder-college.jpg',
    rating: 4.8,
    reviewCount: 2450,
    establishedYear: 1961,
    collegeType: 'Government',
    nirfRanking: 2,
    placementPercentage: 95,
    averagePackage: 1800000,
    highestPackage: 5500000,
    totalStudents: 8500,
    accreditations: ['NAAC A++', 'NBA', 'NIRF'],
    topCourses: ['B.Tech', 'M.Tech', 'PhD'],
    isVerified: true,
    isFeatured: true,
  },
  {
    id: '2',
    name: 'All India Institute of Medical Sciences',
    shortName: 'AIIMS Delhi',
    location: 'New Delhi, Delhi',
    logo: '/placeholder-logo.png',
    bannerImage: '/placeholder-college.jpg',
    rating: 4.9,
    reviewCount: 1890,
    establishedYear: 1956,
    collegeType: 'Government',
    nirfRanking: 1,
    placementPercentage: 100,
    averagePackage: 2200000,
    highestPackage: 8000000,
    totalStudents: 3200,
    accreditations: ['NAAC A++', 'MCI'],
    topCourses: ['MBBS', 'MD', 'MS'],
    isVerified: true,
    isFeatured: true,
  },
  {
    id: '3',
    name: 'Indian Institute of Management Ahmedabad',
    shortName: 'IIM Ahmedabad',
    location: 'Ahmedabad, Gujarat',
    logo: '/placeholder-logo.png',
    bannerImage: '/placeholder-college.jpg',
    rating: 4.7,
    reviewCount: 1650,
    establishedYear: 1961,
    collegeType: 'Government',
    nirfRanking: 1,
    placementPercentage: 100,
    averagePackage: 3400000,
    highestPackage: 7000000,
    totalStudents: 1200,
    accreditations: ['NAAC A++', 'AACSB'],
    topCourses: ['MBA', 'PGDM', 'PhD'],
    isVerified: true,
    isFeatured: true,
  },
  {
    id: '4',
    name: 'Birla Institute of Technology and Science',
    shortName: 'BITS Pilani',
    location: 'Pilani, Rajasthan',
    logo: '/placeholder-logo.png',
    bannerImage: '/placeholder-college.jpg',
    rating: 4.6,
    reviewCount: 3200,
    establishedYear: 1964,
    collegeType: 'Private',
    nirfRanking: 25,
    placementPercentage: 92,
    averagePackage: 1600000,
    highestPackage: 4500000,
    totalStudents: 15000,
    accreditations: ['NAAC A', 'NBA'],
    topCourses: ['B.E.', 'M.E.', 'MBA'],
    isVerified: true,
    isFeatured: true,
  },
];

export function FeaturedColleges() {
  return (
    <section className="bg-gray-50 py-16 sm:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
            Featured Colleges
          </h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Discover top-rated colleges across India with excellent academics, placements, and student satisfaction.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2">
          {featuredColleges.map((college) => (
            <div
              key={college.id}
              className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
            >
              {/* College Banner */}
              <div className="relative h-48 bg-gradient-to-r from-blue-500 to-purple-600">
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="absolute top-4 left-4 flex items-center space-x-2">
                  {college.isVerified && (
                    <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                      ✓ Verified
                    </span>
                  )}
                  <span className="bg-yellow-500 text-gray-900 px-2 py-1 rounded-full text-xs font-medium">
                    Featured
                  </span>
                </div>
                <div className="absolute top-4 right-4">
                  <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
                    <div className="flex items-center space-x-1">
                      <span className="text-yellow-500">★</span>
                      <span className="font-semibold text-gray-900">{college.rating}</span>
                      <span className="text-gray-600 text-sm">({college.reviewCount})</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* College Info */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900 mb-1">
                      {college.name}
                    </h3>
                    <p className="text-gray-600 text-sm mb-2">{college.location}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>Est. {college.establishedYear}</span>
                      <span>•</span>
                      <span>{college.collegeType}</span>
                      <span>•</span>
                      <span>NIRF #{college.nirfRanking}</span>
                    </div>
                  </div>
                </div>

                {/* Key Stats */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="bg-blue-50 rounded-lg p-3">
                    <p className="text-blue-600 font-semibold text-sm">Placement Rate</p>
                    <p className="text-blue-900 font-bold">{college.placementPercentage}%</p>
                  </div>
                  <div className="bg-green-50 rounded-lg p-3">
                    <p className="text-green-600 font-semibold text-sm">Avg. Package</p>
                    <p className="text-green-900 font-bold">{formatCurrency(college.averagePackage)}</p>
                  </div>
                </div>

                {/* Accreditations */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2">
                    {college.accreditations.map((accreditation) => (
                      <span
                        key={accreditation}
                        className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs font-medium"
                      >
                        {accreditation}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Top Courses */}
                <div className="mb-6">
                  <p className="text-sm text-gray-600 mb-2">Popular Courses:</p>
                  <div className="flex flex-wrap gap-2">
                    {college.topCourses.map((course) => (
                      <span
                        key={course}
                        className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium"
                      >
                        {course}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <Link
                    href={`/colleges/${college.id}`}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors duration-200"
                  >
                    View Details
                  </Link>
                  <Link
                    href={`/colleges/${college.id}/apply`}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-900 text-center py-2 px-4 rounded-lg font-medium transition-colors duration-200"
                  >
                    Apply Now
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center mt-12">
          <Link
            href="/colleges"
            className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-lg transition-colors duration-200"
          >
            View All Colleges
            <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
}
