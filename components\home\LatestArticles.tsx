import Link from 'next/link';
import { formatDate } from '@/lib/utils';

// Mock data - In real app, this would come from the database
const articles = [
  {
    id: '1',
    title: 'Complete Guide to JEE Main 2024: Preparation Tips and Strategy',
    excerpt: 'Everything you need to know about JEE Main 2024 including exam pattern, syllabus, preparation tips, and important dates.',
    content: 'Detailed guide content...',
    type: 'exam_info',
    category: 'Engineering',
    author: 'Dr. <PERSON><PERSON>',
    publishedAt: '2024-01-15',
    readingTime: 8,
    featuredImage: '/placeholder-article.jpg',
    tags: ['JEE Main', 'Engineering', 'Exam Preparation'],
    viewCount: 15420,
  },
  {
    id: '2',
    title: 'Top 10 Medical Colleges in India 2024: Rankings and Admission Process',
    excerpt: 'Comprehensive list of best medical colleges in India with detailed information about admission process, fees, and placement records.',
    content: 'Detailed article content...',
    type: 'college_review',
    category: 'Medical',
    author: 'Dr. <PERSON><PERSON>',
    publishedAt: '2024-01-12',
    readingTime: 12,
    featuredImage: '/placeholder-article.jpg',
    tags: ['Medical Colleges', 'MBBS', 'Rankings'],
    viewCount: 23150,
  },
  {
    id: '3',
    title: 'Scholarship Opportunities for Engineering Students in 2024',
    excerpt: 'Discover various scholarship programs available for engineering students including government and private scholarships.',
    content: 'Detailed scholarship information...',
    type: 'scholarship_news',
    category: 'Scholarships',
    author: 'Ankit Verma',
    publishedAt: '2024-01-10',
    readingTime: 6,
    featuredImage: '/placeholder-article.jpg',
    tags: ['Scholarships', 'Engineering', 'Financial Aid'],
    viewCount: 18750,
  },
  {
    id: '4',
    title: 'Career Prospects After MBA: Industry Trends and Salary Insights',
    excerpt: 'Explore the career opportunities available after completing MBA and understand the current industry trends and salary expectations.',
    content: 'Career insights content...',
    type: 'career_advice',
    category: 'Management',
    author: 'Sneha Reddy',
    publishedAt: '2024-01-08',
    readingTime: 10,
    featuredImage: '/placeholder-article.jpg',
    tags: ['MBA', 'Career', 'Salary Trends'],
    viewCount: 12890,
  },
  {
    id: '5',
    title: 'How to Choose the Right College: A Complete Decision Framework',
    excerpt: 'Step-by-step guide to help students and parents make informed decisions when choosing the right college for higher education.',
    content: 'Decision framework content...',
    type: 'guide',
    category: 'General',
    author: 'Rahul Gupta',
    publishedAt: '2024-01-05',
    readingTime: 15,
    featuredImage: '/placeholder-article.jpg',
    tags: ['College Selection', 'Admission Guide', 'Decision Making'],
    viewCount: 31200,
  },
  {
    id: '6',
    title: 'NEET 2024: Important Changes and Preparation Strategy',
    excerpt: 'Latest updates about NEET 2024 including pattern changes, new guidelines, and effective preparation strategies for medical aspirants.',
    content: 'NEET preparation content...',
    type: 'exam_info',
    category: 'Medical',
    author: 'Dr. Amit Singh',
    publishedAt: '2024-01-03',
    readingTime: 9,
    featuredImage: '/placeholder-article.jpg',
    tags: ['NEET', 'Medical Entrance', 'Exam Strategy'],
    viewCount: 19650,
  },
];

const categories = [
  { name: 'All', count: articles.length },
  { name: 'Engineering', count: 2 },
  { name: 'Medical', count: 2 },
  { name: 'Management', count: 1 },
  { name: 'Scholarships', count: 1 },
];

export function LatestArticles() {
  const featuredArticles = articles.slice(0, 3);
  const recentArticles = articles.slice(3, 6);

  return (
    <section className="bg-white py-16 sm:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
            Latest Articles & Insights
          </h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Stay updated with the latest trends in education, exam updates, career guidance, and expert insights to make informed decisions.
          </p>
        </div>

        {/* Categories */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <Link
              key={category.name}
              href={`/articles?category=${category.name.toLowerCase()}`}
              className="bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200"
            >
              {category.name} ({category.count})
            </Link>
          ))}
        </div>

        {/* Featured Articles */}
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3 mb-16">
          {featuredArticles.map((article) => (
            <article
              key={article.id}
              className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
            >
              {/* Article Image */}
              <div className="relative h-48 bg-gradient-to-r from-blue-500 to-purple-600">
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="absolute top-4 left-4">
                  <span className="bg-white/90 backdrop-blur-sm text-gray-900 px-3 py-1 rounded-full text-sm font-medium">
                    {article.category}
                  </span>
                </div>
                <div className="absolute bottom-4 left-4 right-4">
                  <h3 className="text-white font-bold text-lg leading-tight">
                    {article.title}
                  </h3>
                </div>
              </div>

              {/* Article Content */}
              <div className="p-6">
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {article.excerpt}
                </p>

                {/* Article Meta */}
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center space-x-4">
                    <span>By {article.author}</span>
                    <span>•</span>
                    <span>{formatDate(article.publishedAt)}</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span>{article.readingTime} min read</span>
                    <span>•</span>
                    <span>{article.viewCount.toLocaleString()} views</span>
                  </div>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {article.tags.slice(0, 2).map((tag) => (
                    <span
                      key={tag}
                      className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium"
                    >
                      {tag}
                    </span>
                  ))}
                  {article.tags.length > 2 && (
                    <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">
                      +{article.tags.length - 2} more
                    </span>
                  )}
                </div>

                {/* Read More Button */}
                <Link
                  href={`/articles/${article.id}`}
                  className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                >
                  Read Full Article
                  <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </Link>
              </div>
            </article>
          ))}
        </div>

        {/* Recent Articles List */}
        <div className="bg-gray-50 rounded-xl p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Recent Articles</h3>
          <div className="space-y-6">
            {recentArticles.map((article) => (
              <div
                key={article.id}
                className="flex flex-col sm:flex-row gap-4 p-4 bg-white rounded-lg hover:shadow-md transition-shadow duration-300"
              >
                <div className="sm:w-32 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex-shrink-0"></div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium">
                      {article.category}
                    </span>
                    <span className="text-gray-500 text-sm">{formatDate(article.publishedAt)}</span>
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2 hover:text-blue-600">
                    <Link href={`/articles/${article.id}`}>
                      {article.title}
                    </Link>
                  </h4>
                  <p className="text-gray-600 text-sm mb-2">{article.excerpt}</p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>By {article.author}</span>
                    <span>{article.readingTime} min read</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* View All Button */}
        <div className="text-center mt-12">
          <Link
            href="/articles"
            className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-lg transition-colors duration-200"
          >
            View All Articles
            <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
}
