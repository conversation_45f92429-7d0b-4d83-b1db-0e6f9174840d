import Link from 'next/link';
import { formatCurrency } from '@/lib/utils';

// Mock data - In real app, this would come from the database
const popularCourses = [
  {
    id: '1',
    name: 'Bachelor of Technology',
    shortName: 'B.Tech',
    level: 'Undergraduate',
    stream: 'Engineering',
    duration: '4 Years',
    averageFee: 400000,
    averageSalary: 800000,
    totalColleges: 3500,
    totalSeats: 150000,
    popularSpecializations: ['Computer Science', 'Mechanical', 'Electrical', 'Civil'],
    entranceExams: ['JEE Main', 'JEE Advanced', 'BITSAT'],
    icon: '🔧',
    description: 'Comprehensive engineering program covering various technical disciplines.',
    jobRoles: ['Software Engineer', 'Mechanical Engineer', 'Civil Engineer'],
    topRecruiters: ['TCS', 'Infosys', 'Microsoft', 'Google'],
  },
  {
    id: '2',
    name: 'Bachelor of Medicine and Bachelor of Surgery',
    shortName: 'MBBS',
    level: 'Undergraduate',
    stream: 'Medical',
    duration: '5.5 Years',
    averageFee: 1200000,
    averageSalary: 1500000,
    totalColleges: 596,
    totalSeats: 83075,
    popularSpecializations: ['General Medicine', 'Surgery', 'Pediatrics', 'Cardiology'],
    entranceExams: ['NEET UG'],
    icon: '🩺',
    description: 'Professional medical degree to become a licensed doctor.',
    jobRoles: ['Doctor', 'Surgeon', 'Medical Officer'],
    topRecruiters: ['AIIMS', 'Apollo', 'Fortis', 'Max Healthcare'],
  },
  {
    id: '3',
    name: 'Master of Business Administration',
    shortName: 'MBA',
    level: 'Postgraduate',
    stream: 'Management',
    duration: '2 Years',
    averageFee: 800000,
    averageSalary: 1200000,
    totalColleges: 5000,
    totalSeats: 400000,
    popularSpecializations: ['Finance', 'Marketing', 'HR', 'Operations'],
    entranceExams: ['CAT', 'XAT', 'GMAT', 'MAT'],
    icon: '💼',
    description: 'Advanced business management program for leadership roles.',
    jobRoles: ['Manager', 'Consultant', 'Business Analyst'],
    topRecruiters: ['McKinsey', 'BCG', 'Deloitte', 'KPMG'],
  },
  {
    id: '4',
    name: 'Bachelor of Computer Applications',
    shortName: 'BCA',
    level: 'Undergraduate',
    stream: 'Computer Applications',
    duration: '3 Years',
    averageFee: 200000,
    averageSalary: 500000,
    totalColleges: 2000,
    totalSeats: 100000,
    popularSpecializations: ['Software Development', 'Web Development', 'Data Science'],
    entranceExams: ['CUET', 'IPU CET', 'SET'],
    icon: '💻',
    description: 'Computer applications program focusing on software development.',
    jobRoles: ['Software Developer', 'Web Developer', 'System Analyst'],
    topRecruiters: ['Wipro', 'Cognizant', 'HCL', 'Tech Mahindra'],
  },
  {
    id: '5',
    name: 'Bachelor of Science',
    shortName: 'B.Sc',
    level: 'Undergraduate',
    stream: 'Science',
    duration: '3 Years',
    averageFee: 150000,
    averageSalary: 400000,
    totalColleges: 4000,
    totalSeats: 200000,
    popularSpecializations: ['Physics', 'Chemistry', 'Mathematics', 'Biology'],
    entranceExams: ['CUET', 'State CETs'],
    icon: '🔬',
    description: 'Science degree program with various specialization options.',
    jobRoles: ['Research Scientist', 'Lab Technician', 'Teacher'],
    topRecruiters: ['ISRO', 'DRDO', 'Research Labs', 'Pharma Companies'],
  },
  {
    id: '6',
    name: 'Bachelor of Arts',
    shortName: 'BA',
    level: 'Undergraduate',
    stream: 'Arts',
    duration: '3 Years',
    averageFee: 100000,
    averageSalary: 350000,
    totalColleges: 3000,
    totalSeats: 180000,
    popularSpecializations: ['English', 'History', 'Political Science', 'Economics'],
    entranceExams: ['CUET', 'DU Entrance'],
    icon: '📚',
    description: 'Liberal arts program covering humanities and social sciences.',
    jobRoles: ['Content Writer', 'Journalist', 'Civil Servant'],
    topRecruiters: ['Media Houses', 'Government', 'NGOs', 'Publishing'],
  },
];

export function PopularCourses() {
  return (
    <section className="bg-white py-16 sm:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
            Popular Courses
          </h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Explore the most sought-after courses across different streams with detailed information about career prospects and opportunities.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {popularCourses.map((course) => (
            <div
              key={course.id}
              className="bg-white border border-gray-200 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden"
            >
              {/* Course Header */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 border-b border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <div className="text-4xl">{course.icon}</div>
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                    {course.level}
                  </span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {course.name}
                </h3>
                <p className="text-gray-600 text-sm mb-3">{course.description}</p>
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>{course.duration}</span>
                  <span>{course.stream}</span>
                </div>
              </div>

              {/* Course Details */}
              <div className="p-6">
                {/* Key Stats */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">{course.totalColleges.toLocaleString()}</p>
                    <p className="text-sm text-gray-600">Colleges</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">{course.totalSeats.toLocaleString()}</p>
                    <p className="text-sm text-gray-600">Total Seats</p>
                  </div>
                </div>

                {/* Financial Info */}
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Avg. Fee</p>
                      <p className="font-semibold text-gray-900">{formatCurrency(course.averageFee)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Avg. Salary</p>
                      <p className="font-semibold text-gray-900">{formatCurrency(course.averageSalary)}</p>
                    </div>
                  </div>
                </div>

                {/* Specializations */}
                <div className="mb-6">
                  <p className="text-sm font-medium text-gray-900 mb-2">Popular Specializations:</p>
                  <div className="flex flex-wrap gap-2">
                    {course.popularSpecializations.slice(0, 3).map((spec) => (
                      <span
                        key={spec}
                        className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs"
                      >
                        {spec}
                      </span>
                    ))}
                    {course.popularSpecializations.length > 3 && (
                      <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">
                        +{course.popularSpecializations.length - 3} more
                      </span>
                    )}
                  </div>
                </div>

                {/* Entrance Exams */}
                <div className="mb-6">
                  <p className="text-sm font-medium text-gray-900 mb-2">Entrance Exams:</p>
                  <div className="flex flex-wrap gap-2">
                    {course.entranceExams.map((exam) => (
                      <span
                        key={exam}
                        className="bg-yellow-100 text-yellow-700 px-2 py-1 rounded text-xs font-medium"
                      >
                        {exam}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <Link
                    href={`/courses/${course.id}`}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors duration-200"
                  >
                    View Details
                  </Link>
                  <Link
                    href={`/colleges?course=${course.id}`}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-900 text-center py-2 px-4 rounded-lg font-medium transition-colors duration-200"
                  >
                    Find Colleges
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center mt-12">
          <Link
            href="/courses"
            className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-lg transition-colors duration-200"
          >
            Explore All Courses
            <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
}
