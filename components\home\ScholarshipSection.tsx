import Link from 'next/link';
import { formatCurrency } from '@/lib/utils';

// Mock data - In real app, this would come from the database
const scholarships = [
  {
    id: '1',
    name: 'CollegeCampus Merit Scholarship',
    amount: 100000,
    totalSlots: 500,
    availableSlots: 342,
    type: 'Merit Based',
    eligibility: 'Minimum 85% in 12th grade',
    applicationDeadline: '2024-06-30',
    description: 'Scholarship for academically excellent students pursuing higher education.',
    benefits: ['Full tuition fee coverage', 'Monthly stipend', 'Book allowance'],
    isCollegeCampusScholarship: true,
  },
  {
    id: '2',
    name: 'Women in STEM Scholarship',
    amount: 75000,
    totalSlots: 200,
    availableSlots: 156,
    type: 'Women Empowerment',
    eligibility: 'Female students in Science/Engineering',
    applicationDeadline: '2024-07-15',
    description: 'Supporting women pursuing careers in Science, Technology, Engineering, and Mathematics.',
    benefits: ['Partial fee coverage', 'Mentorship program', 'Industry exposure'],
    isCollegeCampusScholarship: true,
  },
  {
    id: '3',
    name: 'Rural Student Support Scholarship',
    amount: 50000,
    totalSlots: 1000,
    availableSlots: 789,
    type: 'Need Based',
    eligibility: 'Students from rural areas with family income < ₹3 LPA',
    applicationDeadline: '2024-08-01',
    description: 'Financial assistance for deserving students from rural backgrounds.',
    benefits: ['Fee assistance', 'Laptop/tablet', 'Career guidance'],
    isCollegeCampusScholarship: true,
  },
];

const scholarshipStats = [
  {
    label: 'Total Scholarships',
    value: '₹50 Cr+',
    description: 'Distributed to students',
  },
  {
    label: 'Students Benefited',
    value: '25,000+',
    description: 'Across India',
  },
  {
    label: 'Success Rate',
    value: '78%',
    description: 'Application approval',
  },
  {
    label: 'Partner Organizations',
    value: '150+',
    description: 'Funding scholarships',
  },
];

export function ScholarshipSection() {
  return (
    <section className="bg-gradient-to-br from-green-50 to-blue-50 py-16 sm:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
            CollegeCampus Scholarships
          </h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            We believe education should be accessible to all. Apply for our scholarships and get financial support for your higher education journey.
          </p>
        </div>

        {/* Scholarship Stats */}
        <div className="grid grid-cols-2 gap-6 lg:grid-cols-4 mb-12">
          {scholarshipStats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="bg-white rounded-lg p-6 shadow-lg">
                <p className="text-2xl font-bold text-green-600 mb-1">{stat.value}</p>
                <p className="text-sm font-medium text-gray-900 mb-1">{stat.label}</p>
                <p className="text-xs text-gray-500">{stat.description}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Featured Scholarships */}
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3 mb-12">
          {scholarships.map((scholarship) => (
            <div
              key={scholarship.id}
              className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
            >
              {/* Scholarship Header */}
              <div className="bg-gradient-to-r from-green-500 to-blue-500 p-6 text-white">
                <div className="flex items-center justify-between mb-4">
                  <span className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium">
                    {scholarship.type}
                  </span>
                  <div className="text-right">
                    <p className="text-2xl font-bold">{formatCurrency(scholarship.amount)}</p>
                    <p className="text-sm opacity-90">Award Amount</p>
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-2">{scholarship.name}</h3>
                <p className="text-sm opacity-90">{scholarship.description}</p>
              </div>

              {/* Scholarship Details */}
              <div className="p-6">
                {/* Availability */}
                <div className="mb-6">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">Available Slots</span>
                    <span className="text-sm font-bold text-green-600">
                      {scholarship.availableSlots} / {scholarship.totalSlots}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full"
                      style={{
                        width: `${(scholarship.availableSlots / scholarship.totalSlots) * 100}%`,
                      }}
                    ></div>
                  </div>
                </div>

                {/* Eligibility */}
                <div className="mb-6">
                  <p className="text-sm font-medium text-gray-700 mb-2">Eligibility:</p>
                  <p className="text-sm text-gray-600">{scholarship.eligibility}</p>
                </div>

                {/* Benefits */}
                <div className="mb-6">
                  <p className="text-sm font-medium text-gray-700 mb-2">Benefits:</p>
                  <ul className="space-y-1">
                    {scholarship.benefits.map((benefit, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Deadline */}
                <div className="mb-6">
                  <p className="text-sm font-medium text-gray-700">Application Deadline:</p>
                  <p className="text-sm text-red-600 font-medium">
                    {new Date(scholarship.applicationDeadline).toLocaleDateString('en-IN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <Link
                    href={`/scholarships/${scholarship.id}`}
                    className="flex-1 bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors duration-200"
                  >
                    Apply Now
                  </Link>
                  <Link
                    href={`/scholarships/${scholarship.id}/details`}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-900 text-center py-2 px-4 rounded-lg font-medium transition-colors duration-200"
                  >
                    Learn More
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="bg-white rounded-xl shadow-lg p-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Don&apos;t Let Financial Constraints Stop Your Dreams
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Our scholarship program has helped thousands of students achieve their educational goals.
            Join our community and get the financial support you need to succeed.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/scholarships"
              className="bg-green-600 hover:bg-green-700 text-white font-semibold px-8 py-3 rounded-lg transition-colors duration-200"
            >
              View All Scholarships
            </Link>
            <Link
              href="/scholarships/eligibility-checker"
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-lg transition-colors duration-200"
            >
              Check Eligibility
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
