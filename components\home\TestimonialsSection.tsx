'use client';

import { useState } from 'react';

// Mock data - In real app, this would come from the database
const testimonials = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    course: 'B.Tech Computer Science',
    college: 'IIT Delhi',
    year: '2023',
    rating: 5,
    content: 'CollegeCampus helped me find the perfect engineering college. The detailed information about placements and faculty made my decision easy. I got into my dream college and received a scholarship too!',
    image: '/placeholder-avatar.jpg',
    location: 'Mumbai, Maharashtra',
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    course: 'MBA Finance',
    college: 'IIM Bangalore',
    year: '2023',
    rating: 5,
    content: 'The platform provided comprehensive details about MBA programs across India. The review system helped me understand the real student experience. Highly recommend for anyone looking for management courses.',
    image: '/placeholder-avatar.jpg',
    location: 'Delhi, NCR',
  },
  {
    id: 3,
    name: '<PERSON><PERSON>',
    course: 'MBBS',
    college: 'AIIMS Delhi',
    year: '2022',
    rating: 5,
    content: 'Finding the right medical college was overwhelming until I discovered CollegeCampus. The detailed comparison features and authentic reviews from seniors made my choice clear. Thank you for this amazing platform!',
    image: '/placeholder-avatar.jpg',
    location: 'Ahmedabad, Gujarat',
  },
  {
    id: 4,
    name: '<PERSON><PERSON><PERSON>',
    course: 'B.Sc Agriculture',
    college: 'Punjab Agricultural University',
    year: '2023',
    rating: 4,
    content: 'As a student from a rural background, CollegeCampus scholarship program changed my life. Not only did I find a great college, but I also received financial support that made my education possible.',
    image: '/placeholder-avatar.jpg',
    location: 'Patna, Bihar',
  },
  {
    id: 5,
    name: 'Sneha Reddy',
    course: 'B.Des Fashion Design',
    college: 'NIFT Delhi',
    year: '2023',
    rating: 5,
    content: 'The platform has information about courses I never knew existed. The career guidance section helped me choose fashion design, and now I\'m pursuing my passion at one of the top design institutes.',
    image: '/placeholder-avatar.jpg',
    location: 'Hyderabad, Telangana',
  },
  {
    id: 6,
    name: 'Arjun Mehta',
    course: 'BCA',
    college: 'Christ University',
    year: '2022',
    rating: 4,
    content: 'The detailed course information and placement statistics helped me make an informed decision. The Q&A section where I could ask current students was particularly helpful.',
    image: '/placeholder-avatar.jpg',
    location: 'Bangalore, Karnataka',
  },
];

export function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const testimonialsPerPage = 3;
  const totalPages = Math.ceil(testimonials.length / testimonialsPerPage);

  const nextTestimonials = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % totalPages);
  };

  const prevTestimonials = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + totalPages) % totalPages);
  };

  const currentTestimonials = testimonials.slice(
    currentIndex * testimonialsPerPage,
    (currentIndex + 1) * testimonialsPerPage
  );

  return (
    <section className="bg-gray-50 py-16 sm:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
            What Our Students Say
          </h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Hear from thousands of students who found their perfect colleges and achieved their dreams through CollegeCampus.
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="relative">
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {currentTestimonials.map((testimonial) => (
              <div
                key={testimonial.id}
                className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
              >
                {/* Rating */}
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      className={`w-5 h-5 ${
                        i < testimonial.rating ? 'text-yellow-400' : 'text-gray-300'
                      }`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                    </svg>
                  ))}
                </div>

                {/* Content */}
                <blockquote className="text-gray-700 mb-6 leading-relaxed">
                  &ldquo;{testimonial.content}&rdquo;
                </blockquote>

                {/* Student Info */}
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-blue-600 font-semibold text-lg">
                      {testimonial.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                    <p className="text-sm text-gray-600">{testimonial.course}</p>
                    <p className="text-sm text-gray-500">{testimonial.college} • {testimonial.year}</p>
                    <p className="text-xs text-gray-400">{testimonial.location}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevTestimonials}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow duration-300"
            disabled={totalPages <= 1}
          >
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={nextTestimonials}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow duration-300"
            disabled={totalPages <= 1}
          >
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        {/* Pagination Dots */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-8 space-x-2">
            {[...Array(totalPages)].map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-colors duration-300 ${
                  index === currentIndex ? 'bg-blue-600' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        )}

        {/* Bottom Stats */}
        <div className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3 text-center">
          <div>
            <p className="text-3xl font-bold text-blue-600">4.8/5</p>
            <p className="text-gray-600">Average Rating</p>
          </div>
          <div>
            <p className="text-3xl font-bold text-green-600">95%</p>
            <p className="text-gray-600">Student Satisfaction</p>
          </div>
          <div>
            <p className="text-3xl font-bold text-purple-600">1,00,000+</p>
            <p className="text-gray-600">Success Stories</p>
          </div>
        </div>
      </div>
    </section>
  );
}
