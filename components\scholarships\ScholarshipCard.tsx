import { formatCurrency } from '@/lib/utils';

interface Scholarship {
  id: string;
  name: string;
  provider: string;
  type: string;
  amount: number;
  amountType: string;
  level: string;
  eligibility: string;
  deadline: string;
  description: string;
  benefits: string[];
  isActive: boolean;
  category: string;
  targetGroup: string;
  renewability: string;
}

interface Props {
  scholarship: Scholarship;
}

export function ScholarshipCard({ scholarship }: Props) {
  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'merit-based':
        return 'bg-blue-100 text-blue-800';
      case 'need-based':
        return 'bg-green-100 text-green-800';
      case 'sports':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'government':
        return 'bg-purple-100 text-purple-800';
      case 'private':
        return 'bg-indigo-100 text-indigo-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDeadline = (deadline: string) => {
    const date = new Date(deadline);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const getDaysLeft = (deadline: string) => {
    const today = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysLeft = getDaysLeft(scholarship.deadline);

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-300 overflow-hidden">
      {/* Header */}
      <div className="relative bg-gradient-to-r from-green-500 to-blue-500 p-6 text-white">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(scholarship.type)}`}>
                {scholarship.type}
              </span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(scholarship.category)}`}>
                {scholarship.category}
              </span>
            </div>
            <h3 className="text-xl font-bold mb-1">{scholarship.name}</h3>
            <p className="text-green-100 text-sm">{scholarship.provider}</p>
          </div>
        </div>
        
        {/* Amount */}
        <div className="mt-4 bg-white/10 backdrop-blur-sm rounded-lg p-3">
          <div className="text-center">
            <div className="text-2xl font-bold">{formatCurrency(scholarship.amount)}</div>
            <div className="text-green-200 text-sm">{scholarship.amountType}</div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Description */}
        <p className="text-gray-700 text-sm mb-4 line-clamp-2">{scholarship.description}</p>

        {/* Key Details */}
        <div className="space-y-3 mb-4">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Level:</span>
            <span className="font-medium text-gray-900">{scholarship.level}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Target Group:</span>
            <span className="font-medium text-gray-900">{scholarship.targetGroup}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Renewable:</span>
            <span className="font-medium text-gray-900">{scholarship.renewability}</span>
          </div>
        </div>

        {/* Deadline */}
        <div className="mb-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Application Deadline:</span>
            <div className="text-right">
              <div className="font-semibold text-gray-900">{formatDeadline(scholarship.deadline)}</div>
              <div className={`text-xs ${
                daysLeft <= 7 ? 'text-red-600' : 
                daysLeft <= 30 ? 'text-orange-600' : 
                'text-green-600'
              }`}>
                {daysLeft > 0 ? `${daysLeft} days left` : 'Deadline passed'}
              </div>
            </div>
          </div>
        </div>

        {/* Benefits */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-900 mb-2">Benefits:</h4>
          <div className="space-y-1">
            {scholarship.benefits.slice(0, 3).map((benefit) => (
              <div key={benefit} className="flex items-center space-x-2">
                <span className="text-green-500 text-xs">✓</span>
                <span className="text-xs text-gray-600">{benefit}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Eligibility Preview */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-900 mb-2">Eligibility:</h4>
          <p className="text-xs text-gray-600 line-clamp-2">{scholarship.eligibility}</p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button 
            className={`w-full py-3 px-4 rounded-lg font-medium transition-colors duration-200 ${
              scholarship.isActive && daysLeft > 0
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
            disabled={!scholarship.isActive || daysLeft <= 0}
          >
            {scholarship.isActive && daysLeft > 0 ? 'Apply Now' : 'Application Closed'}
          </button>
          <div className="grid grid-cols-2 gap-2">
            <button className="bg-gray-100 hover:bg-gray-200 text-gray-900 py-2 px-3 rounded-lg text-sm font-medium transition-colors duration-200">
              View Details
            </button>
            <button className="bg-blue-100 hover:bg-blue-200 text-blue-900 py-2 px-3 rounded-lg text-sm font-medium transition-colors duration-200">
              Save
            </button>
          </div>
        </div>
      </div>

      {/* Status Indicator */}
      {daysLeft <= 7 && daysLeft > 0 && (
        <div className="bg-red-50 border-t border-red-200 px-6 py-3">
          <div className="flex items-center space-x-2">
            <span className="text-red-500">⚠️</span>
            <span className="text-red-700 text-sm font-medium">Deadline approaching!</span>
          </div>
        </div>
      )}
    </div>
  );
}
