'use client';

import { useState } from 'react';
import { ScholarshipCard } from './ScholarshipCard';
import { ScholarshipFilters } from './ScholarshipFilters';
import { Pagination } from '@/components/ui/Pagination';

// Mock scholarships data
const mockScholarships = [
  {
    id: '1',
    name: 'National Merit Scholarship',
    provider: 'Government of India',
    type: 'Merit-based',
    amount: 50000,
    amountType: 'Annual',
    level: 'Undergraduate',
    eligibility: 'Minimum 85% in 12th grade, Family income below ₹8 LPA',
    deadline: '2024-06-30',
    description: 'Merit-based scholarship for academically excellent students from economically weaker sections.',
    benefits: ['Tuition fee coverage', 'Monthly stipend', 'Book allowance'],
    applicationProcess: 'Online application through National Scholarship Portal',
    documentsRequired: ['Income certificate', 'Mark sheets', 'Caste certificate (if applicable)'],
    isActive: true,
    category: 'Government',
    targetGroup: 'General',
    renewability: 'Renewable for 4 years',
  },
  {
    id: '2',
    name: 'Women in STEM Scholarship',
    provider: 'Tech Foundation',
    type: 'Merit-based',
    amount: 100000,
    amountType: 'One-time',
    level: 'Undergraduate',
    eligibility: 'Female students pursuing Engineering/Science, Minimum 80% marks',
    deadline: '2024-07-15',
    description: 'Encouraging women to pursue careers in Science, Technology, Engineering, and Mathematics.',
    benefits: ['Full tuition coverage', 'Laptop allowance', 'Mentorship program'],
    applicationProcess: 'Online application with essay submission',
    documentsRequired: ['Academic transcripts', 'Statement of purpose', 'Recommendation letters'],
    isActive: true,
    category: 'Private',
    targetGroup: 'Women',
    renewability: 'Non-renewable',
  },
  {
    id: '3',
    name: 'SC/ST Education Support',
    provider: 'Ministry of Social Justice',
    type: 'Need-based',
    amount: 75000,
    amountType: 'Annual',
    level: 'Both',
    eligibility: 'SC/ST category students, Family income below ₹2.5 LPA',
    deadline: '2024-08-31',
    description: 'Financial assistance for SC/ST students to pursue higher education.',
    benefits: ['Tuition fee reimbursement', 'Hostel charges', 'Maintenance allowance'],
    applicationProcess: 'Apply through state scholarship portals',
    documentsRequired: ['Caste certificate', 'Income certificate', 'Bank details'],
    isActive: true,
    category: 'Government',
    targetGroup: 'SC/ST',
    renewability: 'Renewable based on performance',
  },
];

export function ScholarshipListingPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    type: '',
    level: '',
    category: '',
    amount: '',
    deadline: '',
    sortBy: 'deadline',
  });

  const itemsPerPage = 9;
  const totalPages = Math.ceil(mockScholarships.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentScholarships = mockScholarships.slice(startIndex, startIndex + itemsPerPage);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Find Scholarships</h1>
            <p className="text-xl text-green-100 max-w-3xl mx-auto">
              Discover financial aid opportunities to support your educational journey.
              Browse merit-based, need-based, and specialized scholarships.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="lg:grid lg:grid-cols-4 lg:gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <ScholarshipFilters
              filters={filters}
              onFiltersChange={setFilters}
            />
          </div>

          {/* Main Content */}
          <div className="mt-8 lg:mt-0 lg:col-span-3">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
              <div>
                <p className="text-sm text-gray-600">
                  Showing {startIndex + 1}-{Math.min(startIndex + itemsPerPage, mockScholarships.length)} of {mockScholarships.length} scholarships
                </p>
              </div>
            </div>

            {/* Scholarship Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {currentScholarships.map((scholarship) => (
                <ScholarshipCard
                  key={scholarship.id}
                  scholarship={scholarship}
                />
              ))}
            </div>

            {/* Pagination */}
            <div className="mt-12">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Scholarship Tips Section */}
      <div className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">Scholarship Application Tips</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📝</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Start Early</h3>
              <p className="text-gray-600">Begin your scholarship search and applications well before deadlines to ensure you don&apos;t miss opportunities.</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📋</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Prepare Documents</h3>
              <p className="text-gray-600">Keep all required documents ready including transcripts, certificates, and recommendation letters.</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">✨</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Personalize Applications</h3>
              <p className="text-gray-600">Tailor your essays and applications to match the specific requirements and values of each scholarship.</p>
            </div>
          </div>
        </div>
      </div>

      {/* Scholarship Categories */}
      <div className="bg-gray-50 border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">Scholarship Categories</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { name: 'Merit-Based', icon: '🏆', description: 'For academic excellence' },
              { name: 'Need-Based', icon: '🤝', description: 'For financial assistance' },
              { name: 'Sports', icon: '⚽', description: 'For athletic achievements' },
              { name: 'Minority', icon: '🌍', description: 'For underrepresented groups' },
            ].map((category) => (
              <div
                key={category.name}
                className="bg-white rounded-lg p-6 text-center shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200"
              >
                <div className="text-3xl mb-3">{category.icon}</div>
                <h3 className="font-semibold text-gray-900 mb-2">{category.name}</h3>
                <p className="text-sm text-gray-600">{category.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
