'use client';

import { useSearchParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import Link from 'next/link';

// Mock search results
const mockResults = {
  colleges: [
    {
      id: '1',
      type: 'college',
      title: 'Indian Institute of Technology Delhi',
      description: 'Premier engineering institution offering undergraduate and postgraduate programs',
      location: 'New Delhi, Delhi',
      rating: 4.8,
      url: '/colleges/1',
    },
    {
      id: '2',
      type: 'college',
      title: 'All India Institute of Medical Sciences',
      description: 'Top medical college offering MBBS, MD, and other medical programs',
      location: 'New Delhi, Delhi',
      rating: 4.9,
      url: '/colleges/2',
    },
  ],
  courses: [
    {
      id: '1',
      type: 'course',
      title: 'Bachelor of Technology (B.Tech)',
      description: '4-year undergraduate engineering program with multiple specializations',
      level: 'Undergraduate',
      duration: '4 Years',
      url: '/courses/1',
    },
    {
      id: '2',
      type: 'course',
      title: 'Master of Business Administration (MBA)',
      description: '2-year postgraduate management program',
      level: 'Postgraduate',
      duration: '2 Years',
      url: '/courses/2',
    },
  ],
  scholarships: [
    {
      id: '1',
      type: 'scholarship',
      title: 'National Merit Scholarship',
      description: 'Merit-based scholarship for academically excellent students',
      amount: '₹50,000',
      provider: 'Government of India',
      url: '/scholarships',
    },
  ],
};

export function SearchResultsPage() {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  const [activeTab, setActiveTab] = useState('all');
  const [results, setResults] = useState(mockResults);

  useEffect(() => {
    // In a real app, this would fetch search results from an API
    // For now, we'll just filter mock data based on the query
    if (query) {
      // Simulate search filtering
      console.log('Searching for:', query);
    }
  }, [query]);

  const allResults = [
    ...results.colleges,
    ...results.courses,
    ...results.scholarships,
  ];

  const getResultsByType = (type: string) => {
    switch (type) {
      case 'colleges':
        return results.colleges;
      case 'courses':
        return results.courses;
      case 'scholarships':
        return results.scholarships;
      default:
        return allResults;
    }
  };

  const currentResults = getResultsByType(activeTab);

  const tabs = [
    { id: 'all', label: 'All', count: allResults.length },
    { id: 'colleges', label: 'Colleges', count: results.colleges.length },
    { id: 'courses', label: 'Courses', count: results.courses.length },
    { id: 'scholarships', label: 'Scholarships', count: results.scholarships.length },
  ];

  const renderResultCard = (result: any) => {
    const getTypeIcon = (type: string) => {
      switch (type) {
        case 'college':
          return '🏫';
        case 'course':
          return '📚';
        case 'scholarship':
          return '💰';
        default:
          return '📄';
      }
    };

    const getTypeColor = (type: string) => {
      switch (type) {
        case 'college':
          return 'bg-blue-100 text-blue-800';
        case 'course':
          return 'bg-green-100 text-green-800';
        case 'scholarship':
          return 'bg-purple-100 text-purple-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    };

    return (
      <div key={result.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
              <span className="text-2xl">{getTypeIcon(result.type)}</span>
            </div>
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(result.type)}`}>
                {result.type.charAt(0).toUpperCase() + result.type.slice(1)}
              </span>
              {result.rating && (
                <div className="flex items-center space-x-1">
                  <span className="text-yellow-400">★</span>
                  <span className="text-sm font-medium text-gray-900">{result.rating}</span>
                </div>
              )}
            </div>
            
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              <Link href={result.url} className="hover:text-blue-600">
                {result.title}
              </Link>
            </h3>
            
            <p className="text-gray-600 text-sm mb-3">{result.description}</p>
            
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
              {result.location && (
                <span className="flex items-center space-x-1">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  <span>{result.location}</span>
                </span>
              )}
              {result.level && (
                <span>{result.level}</span>
              )}
              {result.duration && (
                <span>{result.duration}</span>
              )}
              {result.amount && (
                <span className="font-medium text-green-600">{result.amount}</span>
              )}
              {result.provider && (
                <span>{result.provider}</span>
              )}
            </div>
          </div>
          
          <div className="flex-shrink-0">
            <Link
              href={result.url}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
            >
              View Details
            </Link>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Search Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Search Results {query && `for "${query}"`}
              </h1>
              <p className="mt-1 text-gray-600">
                Found {allResults.length} results
              </p>
            </div>
            
            {/* Search Box */}
            <div className="mt-4 lg:mt-0 lg:ml-8">
              <div className="relative max-w-md">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  defaultValue={query}
                  placeholder="Search colleges, courses..."
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filter Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8 overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label} ({tab.count})
              </button>
            ))}
          </nav>
        </div>

        {/* Results */}
        {currentResults.length > 0 ? (
          <div className="space-y-6">
            {currentResults.map(renderResultCard)}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
            <p className="text-gray-600 mb-6">
              Try adjusting your search terms or browse our categories below.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link
                href="/colleges"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
              >
                Browse Colleges
              </Link>
              <Link
                href="/courses"
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
              >
                Browse Courses
              </Link>
              <Link
                href="/scholarships"
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
              >
                Browse Scholarships
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
