import { pgTable, uuid, varchar, text, timestamp, boolean, integer, pgEnum, jsonb } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { users } from './users';

// Article status enum
export const articleStatusEnum = pgEnum('article_status', [
  'draft',
  'published',
  'archived',
  'scheduled'
]);

// Article type enum
export const articleTypeEnum = pgEnum('article_type', [
  'blog_post',
  'news',
  'guide',
  'exam_info',
  'career_advice',
  'admission_tips',
  'scholarship_news',
  'college_review',
  'course_info'
]);

// Article categories table
export const articleCategories = pgTable('article_categories', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull(),
  slug: varchar('slug', { length: 100 }).notNull().unique(),
  description: text('description'),
  color: varchar('color', { length: 7 }), // Hex color code
  icon: varchar('icon', { length: 100 }),
  sortOrder: integer('sort_order').notNull().default(0),
  isActive: boolean('is_active').notNull().default(true),
  
  // SEO
  metaTitle: varchar('meta_title', { length: 255 }),
  metaDescription: text('meta_description'),
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Article tags table
export const articleTags = pgTable('article_tags', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 50 }).notNull(),
  slug: varchar('slug', { length: 50 }).notNull().unique(),
  description: text('description'),
  color: varchar('color', { length: 7 }), // Hex color code
  usageCount: integer('usage_count').notNull().default(0),
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// Articles table
export const articles = pgTable('articles', {
  id: uuid('id').primaryKey().defaultRandom(),
  title: varchar('title', { length: 255 }).notNull(),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  excerpt: text('excerpt'),
  content: text('content').notNull(),
  
  // Article Classification
  type: articleTypeEnum('type').notNull().default('blog_post'),
  categoryId: uuid('category_id').references(() => articleCategories.id),
  
  // Media
  featuredImage: text('featured_image'),
  featuredImageAlt: varchar('featured_image_alt', { length: 255 }),
  gallery: jsonb('gallery'), // Array of image objects
  
  // Author Information
  authorId: uuid('author_id').notNull().references(() => users.id),
  authorName: varchar('author_name', { length: 255 }), // Override author display name
  authorBio: text('author_bio'),
  authorImage: text('author_image'),
  
  // Content Metadata
  readingTime: integer('reading_time'), // Estimated reading time in minutes
  wordCount: integer('word_count'),
  tableOfContents: jsonb('table_of_contents'), // Array of heading objects
  
  // SEO
  metaTitle: varchar('meta_title', { length: 255 }),
  metaDescription: text('meta_description'),
  keywords: text('keywords'),
  canonicalUrl: varchar('canonical_url', { length: 255 }),
  
  // Social Media
  socialTitle: varchar('social_title', { length: 255 }),
  socialDescription: text('social_description'),
  socialImage: text('social_image'),
  
  // Publishing
  status: articleStatusEnum('status').notNull().default('draft'),
  publishedAt: timestamp('published_at'),
  scheduledAt: timestamp('scheduled_at'),
  
  // Engagement
  viewCount: integer('view_count').notNull().default(0),
  likeCount: integer('like_count').notNull().default(0),
  shareCount: integer('share_count').notNull().default(0),
  commentCount: integer('comment_count').notNull().default(0),
  
  // Features
  isFeatured: boolean('is_featured').notNull().default(false),
  isSticky: boolean('is_sticky').notNull().default(false),
  allowComments: boolean('allow_comments').notNull().default(true),
  
  // Related Content
  relatedArticles: jsonb('related_articles'), // Array of article IDs
  relatedColleges: jsonb('related_colleges'), // Array of college IDs
  relatedCourses: jsonb('related_courses'), // Array of course IDs
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Article tag mapping (many-to-many)
export const articleTagMapping = pgTable('article_tag_mapping', {
  id: uuid('id').primaryKey().defaultRandom(),
  articleId: uuid('article_id').notNull().references(() => articles.id, { onDelete: 'cascade' }),
  tagId: uuid('tag_id').notNull().references(() => articleTags.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// Article comments
export const articleComments: any = pgTable('article_comments', {
  id: uuid('id').primaryKey().defaultRandom(),
  articleId: uuid('article_id').notNull().references(() => articles.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'set null' }),
  parentId: uuid('parent_id').references((): any => articleComments.id), // For nested comments
  
  // Comment Content
  content: text('content').notNull(),
  authorName: varchar('author_name', { length: 255 }).notNull(),
  authorEmail: varchar('author_email', { length: 255 }).notNull(),
  authorWebsite: varchar('author_website', { length: 255 }),
  
  // Moderation
  isApproved: boolean('is_approved').notNull().default(false),
  isSpam: boolean('is_spam').notNull().default(false),
  moderatedBy: uuid('moderated_by').references(() => users.id),
  moderatedAt: timestamp('moderated_at'),
  
  // Engagement
  likeCount: integer('like_count').notNull().default(0),
  
  // IP tracking
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Article likes
export const articleLikes = pgTable('article_likes', {
  id: uuid('id').primaryKey().defaultRandom(),
  articleId: uuid('article_id').notNull().references(() => articles.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }),
  ipAddress: varchar('ip_address', { length: 45 }), // For anonymous likes
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// Article views tracking
export const articleViews = pgTable('article_views', {
  id: uuid('id').primaryKey().defaultRandom(),
  articleId: uuid('article_id').notNull().references(() => articles.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'set null' }),
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  referrer: text('referrer'),
  viewedAt: timestamp('viewed_at').notNull().defaultNow(),
});

// Newsletter subscriptions
export const newsletterSubscriptions = pgTable('newsletter_subscriptions', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  firstName: varchar('first_name', { length: 100 }),
  lastName: varchar('last_name', { length: 100 }),
  isActive: boolean('is_active').notNull().default(true),
  isVerified: boolean('is_verified').notNull().default(false),
  verificationToken: varchar('verification_token', { length: 255 }),
  unsubscribeToken: varchar('unsubscribe_token', { length: 255 }).notNull(),
  subscribedAt: timestamp('subscribed_at').notNull().defaultNow(),
  verifiedAt: timestamp('verified_at'),
  unsubscribedAt: timestamp('unsubscribed_at'),
});

// Zod schemas
export const insertArticleSchema = createInsertSchema(articles);
export const selectArticleSchema = createSelectSchema(articles);
export const insertArticleCategorySchema = createInsertSchema(articleCategories);
export const selectArticleCategorySchema = createSelectSchema(articleCategories);

export type Article = typeof articles.$inferSelect;
export type NewArticle = typeof articles.$inferInsert;
export type ArticleCategory = typeof articleCategories.$inferSelect;
export type ArticleTag = typeof articleTags.$inferSelect;
export type ArticleComment = typeof articleComments.$inferSelect;
export type NewsletterSubscription = typeof newsletterSubscriptions.$inferSelect;
