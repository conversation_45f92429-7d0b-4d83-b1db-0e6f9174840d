import { pgTable, uuid, varchar, text, timestamp, boolean, integer, decimal, pgEnum, jsonb } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { users } from './users';

// College type enum
export const collegeTypeEnum = pgEnum('college_type', [
  'government',
  'private',
  'deemed',
  'autonomous',
  'central_university',
  'state_university',
  'private_university'
]);

// College status enum
export const collegeStatusEnum = pgEnum('college_status', [
  'active',
  'inactive',
  'pending_approval',
  'suspended'
]);

// Accreditation enum
export const accreditationEnum = pgEnum('accreditation', [
  'naac_a_plus_plus',
  'naac_a_plus',
  'naac_a',
  'naac_b_plus_plus',
  'naac_b_plus',
  'naac_b',
  'nba',
  'aicte',
  'ugc',
  'nirf',
  'iso_certified'
]);

// Colleges table
export const colleges = pgTable('colleges', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  shortName: varchar('short_name', { length: 100 }),
  logo: text('logo'),
  bannerImage: text('banner_image'),
  tagline: varchar('tagline', { length: 500 }),
  description: text('description'),
  
  // Basic Information
  establishedYear: integer('established_year'),
  collegeType: collegeTypeEnum('college_type').notNull(),
  affiliation: varchar('affiliation', { length: 255 }),
  university: varchar('university', { length: 255 }),
  
  // Location
  address: text('address'),
  city: varchar('city', { length: 100 }).notNull(),
  state: varchar('state', { length: 100 }).notNull(),
  country: varchar('country', { length: 100 }).notNull().default('India'),
  pincode: varchar('pincode', { length: 10 }),
  latitude: decimal('latitude', { precision: 10, scale: 8 }),
  longitude: decimal('longitude', { precision: 11, scale: 8 }),
  
  // Contact Information
  phone: varchar('phone', { length: 20 }),
  email: varchar('email', { length: 255 }),
  website: varchar('website', { length: 255 }),
  
  // Accreditations and Rankings
  accreditations: jsonb('accreditations'), // Array of accreditation objects
  nirfRanking: integer('nirf_ranking'),
  nirfRankingYear: integer('nirf_ranking_year'),
  nirfCategory: varchar('nirf_category', { length: 100 }),
  otherRankings: jsonb('other_rankings'), // Array of ranking objects
  
  // Facilities
  campusArea: decimal('campus_area', { precision: 10, scale: 2 }), // in acres
  hostelCapacity: integer('hostel_capacity'),
  libraryBooks: integer('library_books'),
  hasWifi: boolean('has_wifi').default(true),
  hasGym: boolean('has_gym').default(false),
  hasSportsComplex: boolean('has_sports_complex').default(false),
  hasMedicalCenter: boolean('has_medical_center').default(false),
  hasCafeteria: boolean('has_cafeteria').default(true),
  hasTransport: boolean('has_transport').default(false),
  hasPlacementCell: boolean('has_placement_cell').default(true),
  
  // Academic Information
  totalStudents: integer('total_students'),
  totalFaculty: integer('total_faculty'),
  facultyStudentRatio: decimal('faculty_student_ratio', { precision: 5, scale: 2 }),
  phdFacultyPercentage: decimal('phd_faculty_percentage', { precision: 5, scale: 2 }),
  
  // Placement Statistics
  placementPercentage: decimal('placement_percentage', { precision: 5, scale: 2 }),
  averagePackage: decimal('average_package', { precision: 12, scale: 2 }),
  highestPackage: decimal('highest_package', { precision: 12, scale: 2 }),
  medianPackage: decimal('median_package', { precision: 12, scale: 2 }),
  topRecruiters: jsonb('top_recruiters'), // Array of company names
  
  // Admission Information
  applicationStartDate: timestamp('application_start_date'),
  applicationEndDate: timestamp('application_end_date'),
  entranceExams: jsonb('entrance_exams'), // Array of exam names
  admissionProcess: text('admission_process'),
  
  // SEO and Content
  metaTitle: varchar('meta_title', { length: 255 }),
  metaDescription: text('meta_description'),
  keywords: text('keywords'),
  
  // Status and Management
  status: collegeStatusEnum('status').notNull().default('pending_approval'),
  isVerified: boolean('is_verified').notNull().default(false),
  isFeatured: boolean('is_featured').notNull().default(false),
  isPremium: boolean('is_premium').notNull().default(false),
  viewCount: integer('view_count').notNull().default(0),
  
  // Representative Information
  representativeId: uuid('representative_id').references(() => users.id),
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  publishedAt: timestamp('published_at'),
});

// College gallery table
export const collegeGallery = pgTable('college_gallery', {
  id: uuid('id').primaryKey().defaultRandom(),
  collegeId: uuid('college_id').notNull().references(() => colleges.id, { onDelete: 'cascade' }),
  type: varchar('type', { length: 20 }).notNull(), // 'image' or 'video'
  url: text('url').notNull(),
  title: varchar('title', { length: 255 }),
  description: text('description'),
  category: varchar('category', { length: 100 }), // 'campus', 'infrastructure', 'events', 'hostel', etc.
  isMain: boolean('is_main').notNull().default(false),
  sortOrder: integer('sort_order').notNull().default(0),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// College news and events
export const collegeNews = pgTable('college_news', {
  id: uuid('id').primaryKey().defaultRandom(),
  collegeId: uuid('college_id').notNull().references(() => colleges.id, { onDelete: 'cascade' }),
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content').notNull(),
  type: varchar('type', { length: 20 }).notNull(), // 'news', 'event', 'announcement'
  eventDate: timestamp('event_date'),
  isPublished: boolean('is_published').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Zod schemas
export const insertCollegeSchema = createInsertSchema(colleges);
export const selectCollegeSchema = createSelectSchema(colleges);

export type College = typeof colleges.$inferSelect;
export type NewCollege = typeof colleges.$inferInsert;
export type CollegeGallery = typeof collegeGallery.$inferSelect;
export type CollegeNews = typeof collegeNews.$inferSelect;
