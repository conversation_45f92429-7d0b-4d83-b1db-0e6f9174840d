import { pgTable, uuid, varchar, text, timestamp, boolean, integer, decimal, pgEnum, jsonb } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { users } from './users';
import { colleges } from './colleges';
import { courses } from './courses';

// Contact inquiry type enum
export const inquiryTypeEnum = pgEnum('inquiry_type', [
  'general_inquiry',
  'scholarship_query',
  'college_partnership',
  'report_error',
  'feedback',
  'admission_guidance',
  'course_information',
  'technical_support'
]);

// Lead status enum
export const leadStatusEnum = pgEnum('lead_status', [
  'new',
  'contacted',
  'qualified',
  'proposal_sent',
  'negotiation',
  'closed_won',
  'closed_lost',
  'on_hold'
]);

// Contact inquiry status enum
export const contactStatusEnum = pgEnum('contact_status', [
  'new',
  'open',
  'in_progress',
  'counselling_completed',
  'admission_assisted',
  'admission_confirmed',
  'closed',
  'follow_up_required'
]);

// Contact inquiries table
export const contactInquiries = pgTable('contact_inquiries', {
  id: uuid('id').primaryKey().defaultRandom(),
  
  // Contact Information
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }),
  email: varchar('email', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 20 }),
  
  // Inquiry Details
  subject: varchar('subject', { length: 255 }).notNull(),
  message: text('message').notNull(),
  type: inquiryTypeEnum('type').notNull().default('general_inquiry'),
  
  // Additional Information
  interestedCourses: jsonb('interested_courses'), // Array of course names/IDs
  preferredLocation: varchar('preferred_location', { length: 255 }),
  budgetRange: varchar('budget_range', { length: 100 }),
  currentEducation: varchar('current_education', { length: 255 }),
  graduationYear: varchar('graduation_year', { length: 4 }),
  
  // Source tracking
  source: varchar('source', { length: 100 }), // 'website', 'popup', 'landing_page', 'social_media'
  referrer: text('referrer'),
  utmSource: varchar('utm_source', { length: 100 }),
  utmMedium: varchar('utm_medium', { length: 100 }),
  utmCampaign: varchar('utm_campaign', { length: 100 }),
  
  // Status and Assignment
  status: contactStatusEnum('status').notNull().default('new'),
  priority: varchar('priority', { length: 20 }).notNull().default('medium'), // 'low', 'medium', 'high', 'urgent'
  assignedTo: uuid('assigned_to').references(() => users.id),
  
  // Follow-up
  nextFollowUpDate: timestamp('next_follow_up_date'),
  followUpNotes: text('follow_up_notes'),
  
  // Conversion tracking
  isConverted: boolean('is_converted').notNull().default(false),
  convertedToAdmission: boolean('converted_to_admission').notNull().default(false),
  conversionDate: timestamp('conversion_date'),
  conversionValue: decimal('conversion_value', { precision: 12, scale: 2 }),
  
  // IP and device tracking
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  deviceType: varchar('device_type', { length: 50 }), // 'desktop', 'mobile', 'tablet'
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  respondedAt: timestamp('responded_at'),
  closedAt: timestamp('closed_at'),
});

// Contact inquiry notes/activities
export const contactInquiryNotes = pgTable('contact_inquiry_notes', {
  id: uuid('id').primaryKey().defaultRandom(),
  inquiryId: uuid('inquiry_id').notNull().references(() => contactInquiries.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').notNull().references(() => users.id),
  
  // Note content
  note: text('note').notNull(),
  type: varchar('type', { length: 50 }).notNull().default('note'), // 'note', 'call', 'email', 'meeting', 'follow_up'
  
  // Activity details
  duration: integer('duration'), // in minutes for calls/meetings
  outcome: varchar('outcome', { length: 100 }), // 'positive', 'negative', 'neutral', 'no_response'
  nextAction: text('next_action'),
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// Student admissions tracking
export const studentAdmissions = pgTable('student_admissions', {
  id: uuid('id').primaryKey().defaultRandom(),
  
  // Link to original inquiry
  inquiryId: uuid('inquiry_id').references(() => contactInquiries.id),
  
  // Student Information
  studentId: varchar('student_id', { length: 50 }).unique(), // Internal student ID
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }).notNull(),
  email: varchar('email', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 20 }).notNull(),
  
  // Address
  address: text('address'),
  city: varchar('city', { length: 100 }),
  state: varchar('state', { length: 100 }),
  pincode: varchar('pincode', { length: 10 }),
  
  // Admission Details
  collegeId: uuid('college_id').notNull().references(() => colleges.id),
  courseId: uuid('course_id').notNull().references(() => courses.id),
  admissionYear: varchar('admission_year', { length: 9 }), // e.g., '2025-26'
  admissionDate: timestamp('admission_date').notNull(),
  firstYearFee: decimal('first_year_fee', { precision: 12, scale: 2 }),
  
  // Scholarship Information
  scholarshipApplied: boolean('scholarship_applied').notNull().default(false),
  scholarshipAmount: decimal('scholarship_amount', { precision: 12, scale: 2 }),
  scholarshipStatus: varchar('scholarship_status', { length: 50 }), // 'pending', 'approved', 'disbursed', 'rejected'
  scholarshipDisbursementDate: timestamp('scholarship_disbursement_date'),
  scholarshipTransactionId: varchar('scholarship_transaction_id', { length: 255 }),
  scholarshipNotes: text('scholarship_notes'),
  
  // Commission Information
  commissionAmount: decimal('commission_amount', { precision: 12, scale: 2 }),
  commissionPercentage: decimal('commission_percentage', { precision: 5, scale: 2 }),
  commissionStatus: varchar('commission_status', { length: 50 }), // 'pending', 'invoiced', 'received', 'disputed'
  commissionReceivedDate: timestamp('commission_received_date'),
  commissionTransactionId: varchar('commission_transaction_id', { length: 255 }),
  commissionNotes: text('commission_notes'),
  
  // Student Status
  currentStatus: varchar('current_status', { length: 50 }).notNull().default('active'), // 'active', 'withdrew', 'completed', 'dropout'
  
  // Internal Notes
  internalNotes: text('internal_notes'),
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Q&A system
export const questions = pgTable('questions', {
  id: uuid('id').primaryKey().defaultRandom(),
  
  // Question content
  question: text('question').notNull(),
  askerName: varchar('asker_name', { length: 255 }),
  askerEmail: varchar('asker_email', { length: 255 }),
  
  // Context
  collegeId: uuid('college_id').references(() => colleges.id),
  courseId: uuid('course_id').references(() => courses.id),
  category: varchar('category', { length: 100 }), // 'admission', 'fees', 'placement', 'facilities', 'general'
  
  // Status
  isAnswered: boolean('is_answered').notNull().default(false),
  isPublished: boolean('is_published').notNull().default(false),
  isAnonymous: boolean('is_anonymous').notNull().default(false),
  
  // Moderation
  isApproved: boolean('is_approved').notNull().default(false),
  moderatedBy: uuid('moderated_by').references(() => users.id),
  moderatedAt: timestamp('moderated_at'),
  
  // Engagement
  viewCount: integer('view_count').notNull().default(0),
  helpfulCount: integer('helpful_count').notNull().default(0),
  
  // IP tracking
  ipAddress: varchar('ip_address', { length: 45 }),
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Q&A answers
export const answers = pgTable('answers', {
  id: uuid('id').primaryKey().defaultRandom(),
  questionId: uuid('question_id').notNull().references(() => questions.id, { onDelete: 'cascade' }),
  
  // Answer content
  answer: text('answer').notNull(),
  
  // Answerer information
  answeredBy: uuid('answered_by').references(() => users.id),
  answererName: varchar('answerer_name', { length: 255 }),
  answererTitle: varchar('answerer_title', { length: 255 }), // 'Admin', 'College Representative', 'Expert'
  isOfficial: boolean('is_official').notNull().default(false), // Official college response
  
  // Status
  isApproved: boolean('is_approved').notNull().default(false),
  isPublished: boolean('is_published').notNull().default(false),
  
  // Engagement
  helpfulCount: integer('helpful_count').notNull().default(0),
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Zod schemas
export const insertContactInquirySchema = createInsertSchema(contactInquiries);
export const selectContactInquirySchema = createSelectSchema(contactInquiries);
export const insertStudentAdmissionSchema = createInsertSchema(studentAdmissions);
export const selectStudentAdmissionSchema = createSelectSchema(studentAdmissions);

export type ContactInquiry = typeof contactInquiries.$inferSelect;
export type NewContactInquiry = typeof contactInquiries.$inferInsert;
export type ContactInquiryNote = typeof contactInquiryNotes.$inferSelect;
export type StudentAdmission = typeof studentAdmissions.$inferSelect;
export type NewStudentAdmission = typeof studentAdmissions.$inferInsert;
export type Question = typeof questions.$inferSelect;
export type Answer = typeof answers.$inferSelect;
