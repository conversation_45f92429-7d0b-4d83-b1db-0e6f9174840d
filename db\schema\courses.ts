import { pgTable, uuid, varchar, text, timestamp, boolean, integer, decimal, pgEnum, jsonb } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { colleges } from './colleges';

// Course level enum
export const courseLevelEnum = pgEnum('course_level', [
  'undergraduate',
  'postgraduate',
  'diploma',
  'certificate',
  'doctorate',
  'post_doctorate'
]);

// Course mode enum
export const courseModeEnum = pgEnum('course_mode', [
  'full_time',
  'part_time',
  'distance',
  'online',
  'hybrid'
]);

// Course status enum
export const courseStatusEnum = pgEnum('course_status', [
  'active',
  'inactive',
  'discontinued'
]);

// Stream enum
export const streamEnum = pgEnum('stream', [
  'engineering',
  'medical',
  'management',
  'arts',
  'science',
  'commerce',
  'law',
  'education',
  'agriculture',
  'pharmacy',
  'architecture',
  'design',
  'journalism',
  'hospitality',
  'aviation',
  'paramedical',
  'computer_applications',
  'social_work',
  'library_science',
  'fine_arts',
  'performing_arts'
]);

// Courses table
export const courses = pgTable('courses', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  shortName: varchar('short_name', { length: 50 }),
  description: text('description'),
  
  // Course Classification
  level: courseLevelEnum('level').notNull(),
  stream: streamEnum('stream').notNull(),
  specialization: varchar('specialization', { length: 255 }),
  mode: courseModeEnum('mode').notNull().default('full_time'),
  
  // Duration and Structure
  durationYears: decimal('duration_years', { precision: 3, scale: 1 }).notNull(),
  durationMonths: integer('duration_months'),
  totalSemesters: integer('total_semesters'),
  totalCredits: integer('total_credits'),
  
  // Eligibility
  eligibilityCriteria: text('eligibility_criteria'),
  minimumPercentage: decimal('minimum_percentage', { precision: 5, scale: 2 }),
  entranceExams: jsonb('entrance_exams'), // Array of exam names
  ageLimit: integer('age_limit'),
  
  // Academic Content
  syllabus: text('syllabus'),
  curriculum: jsonb('curriculum'), // Semester/year wise subjects
  keySubjects: jsonb('key_subjects'), // Array of important subjects
  
  // Career Information
  careerProspects: text('career_prospects'),
  jobRoles: jsonb('job_roles'), // Array of job titles
  industries: jsonb('industries'), // Array of industry names
  averageSalary: decimal('average_salary', { precision: 12, scale: 2 }),
  salaryRange: jsonb('salary_range'), // {min: number, max: number}
  
  // Further Studies
  higherStudyOptions: jsonb('higher_study_options'), // Array of course names
  
  // SEO
  metaTitle: varchar('meta_title', { length: 255 }),
  metaDescription: text('meta_description'),
  keywords: text('keywords'),
  
  // Status
  status: courseStatusEnum('status').notNull().default('active'),
  isPopular: boolean('is_popular').notNull().default(false),
  viewCount: integer('view_count').notNull().default(0),
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// College courses junction table (many-to-many relationship)
export const collegeCoursesEnum = pgEnum('admission_status', [
  'open',
  'closed',
  'coming_soon'
]);

export const collegeCourses = pgTable('college_courses', {
  id: uuid('id').primaryKey().defaultRandom(),
  collegeId: uuid('college_id').notNull().references(() => colleges.id, { onDelete: 'cascade' }),
  courseId: uuid('course_id').notNull().references(() => courses.id, { onDelete: 'cascade' }),
  
  // College-specific course details
  courseName: varchar('course_name', { length: 255 }), // College-specific name if different
  totalSeats: integer('total_seats'),
  reservedSeats: jsonb('reserved_seats'), // Category-wise reservation
  
  // Fee Structure
  tuitionFee: decimal('tuition_fee', { precision: 12, scale: 2 }),
  hostelFee: decimal('hostel_fee', { precision: 12, scale: 2 }),
  examFee: decimal('exam_fee', { precision: 12, scale: 2 }),
  otherFees: decimal('other_fees', { precision: 12, scale: 2 }),
  totalFee: decimal('total_fee', { precision: 12, scale: 2 }),
  feeStructure: jsonb('fee_structure'), // Detailed year-wise fee breakdown
  
  // Admission Details
  admissionStatus: collegeCoursesEnum('admission_status').notNull().default('open'),
  applicationStartDate: timestamp('application_start_date'),
  applicationEndDate: timestamp('application_end_date'),
  selectionProcess: text('selection_process'),
  cutoffMarks: jsonb('cutoff_marks'), // Previous year cutoffs
  
  // Course-specific placement data
  placementPercentage: decimal('placement_percentage', { precision: 5, scale: 2 }),
  averagePackage: decimal('average_package', { precision: 12, scale: 2 }),
  highestPackage: decimal('highest_package', { precision: 12, scale: 2 }),
  topRecruiters: jsonb('top_recruiters'),
  
  // Status
  isActive: boolean('is_active').notNull().default(true),
  isFeatured: boolean('is_featured').notNull().default(false),
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Course categories for better organization
export const courseCategories = pgTable('course_categories', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull(),
  slug: varchar('slug', { length: 100 }).notNull().unique(),
  description: text('description'),
  icon: varchar('icon', { length: 100 }),
  sortOrder: integer('sort_order').notNull().default(0),
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// Course category mapping
export const courseCategoryMapping = pgTable('course_category_mapping', {
  id: uuid('id').primaryKey().defaultRandom(),
  courseId: uuid('course_id').notNull().references(() => courses.id, { onDelete: 'cascade' }),
  categoryId: uuid('category_id').notNull().references(() => courseCategories.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// Zod schemas
export const insertCourseSchema = createInsertSchema(courses);
export const selectCourseSchema = createSelectSchema(courses);
export const insertCollegeCourseSchema = createInsertSchema(collegeCourses);
export const selectCollegeCourseSchema = createSelectSchema(collegeCourses);

export type Course = typeof courses.$inferSelect;
export type NewCourse = typeof courses.$inferInsert;
export type CollegeCourse = typeof collegeCourses.$inferSelect;
export type NewCollegeCourse = typeof collegeCourses.$inferInsert;
export type CourseCategory = typeof courseCategories.$inferSelect;
