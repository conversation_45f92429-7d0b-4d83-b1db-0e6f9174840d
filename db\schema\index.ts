// Export all schemas and types

// Users schema
export * from './users';

// Colleges schema
export * from './colleges';

// Courses schema
export * from './courses';

// Reviews schema (create a simple version since the file seems to have issues)
import { pgTable, uuid, varchar, text, timestamp, boolean, integer, decimal, pgEnum, jsonb } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { users } from './users';
import { colleges } from './colleges';
import { courses } from './courses';

// Review status enum
export const reviewStatusEnum = pgEnum('review_status', [
  'pending',
  'approved',
  'rejected',
  'flagged',
  'under_review'
]);

// Student status enum for reviews
export const studentStatusEnum = pgEnum('student_status', [
  'current_student',
  'alumni',
  'dropout'
]);

// Reviews table
export const reviews = pgTable('reviews', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  collegeId: uuid('college_id').notNull().references(() => colleges.id, { onDelete: 'cascade' }),
  courseId: uuid('course_id').references(() => courses.id),
  
  // Review Content
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content').notNull(),
  pros: text('pros'),
  cons: text('cons'),
  
  // Ratings (1-5 scale)
  overallRating: decimal('overall_rating', { precision: 2, scale: 1 }).notNull(),
  academicsRating: decimal('academics_rating', { precision: 2, scale: 1 }),
  facultyRating: decimal('faculty_rating', { precision: 2, scale: 1 }),
  infrastructureRating: decimal('infrastructure_rating', { precision: 2, scale: 1 }),
  placementsRating: decimal('placements_rating', { precision: 2, scale: 1 }),
  socialLifeRating: decimal('social_life_rating', { precision: 2, scale: 1 }),
  valueForMoneyRating: decimal('value_for_money_rating', { precision: 2, scale: 1 }),
  hostelRating: decimal('hostel_rating', { precision: 2, scale: 1 }),
  
  // Student Information
  studentStatus: studentStatusEnum('student_status').notNull(),
  graduationYear: varchar('graduation_year', { length: 4 }),
  expectedGraduationYear: varchar('expected_graduation_year', { length: 4 }),
  studyYear: integer('study_year'), // Current year of study
  
  // Experience Details
  placementExperience: text('placement_experience'),
  internshipExperience: text('internship_experience'),
  packageOffered: decimal('package_offered', { precision: 12, scale: 2 }),
  companyPlaced: varchar('company_placed', { length: 255 }),
  
  // Media
  images: jsonb('images'), // Array of image URLs
  videos: jsonb('videos'), // Array of video URLs
  
  // Verification
  isVerified: boolean('is_verified').notNull().default(false),
  verificationMethod: varchar('verification_method', { length: 50 }), // 'email', 'document', 'phone'
  verificationData: jsonb('verification_data'), // Store verification details
  
  // Moderation
  status: reviewStatusEnum('status').notNull().default('pending'),
  moderatorId: uuid('moderator_id').references(() => users.id),
  moderatorNotes: text('moderator_notes'),
  moderatedAt: timestamp('moderated_at'),
  rejectionReason: text('rejection_reason'),
  
  // Engagement
  helpfulCount: integer('helpful_count').notNull().default(0),
  notHelpfulCount: integer('not_helpful_count').notNull().default(0),
  reportCount: integer('report_count').notNull().default(0),
  
  // Anonymous option
  isAnonymous: boolean('is_anonymous').notNull().default(false),
  
  // IP tracking for spam prevention
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  publishedAt: timestamp('published_at'),
});

// Review helpfulness tracking
export const reviewHelpfulness = pgTable('review_helpfulness', {
  id: uuid('id').primaryKey().defaultRandom(),
  reviewId: uuid('review_id').notNull().references(() => reviews.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  isHelpful: boolean('is_helpful').notNull(), // true for helpful, false for not helpful
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// College rating aggregates (computed from reviews)
export const collegeRatings = pgTable('college_ratings', {
  id: uuid('id').primaryKey().defaultRandom(),
  collegeId: uuid('college_id').notNull().references(() => colleges.id, { onDelete: 'cascade' }),
  
  // Aggregate ratings
  overallRating: decimal('overall_rating', { precision: 3, scale: 2 }),
  academicsRating: decimal('academics_rating', { precision: 3, scale: 2 }),
  facultyRating: decimal('faculty_rating', { precision: 3, scale: 2 }),
  infrastructureRating: decimal('infrastructure_rating', { precision: 3, scale: 2 }),
  placementsRating: decimal('placements_rating', { precision: 3, scale: 2 }),
  socialLifeRating: decimal('social_life_rating', { precision: 3, scale: 2 }),
  valueForMoneyRating: decimal('value_for_money_rating', { precision: 3, scale: 2 }),
  hostelRating: decimal('hostel_rating', { precision: 3, scale: 2 }),
  
  // Counts
  totalReviews: integer('total_reviews').notNull().default(0),
  verifiedReviews: integer('verified_reviews').notNull().default(0),
  
  // Rating distribution
  fiveStarCount: integer('five_star_count').notNull().default(0),
  fourStarCount: integer('four_star_count').notNull().default(0),
  threeStarCount: integer('three_star_count').notNull().default(0),
  twoStarCount: integer('two_star_count').notNull().default(0),
  oneStarCount: integer('one_star_count').notNull().default(0),
  
  // Last updated
  lastUpdated: timestamp('last_updated').notNull().defaultNow(),
});

// Scholarships schema
export * from './scholarships';

// Articles schema
export * from './articles';

// Contacts schema
export * from './contacts';

// Review types
export type Review = typeof reviews.$inferSelect;
export type NewReview = typeof reviews.$inferInsert;
export type ReviewHelpfulness = typeof reviewHelpfulness.$inferSelect;
export type CollegeRating = typeof collegeRatings.$inferSelect;

// Review schemas
export const insertReviewSchema = createInsertSchema(reviews);
export const selectReviewSchema = createSelectSchema(reviews);
