import { pgTable, uuid, varchar, text, timestamp, boolean, integer, decimal, pgEnum, jsonb } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { users } from './users';

// Scholarship status enum
export const scholarshipStatusEnum = pgEnum('scholarship_status', [
  'active',
  'inactive',
  'expired',
  'draft'
]);

// Application status enum
export const applicationStatusEnum = pgEnum('application_status', [
  'received',
  'under_review',
  'shortlisted',
  'approved',
  'rejected',
  'disbursed',
  'cancelled'
]);

// Scholarship type enum
export const scholarshipTypeEnum = pgEnum('scholarship_type', [
  'merit_based',
  'need_based',
  'sports',
  'minority',
  'women',
  'disability',
  'rural',
  'general'
]);

// Scholarships table
export const scholarships = pgTable('scholarships', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  description: text('description').notNull(),
  shortDescription: varchar('short_description', { length: 500 }),
  
  // Scholarship Details
  type: scholarshipTypeEnum('type').notNull(),
  amount: decimal('amount', { precision: 12, scale: 2 }).notNull(),
  totalSlots: integer('total_slots').notNull(),
  availableSlots: integer('available_slots').notNull(),
  
  // Eligibility Criteria
  eligibilityCriteria: text('eligibility_criteria').notNull(),
  minimumPercentage: decimal('minimum_percentage', { precision: 5, scale: 2 }),
  familyIncomeLimit: decimal('family_income_limit', { precision: 12, scale: 2 }),
  ageLimit: integer('age_limit'),
  courseEligibility: jsonb('course_eligibility'), // Array of eligible courses
  streamEligibility: jsonb('stream_eligibility'), // Array of eligible streams
  genderEligibility: varchar('gender_eligibility', { length: 20 }), // 'male', 'female', 'all'
  categoryEligibility: jsonb('category_eligibility'), // Array of categories (SC, ST, OBC, etc.)
  
  // Application Details
  applicationStartDate: timestamp('application_start_date').notNull(),
  applicationEndDate: timestamp('application_end_date').notNull(),
  selectionProcess: text('selection_process'),
  requiredDocuments: jsonb('required_documents'), // Array of required documents
  
  // Contact Information
  contactEmail: varchar('contact_email', { length: 255 }),
  contactPhone: varchar('contact_phone', { length: 20 }),
  
  // SEO
  metaTitle: varchar('meta_title', { length: 255 }),
  metaDescription: text('meta_description'),
  keywords: text('keywords'),
  
  // Status and Management
  status: scholarshipStatusEnum('status').notNull().default('draft'),
  isCollegeCampusScholarship: boolean('is_college_campus_scholarship').notNull().default(true),
  isFeatured: boolean('is_featured').notNull().default(false),
  viewCount: integer('view_count').notNull().default(0),
  applicationCount: integer('application_count').notNull().default(0),
  
  // Sponsor Information
  sponsorName: varchar('sponsor_name', { length: 255 }),
  sponsorLogo: text('sponsor_logo'),
  sponsorWebsite: varchar('sponsor_website', { length: 255 }),
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  publishedAt: timestamp('published_at'),
});

// Scholarship applications table
export const scholarshipApplications = pgTable('scholarship_applications', {
  id: uuid('id').primaryKey().defaultRandom(),
  scholarshipId: uuid('scholarship_id').notNull().references(() => scholarships.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  
  // Personal Information
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }).notNull(),
  email: varchar('email', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 20 }).notNull(),
  dateOfBirth: timestamp('date_of_birth').notNull(),
  gender: varchar('gender', { length: 20 }).notNull(),
  category: varchar('category', { length: 50 }), // SC, ST, OBC, General
  
  // Address
  address: text('address').notNull(),
  city: varchar('city', { length: 100 }).notNull(),
  state: varchar('state', { length: 100 }).notNull(),
  pincode: varchar('pincode', { length: 10 }).notNull(),
  
  // Academic Information
  currentCourse: varchar('current_course', { length: 255 }).notNull(),
  currentYear: integer('current_year').notNull(),
  collegeName: varchar('college_name', { length: 255 }).notNull(),
  currentPercentage: decimal('current_percentage', { precision: 5, scale: 2 }).notNull(),
  previousEducation: jsonb('previous_education'), // Array of education details
  
  // Financial Information
  familyIncome: decimal('family_income', { precision: 12, scale: 2 }).notNull(),
  fatherOccupation: varchar('father_occupation', { length: 255 }),
  motherOccupation: varchar('mother_occupation', { length: 255 }),
  
  // Documents
  documents: jsonb('documents'), // Array of document objects with type and URL
  
  // Essay/Statement
  personalStatement: text('personal_statement'),
  whyDeserveScholarship: text('why_deserve_scholarship'),
  futureGoals: text('future_goals'),
  
  // Application Status
  status: applicationStatusEnum('status').notNull().default('received'),
  reviewedBy: uuid('reviewed_by').references(() => users.id),
  reviewNotes: text('review_notes'),
  rejectionReason: text('rejection_reason'),
  
  // Disbursement Information
  disbursementAmount: decimal('disbursement_amount', { precision: 12, scale: 2 }),
  disbursementDate: timestamp('disbursement_date'),
  disbursementMethod: varchar('disbursement_method', { length: 50 }), // 'bank_transfer', 'cheque', 'direct_to_college'
  bankDetails: jsonb('bank_details'), // Bank account information
  transactionId: varchar('transaction_id', { length: 255 }),
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  reviewedAt: timestamp('reviewed_at'),
  approvedAt: timestamp('approved_at'),
  disbursedAt: timestamp('disbursed_at'),
});

// Scholarship testimonials
export const scholarshipTestimonials = pgTable('scholarship_testimonials', {
  id: uuid('id').primaryKey().defaultRandom(),
  scholarshipId: uuid('scholarship_id').notNull().references(() => scholarships.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  
  // Testimonial Content
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content').notNull(),
  rating: integer('rating').notNull(), // 1-5 stars
  
  // Student Information
  studentName: varchar('student_name', { length: 255 }).notNull(),
  course: varchar('course', { length: 255 }),
  college: varchar('college', { length: 255 }),
  graduationYear: varchar('graduation_year', { length: 4 }),
  photo: text('photo'),
  
  // Status
  isApproved: boolean('is_approved').notNull().default(false),
  isFeatured: boolean('is_featured').notNull().default(false),
  approvedBy: uuid('approved_by').references(() => users.id),
  approvedAt: timestamp('approved_at'),
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Zod schemas
export const insertScholarshipSchema = createInsertSchema(scholarships);
export const selectScholarshipSchema = createSelectSchema(scholarships);
export const insertScholarshipApplicationSchema = createInsertSchema(scholarshipApplications);
export const selectScholarshipApplicationSchema = createSelectSchema(scholarshipApplications);

export type Scholarship = typeof scholarships.$inferSelect;
export type NewScholarship = typeof scholarships.$inferInsert;
export type ScholarshipApplication = typeof scholarshipApplications.$inferSelect;
export type NewScholarshipApplication = typeof scholarshipApplications.$inferInsert;
export type ScholarshipTestimonial = typeof scholarshipTestimonials.$inferSelect;
