CREATE TABLE "admin_activity_logs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"admin_id" uuid NOT NULL,
	"action" varchar(100) NOT NULL,
	"resource" varchar(100) NOT NULL,
	"resource_id" varchar(255),
	"details" jsonb,
	"ip_address" varchar(45),
	"user_agent" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "admin_roles" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(100) NOT NULL,
	"description" text,
	"permissions" jsonb NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "admin_roles_name_unique" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "admin_users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" varchar(255) NOT NULL,
	"password" text NOT NULL,
	"name" varchar(255) NOT NULL,
	"role" varchar(50) DEFAULT 'admin' NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"last_login" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid,
	CONSTRAINT "admin_users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "articles" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"title" varchar(255) NOT NULL,
	"slug" varchar(255) NOT NULL,
	"excerpt" text,
	"content" text NOT NULL,
	"featured_image" text,
	"category" varchar(100),
	"tags" jsonb,
	"author_id" uuid NOT NULL,
	"status" varchar(20) DEFAULT 'draft',
	"published_at" timestamp,
	"read_time" integer,
	"view_count" integer DEFAULT 0,
	"seo_title" varchar(255),
	"seo_description" text,
	"seo_keywords" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "articles_slug_unique" UNIQUE("slug")
);
--> statement-breakpoint
CREATE TABLE "colleges" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"short_name" varchar(100),
	"slug" varchar(255) NOT NULL,
	"description" text,
	"about" text,
	"vision" text,
	"mission" text,
	"established_year" integer,
	"college_type" varchar(50),
	"affiliation" varchar(255),
	"approvals" jsonb,
	"accreditations" jsonb,
	"location" jsonb,
	"contact_info" jsonb,
	"logo" text,
	"banner_images" jsonb,
	"nirf_ranking" integer,
	"overall_rating" numeric(3, 2),
	"total_reviews" integer DEFAULT 0,
	"total_students" integer,
	"faculty_count" integer,
	"campus_area" varchar(100),
	"is_verified" boolean DEFAULT false,
	"is_featured" boolean DEFAULT false,
	"is_published" boolean DEFAULT false,
	"seo_title" varchar(255),
	"seo_description" text,
	"seo_keywords" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid,
	CONSTRAINT "colleges_slug_unique" UNIQUE("slug")
);
--> statement-breakpoint
CREATE TABLE "colleges_courses" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"college_id" uuid NOT NULL,
	"course_id" uuid NOT NULL,
	"specializations" jsonb,
	"fees" jsonb,
	"seats" integer,
	"selection_process" text,
	"entrance_exams" jsonb,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "contact_inquiries" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"email" varchar(255) NOT NULL,
	"phone" varchar(20),
	"subject" varchar(255),
	"message" text NOT NULL,
	"source" varchar(50) DEFAULT 'contact_form',
	"status" varchar(50) DEFAULT 'new',
	"priority" varchar(20) DEFAULT 'medium',
	"assigned_to" uuid,
	"interested_courses" jsonb,
	"interested_colleges" jsonb,
	"notes" text,
	"follow_up_date" timestamp,
	"ip_address" varchar(45),
	"user_agent" text,
	"utm_source" varchar(100),
	"utm_medium" varchar(100),
	"utm_campaign" varchar(100),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "courses" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"short_name" varchar(100),
	"slug" varchar(255) NOT NULL,
	"level" varchar(50) NOT NULL,
	"duration" varchar(50),
	"stream" varchar(100),
	"description" text,
	"detailed_description" text,
	"eligibility" jsonb,
	"syllabus" jsonb,
	"career_prospects" jsonb,
	"average_fees" integer,
	"average_salary" integer,
	"is_popular" boolean DEFAULT false,
	"is_published" boolean DEFAULT false,
	"seo_title" varchar(255),
	"seo_description" text,
	"seo_keywords" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid,
	CONSTRAINT "courses_slug_unique" UNIQUE("slug")
);
--> statement-breakpoint
CREATE TABLE "reviews" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"college_id" uuid NOT NULL,
	"student_id" uuid NOT NULL,
	"course_id" uuid,
	"title" varchar(255) NOT NULL,
	"overall_rating" numeric(3, 2) NOT NULL,
	"ratings" jsonb NOT NULL,
	"pros" text,
	"cons" text,
	"review" text NOT NULL,
	"year_of_study" varchar(50),
	"graduation_year" integer,
	"is_anonymous" boolean DEFAULT false,
	"status" varchar(20) DEFAULT 'pending',
	"moderated_by" uuid,
	"moderated_at" timestamp,
	"moderation_notes" text,
	"helpful_count" integer DEFAULT 0,
	"report_count" integer DEFAULT 0,
	"attachments" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "scholarship_applications" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"student_id" uuid,
	"name" varchar(255) NOT NULL,
	"email" varchar(255) NOT NULL,
	"phone" varchar(20) NOT NULL,
	"date_of_birth" timestamp NOT NULL,
	"gender" varchar(10) NOT NULL,
	"category" varchar(50),
	"address" jsonb NOT NULL,
	"academic_details" jsonb NOT NULL,
	"family_income" integer NOT NULL,
	"course_applied_for" varchar(255) NOT NULL,
	"college_preferences" jsonb,
	"documents" jsonb,
	"personal_statement" text,
	"status" varchar(50) DEFAULT 'received',
	"reviewed_by" uuid,
	"reviewed_at" timestamp,
	"review_notes" text,
	"scholarship_amount" integer,
	"disbursement_status" varchar(50),
	"disbursement_date" timestamp,
	"disbursement_transaction_id" varchar(255),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "student_admissions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"student_id" varchar(255) NOT NULL,
	"lead_id" uuid,
	"name" varchar(255) NOT NULL,
	"email" varchar(255) NOT NULL,
	"phone" varchar(20) NOT NULL,
	"address" jsonb,
	"college_id" uuid NOT NULL,
	"course_id" uuid NOT NULL,
	"year_of_admission" varchar(10) NOT NULL,
	"admission_date" timestamp NOT NULL,
	"college_first_year_fee" integer,
	"scholarship_application_id" uuid,
	"scholarship_amount_awarded" integer,
	"scholarship_disbursement_status" varchar(50),
	"scholarship_disbursement_date" timestamp,
	"scholarship_transaction_id" varchar(255),
	"scholarship_notes" text,
	"commission_amount" integer,
	"commission_percentage" numeric(5, 2),
	"commission_status" varchar(50),
	"commission_received_date" timestamp,
	"commission_transaction_id" varchar(255),
	"commission_notes" text,
	"overall_status" varchar(50) DEFAULT 'active_student',
	"internal_notes" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid,
	"last_updated_by" uuid
);
--> statement-breakpoint
CREATE TABLE "student_users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" varchar(255) NOT NULL,
	"password" text,
	"name" varchar(255) NOT NULL,
	"phone" varchar(20),
	"date_of_birth" timestamp,
	"gender" varchar(10),
	"address" jsonb,
	"profile_picture" text,
	"is_verified" boolean DEFAULT false,
	"is_active" boolean DEFAULT true,
	"last_login" timestamp,
	"auth_provider" varchar(50) DEFAULT 'email',
	"auth_provider_id" varchar(255),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "student_users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "website_settings" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"key" varchar(100) NOT NULL,
	"value" jsonb,
	"description" text,
	"category" varchar(50),
	"is_public" boolean DEFAULT false,
	"updated_by" uuid,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "website_settings_key_unique" UNIQUE("key")
);
--> statement-breakpoint
ALTER TABLE "admin_activity_logs" ADD CONSTRAINT "admin_activity_logs_admin_id_admin_users_id_fk" FOREIGN KEY ("admin_id") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "admin_users" ADD CONSTRAINT "admin_users_created_by_admin_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "articles" ADD CONSTRAINT "articles_author_id_admin_users_id_fk" FOREIGN KEY ("author_id") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "colleges" ADD CONSTRAINT "colleges_created_by_admin_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "colleges_courses" ADD CONSTRAINT "colleges_courses_college_id_colleges_id_fk" FOREIGN KEY ("college_id") REFERENCES "public"."colleges"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "colleges_courses" ADD CONSTRAINT "colleges_courses_course_id_courses_id_fk" FOREIGN KEY ("course_id") REFERENCES "public"."courses"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "contact_inquiries" ADD CONSTRAINT "contact_inquiries_assigned_to_admin_users_id_fk" FOREIGN KEY ("assigned_to") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "courses" ADD CONSTRAINT "courses_created_by_admin_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_college_id_colleges_id_fk" FOREIGN KEY ("college_id") REFERENCES "public"."colleges"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_student_id_student_users_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."student_users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_course_id_courses_id_fk" FOREIGN KEY ("course_id") REFERENCES "public"."courses"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_moderated_by_admin_users_id_fk" FOREIGN KEY ("moderated_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "scholarship_applications" ADD CONSTRAINT "scholarship_applications_student_id_student_users_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."student_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "scholarship_applications" ADD CONSTRAINT "scholarship_applications_reviewed_by_admin_users_id_fk" FOREIGN KEY ("reviewed_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_admissions" ADD CONSTRAINT "student_admissions_lead_id_contact_inquiries_id_fk" FOREIGN KEY ("lead_id") REFERENCES "public"."contact_inquiries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_admissions" ADD CONSTRAINT "student_admissions_college_id_colleges_id_fk" FOREIGN KEY ("college_id") REFERENCES "public"."colleges"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_admissions" ADD CONSTRAINT "student_admissions_course_id_courses_id_fk" FOREIGN KEY ("course_id") REFERENCES "public"."courses"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_admissions" ADD CONSTRAINT "student_admissions_scholarship_application_id_scholarship_applications_id_fk" FOREIGN KEY ("scholarship_application_id") REFERENCES "public"."scholarship_applications"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_admissions" ADD CONSTRAINT "student_admissions_created_by_admin_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_admissions" ADD CONSTRAINT "student_admissions_last_updated_by_admin_users_id_fk" FOREIGN KEY ("last_updated_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "website_settings" ADD CONSTRAINT "website_settings_updated_by_admin_users_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;