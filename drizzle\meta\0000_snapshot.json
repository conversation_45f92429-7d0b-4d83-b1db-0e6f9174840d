{"id": "6f19a694-40a4-4e60-a755-965ad3b1934e", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.admin_activity_logs": {"name": "admin_activity_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "admin_id": {"name": "admin_id", "type": "uuid", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "resource": {"name": "resource", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "resource_id": {"name": "resource_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"admin_activity_logs_admin_id_admin_users_id_fk": {"name": "admin_activity_logs_admin_id_admin_users_id_fk", "tableFrom": "admin_activity_logs", "tableTo": "admin_users", "columnsFrom": ["admin_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.admin_roles": {"name": "admin_roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"admin_roles_name_unique": {"name": "admin_roles_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.admin_users": {"name": "admin_users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'admin'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"admin_users_created_by_admin_users_id_fk": {"name": "admin_users_created_by_admin_users_id_fk", "tableFrom": "admin_users", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"admin_users_email_unique": {"name": "admin_users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.articles": {"name": "articles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "excerpt": {"name": "excerpt", "type": "text", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "featured_image": {"name": "featured_image", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": false}, "author_id": {"name": "author_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'draft'"}, "published_at": {"name": "published_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "read_time": {"name": "read_time", "type": "integer", "primaryKey": false, "notNull": false}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "seo_title": {"name": "seo_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "seo_description": {"name": "seo_description", "type": "text", "primaryKey": false, "notNull": false}, "seo_keywords": {"name": "seo_keywords", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"articles_author_id_admin_users_id_fk": {"name": "articles_author_id_admin_users_id_fk", "tableFrom": "articles", "tableTo": "admin_users", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"articles_slug_unique": {"name": "articles_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.colleges": {"name": "colleges", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "short_name": {"name": "short_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "about": {"name": "about", "type": "text", "primaryKey": false, "notNull": false}, "vision": {"name": "vision", "type": "text", "primaryKey": false, "notNull": false}, "mission": {"name": "mission", "type": "text", "primaryKey": false, "notNull": false}, "established_year": {"name": "established_year", "type": "integer", "primaryKey": false, "notNull": false}, "college_type": {"name": "college_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "affiliation": {"name": "affiliation", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "approvals": {"name": "approvals", "type": "jsonb", "primaryKey": false, "notNull": false}, "accreditations": {"name": "accreditations", "type": "jsonb", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "jsonb", "primaryKey": false, "notNull": false}, "contact_info": {"name": "contact_info", "type": "jsonb", "primaryKey": false, "notNull": false}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "banner_images": {"name": "banner_images", "type": "jsonb", "primaryKey": false, "notNull": false}, "nirf_ranking": {"name": "nirf_ranking", "type": "integer", "primaryKey": false, "notNull": false}, "overall_rating": {"name": "overall_rating", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false}, "total_reviews": {"name": "total_reviews", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "total_students": {"name": "total_students", "type": "integer", "primaryKey": false, "notNull": false}, "faculty_count": {"name": "faculty_count", "type": "integer", "primaryKey": false, "notNull": false}, "campus_area": {"name": "campus_area", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_featured": {"name": "is_featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "seo_title": {"name": "seo_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "seo_description": {"name": "seo_description", "type": "text", "primaryKey": false, "notNull": false}, "seo_keywords": {"name": "seo_keywords", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"colleges_created_by_admin_users_id_fk": {"name": "colleges_created_by_admin_users_id_fk", "tableFrom": "colleges", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"colleges_slug_unique": {"name": "colleges_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.colleges_courses": {"name": "colleges_courses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "college_id": {"name": "college_id", "type": "uuid", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "uuid", "primaryKey": false, "notNull": true}, "specializations": {"name": "specializations", "type": "jsonb", "primaryKey": false, "notNull": false}, "fees": {"name": "fees", "type": "jsonb", "primaryKey": false, "notNull": false}, "seats": {"name": "seats", "type": "integer", "primaryKey": false, "notNull": false}, "selection_process": {"name": "selection_process", "type": "text", "primaryKey": false, "notNull": false}, "entrance_exams": {"name": "entrance_exams", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"colleges_courses_college_id_colleges_id_fk": {"name": "colleges_courses_college_id_colleges_id_fk", "tableFrom": "colleges_courses", "tableTo": "colleges", "columnsFrom": ["college_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "colleges_courses_course_id_courses_id_fk": {"name": "colleges_courses_course_id_courses_id_fk", "tableFrom": "colleges_courses", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contact_inquiries": {"name": "contact_inquiries", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'contact_form'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'new'"}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'medium'"}, "assigned_to": {"name": "assigned_to", "type": "uuid", "primaryKey": false, "notNull": false}, "interested_courses": {"name": "interested_courses", "type": "jsonb", "primaryKey": false, "notNull": false}, "interested_colleges": {"name": "interested_colleges", "type": "jsonb", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "follow_up_date": {"name": "follow_up_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "utm_source": {"name": "utm_source", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "utm_medium": {"name": "utm_medium", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "utm_campaign": {"name": "utm_campaign", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"contact_inquiries_assigned_to_admin_users_id_fk": {"name": "contact_inquiries_assigned_to_admin_users_id_fk", "tableFrom": "contact_inquiries", "tableTo": "admin_users", "columnsFrom": ["assigned_to"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.courses": {"name": "courses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "short_name": {"name": "short_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "level": {"name": "level", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "stream": {"name": "stream", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "detailed_description": {"name": "detailed_description", "type": "text", "primaryKey": false, "notNull": false}, "eligibility": {"name": "eligibility", "type": "jsonb", "primaryKey": false, "notNull": false}, "syllabus": {"name": "syllabus", "type": "jsonb", "primaryKey": false, "notNull": false}, "career_prospects": {"name": "career_prospects", "type": "jsonb", "primaryKey": false, "notNull": false}, "average_fees": {"name": "average_fees", "type": "integer", "primaryKey": false, "notNull": false}, "average_salary": {"name": "average_salary", "type": "integer", "primaryKey": false, "notNull": false}, "is_popular": {"name": "is_popular", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "seo_title": {"name": "seo_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "seo_description": {"name": "seo_description", "type": "text", "primaryKey": false, "notNull": false}, "seo_keywords": {"name": "seo_keywords", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"courses_created_by_admin_users_id_fk": {"name": "courses_created_by_admin_users_id_fk", "tableFrom": "courses", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"courses_slug_unique": {"name": "courses_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.reviews": {"name": "reviews", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "college_id": {"name": "college_id", "type": "uuid", "primaryKey": false, "notNull": true}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "overall_rating": {"name": "overall_rating", "type": "numeric(3, 2)", "primaryKey": false, "notNull": true}, "ratings": {"name": "ratings", "type": "jsonb", "primaryKey": false, "notNull": true}, "pros": {"name": "pros", "type": "text", "primaryKey": false, "notNull": false}, "cons": {"name": "cons", "type": "text", "primaryKey": false, "notNull": false}, "review": {"name": "review", "type": "text", "primaryKey": false, "notNull": true}, "year_of_study": {"name": "year_of_study", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "graduation_year": {"name": "graduation_year", "type": "integer", "primaryKey": false, "notNull": false}, "is_anonymous": {"name": "is_anonymous", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "moderated_by": {"name": "moderated_by", "type": "uuid", "primaryKey": false, "notNull": false}, "moderated_at": {"name": "moderated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "moderation_notes": {"name": "moderation_notes", "type": "text", "primaryKey": false, "notNull": false}, "helpful_count": {"name": "helpful_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "report_count": {"name": "report_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "attachments": {"name": "attachments", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"reviews_college_id_colleges_id_fk": {"name": "reviews_college_id_colleges_id_fk", "tableFrom": "reviews", "tableTo": "colleges", "columnsFrom": ["college_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "reviews_student_id_student_users_id_fk": {"name": "reviews_student_id_student_users_id_fk", "tableFrom": "reviews", "tableTo": "student_users", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "reviews_course_id_courses_id_fk": {"name": "reviews_course_id_courses_id_fk", "tableFrom": "reviews", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "reviews_moderated_by_admin_users_id_fk": {"name": "reviews_moderated_by_admin_users_id_fk", "tableFrom": "reviews", "tableTo": "admin_users", "columnsFrom": ["moderated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.scholarship_applications": {"name": "scholarship_applications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "date_of_birth": {"name": "date_of_birth", "type": "timestamp", "primaryKey": false, "notNull": true}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "jsonb", "primaryKey": false, "notNull": true}, "academic_details": {"name": "academic_details", "type": "jsonb", "primaryKey": false, "notNull": true}, "family_income": {"name": "family_income", "type": "integer", "primaryKey": false, "notNull": true}, "course_applied_for": {"name": "course_applied_for", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "college_preferences": {"name": "college_preferences", "type": "jsonb", "primaryKey": false, "notNull": false}, "documents": {"name": "documents", "type": "jsonb", "primaryKey": false, "notNull": false}, "personal_statement": {"name": "personal_statement", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'received'"}, "reviewed_by": {"name": "reviewed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "reviewed_at": {"name": "reviewed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "review_notes": {"name": "review_notes", "type": "text", "primaryKey": false, "notNull": false}, "scholarship_amount": {"name": "scholarship_amount", "type": "integer", "primaryKey": false, "notNull": false}, "disbursement_status": {"name": "disbursement_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "disbursement_date": {"name": "disbursement_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "disbursement_transaction_id": {"name": "disbursement_transaction_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"scholarship_applications_student_id_student_users_id_fk": {"name": "scholarship_applications_student_id_student_users_id_fk", "tableFrom": "scholarship_applications", "tableTo": "student_users", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "scholarship_applications_reviewed_by_admin_users_id_fk": {"name": "scholarship_applications_reviewed_by_admin_users_id_fk", "tableFrom": "scholarship_applications", "tableTo": "admin_users", "columnsFrom": ["reviewed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_admissions": {"name": "student_admissions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "lead_id": {"name": "lead_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "jsonb", "primaryKey": false, "notNull": false}, "college_id": {"name": "college_id", "type": "uuid", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "uuid", "primaryKey": false, "notNull": true}, "year_of_admission": {"name": "year_of_admission", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "admission_date": {"name": "admission_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "college_first_year_fee": {"name": "college_first_year_fee", "type": "integer", "primaryKey": false, "notNull": false}, "scholarship_application_id": {"name": "scholarship_application_id", "type": "uuid", "primaryKey": false, "notNull": false}, "scholarship_amount_awarded": {"name": "scholarship_amount_awarded", "type": "integer", "primaryKey": false, "notNull": false}, "scholarship_disbursement_status": {"name": "scholarship_disbursement_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "scholarship_disbursement_date": {"name": "scholarship_disbursement_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "scholarship_transaction_id": {"name": "scholarship_transaction_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "scholarship_notes": {"name": "scholarship_notes", "type": "text", "primaryKey": false, "notNull": false}, "commission_amount": {"name": "commission_amount", "type": "integer", "primaryKey": false, "notNull": false}, "commission_percentage": {"name": "commission_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "commission_status": {"name": "commission_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "commission_received_date": {"name": "commission_received_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "commission_transaction_id": {"name": "commission_transaction_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "commission_notes": {"name": "commission_notes", "type": "text", "primaryKey": false, "notNull": false}, "overall_status": {"name": "overall_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'active_student'"}, "internal_notes": {"name": "internal_notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "last_updated_by": {"name": "last_updated_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"student_admissions_lead_id_contact_inquiries_id_fk": {"name": "student_admissions_lead_id_contact_inquiries_id_fk", "tableFrom": "student_admissions", "tableTo": "contact_inquiries", "columnsFrom": ["lead_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_admissions_college_id_colleges_id_fk": {"name": "student_admissions_college_id_colleges_id_fk", "tableFrom": "student_admissions", "tableTo": "colleges", "columnsFrom": ["college_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_admissions_course_id_courses_id_fk": {"name": "student_admissions_course_id_courses_id_fk", "tableFrom": "student_admissions", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_admissions_scholarship_application_id_scholarship_applications_id_fk": {"name": "student_admissions_scholarship_application_id_scholarship_applications_id_fk", "tableFrom": "student_admissions", "tableTo": "scholarship_applications", "columnsFrom": ["scholarship_application_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_admissions_created_by_admin_users_id_fk": {"name": "student_admissions_created_by_admin_users_id_fk", "tableFrom": "student_admissions", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_admissions_last_updated_by_admin_users_id_fk": {"name": "student_admissions_last_updated_by_admin_users_id_fk", "tableFrom": "student_admissions", "tableTo": "admin_users", "columnsFrom": ["last_updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_users": {"name": "student_users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "timestamp", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "jsonb", "primaryKey": false, "notNull": false}, "profile_picture": {"name": "profile_picture", "type": "text", "primaryKey": false, "notNull": false}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "auth_provider": {"name": "auth_provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'email'"}, "auth_provider_id": {"name": "auth_provider_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"student_users_email_unique": {"name": "student_users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.website_settings": {"name": "website_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"website_settings_updated_by_admin_users_id_fk": {"name": "website_settings_updated_by_admin_users_id_fk", "tableFrom": "website_settings", "tableTo": "admin_users", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"website_settings_key_unique": {"name": "website_settings_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}