import { useQuery } from '@tanstack/react-query';
import { getAuthHeaders } from './use-admin-auth';

// Types
export interface ActivityLog {
  id: string;
  adminId: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  // Joined data
  adminName?: string;
  adminEmail?: string;
}

interface ActivityLogsResponse {
  success: boolean;
  data: ActivityLog[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  error?: string;
}

interface ActivityLogFilters {
  page?: number;
  limit?: number;
  adminId?: string;
  action?: string;
  resource?: string;
  resourceId?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Get activity logs
export const useActivityLogs = (filters: ActivityLogFilters = {}) => {
  const { page = 1, limit = 20, ...otherFilters } = filters;
  
  return useQuery<ActivityLogsResponse, Error>({
    queryKey: ['admin', 'activity-logs', { page, limit, ...otherFilters }],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...Object.entries(otherFilters).reduce((acc, [key, value]) => {
          if (value !== undefined && value !== '') {
            acc[key] = value.toString();
          }
          return acc;
        }, {} as Record<string, string>)
      });

      const response = await fetch(`/api/admin/activity-logs?${params}`, {
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch activity logs');
      }

      return result;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get activity log statistics
export const useActivityLogStats = (filters: { dateFrom?: string; dateTo?: string } = {}) => {
  return useQuery<any, Error>({
    queryKey: ['admin', 'activity-logs', 'stats', filters],
    queryFn: async () => {
      const params = new URLSearchParams();
      
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom);
      if (filters.dateTo) params.append('dateTo', filters.dateTo);

      const response = await fetch(`/api/admin/activity-logs/stats?${params}`, {
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch activity log statistics');
      }

      return result;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get recent activities for dashboard
export const useRecentActivities = (limit: number = 10) => {
  return useQuery<ActivityLogsResponse, Error>({
    queryKey: ['admin', 'recent-activities', limit],
    queryFn: async () => {
      const response = await fetch(`/api/admin/activity-logs?limit=${limit}&sortBy=createdAt&sortOrder=desc`, {
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch recent activities');
      }

      return result;
    },
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};
