import { useQuery } from '@tanstack/react-query';
import { getAuthHeaders } from './use-admin-auth';

// Types
interface DashboardStatsResponse {
  success: boolean;
  data?: {
    stats: {
      colleges: number;
      courses: number;
      reviews: number;
      pendingReviews: number;
      leads: number;
      newLeads: number;
      scholarshipApplications: number;
      admissions: number;
      articles: number;
    };
    recentActivity: Array<{
      id: string;
      action: string;
      resource: string;
      createdAt: string;
      adminName: string;
    }>;
  };
  error?: string;
}

// Dashboard stats query
export const useAdminDashboardStats = () => {
  return useQuery<DashboardStatsResponse, Error>({
    queryKey: ['admin', 'dashboard', 'stats'],
    queryFn: async () => {
      const response = await fetch('/api/admin/dashboard/stats', {
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch dashboard stats');
      }

      return result;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
};
