import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getAuthHeaders } from './use-admin-auth';

// Types
export interface AdminUser {
  id: string;
  name: string;
  email: string;
  role: 'super_admin' | 'content_manager' | 'review_moderator' | 'lead_manager' | 'finance_officer' | 'seo_specialist';
  isActive: boolean;
  avatar?: string;
  phone?: string;
  department?: string;
  permissions?: string[];
  lastLoginAt?: string;
  passwordChangedAt?: string;
  twoFactorEnabled: boolean;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
}

interface AdminUsersResponse {
  success: boolean;
  data: AdminUser[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  error?: string;
}

interface AdminUserResponse {
  success: boolean;
  data: AdminUser;
  error?: string;
}

interface AdminUserFilters {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  status?: string;
  department?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Get admin users list
export const useAdminUsers = (filters: AdminUserFilters = {}) => {
  const { page = 1, limit = 10, search = '', role = 'all', status = 'all', ...otherFilters } = filters;
  
  return useQuery<AdminUsersResponse, Error>({
    queryKey: ['admin', 'users', { page, limit, search, role, status, ...otherFilters }],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
        ...(role !== 'all' && { role }),
        ...(status !== 'all' && { status }),
        ...Object.entries(otherFilters).reduce((acc, [key, value]) => {
          if (value !== undefined && value !== '') {
            acc[key] = value.toString();
          }
          return acc;
        }, {} as Record<string, string>)
      });

      const response = await fetch(`/api/admin/users?${params}`, {
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch admin users');
      }

      return result;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get single admin user
export const useAdminUser = (id: string) => {
  return useQuery<AdminUserResponse, Error>({
    queryKey: ['admin', 'users', id],
    queryFn: async () => {
      const response = await fetch(`/api/admin/users/${id}`, {
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch admin user');
      }

      return result;
    },
    enabled: !!id,
  });
};

// Create admin user
export const useCreateAdminUser = () => {
  const queryClient = useQueryClient();

  return useMutation<AdminUserResponse, Error, Partial<AdminUser> & { password: string }>({
    mutationFn: async (data) => {
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create admin user');
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
    },
  });
};

// Update admin user
export const useUpdateAdminUser = () => {
  const queryClient = useQueryClient();

  return useMutation<AdminUserResponse, Error, { id: string; data: Partial<AdminUser> }>({
    mutationFn: async ({ id, data }) => {
      const response = await fetch(`/api/admin/users/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update admin user');
      }

      return result;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'users', id] });
    },
  });
};

// Toggle admin user status
export const useToggleAdminUserStatus = () => {
  const queryClient = useQueryClient();

  return useMutation<AdminUserResponse, Error, { id: string; isActive: boolean }>({
    mutationFn: async ({ id, isActive }) => {
      const response = await fetch(`/api/admin/users/${id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        },
        body: JSON.stringify({ isActive }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update admin user status');
      }

      return result;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'users', id] });
    },
  });
};

// Reset admin user password
export const useResetAdminUserPassword = () => {
  const queryClient = useQueryClient();

  return useMutation<{ success: boolean; message: string }, Error, { id: string; newPassword: string }>({
    mutationFn: async ({ id, newPassword }) => {
      const response = await fetch(`/api/admin/users/${id}/reset-password`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        },
        body: JSON.stringify({ newPassword }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to reset password');
      }

      return result;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users', id] });
    },
  });
};

// Delete admin user
export const useDeleteAdminUser = () => {
  const queryClient = useQueryClient();

  return useMutation<{ success: boolean }, Error, string>({
    mutationFn: async (id) => {
      const response = await fetch(`/api/admin/users/${id}`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete admin user');
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
    },
  });
};

// Get admin user activity logs
export const useAdminUserActivityLogs = (userId: string, filters: { page?: number; limit?: number } = {}) => {
  const { page = 1, limit = 20 } = filters;
  
  return useQuery<any, Error>({
    queryKey: ['admin', 'users', userId, 'activity-logs', { page, limit }],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      const response = await fetch(`/api/admin/users/${userId}/activity-logs?${params}`, {
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch activity logs');
      }

      return result;
    },
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};
