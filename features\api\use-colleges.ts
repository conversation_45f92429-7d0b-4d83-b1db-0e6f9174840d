import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getAuthHeaders } from './use-admin-auth';

// Types
export interface College {
  id: string;
  name: string;
  shortName?: string;
  slug: string;
  description?: string;
  about?: string;
  vision?: string;
  mission?: string;
  establishedYear?: number;
  collegeType?: string;
  affiliation?: string;
  approvals?: any;
  accreditations?: any;
  location?: any;
  contactInfo?: any;
  logo?: string;
  bannerImages?: any;
  nirfRanking?: number;
  overallRating?: number;
  totalReviews: number;
  totalStudents?: number;
  facultyCount?: number;
  campusArea?: string;
  isVerified: boolean;
  isFeatured: boolean;
  isPublished: boolean;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
}

interface CollegesResponse {
  success: boolean;
  data: College[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  error?: string;
}

interface CollegeResponse {
  success: boolean;
  data: College;
  error?: string;
}

interface CollegeFilters {
  page?: number;
  limit?: number;
  search?: string;
  filter?: string;
  collegeType?: string;
  isPublished?: boolean;
  isVerified?: boolean;
  isFeatured?: boolean;
}

// Get colleges list
export const useColleges = (filters: CollegeFilters = {}) => {
  const { page = 1, limit = 10, search = '', filter = 'all', ...otherFilters } = filters;
  
  return useQuery<CollegesResponse, Error>({
    queryKey: ['admin', 'colleges', { page, limit, search, filter, ...otherFilters }],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
        ...(filter !== 'all' && { filter }),
        ...Object.entries(otherFilters).reduce((acc, [key, value]) => {
          if (value !== undefined && value !== '') {
            acc[key] = value.toString();
          }
          return acc;
        }, {} as Record<string, string>)
      });

      const response = await fetch(`/api/admin/colleges?${params}`, {
        headers: {
          ...getAuthHeaders(),
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch colleges');
      }

      return result;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get single college
export const useCollege = (id: string) => {
  return useQuery<CollegeResponse, Error>({
    queryKey: ['admin', 'colleges', id],
    queryFn: async () => {
      const response = await fetch(`/api/admin/colleges/${id}`, {
        headers: {
          ...getAuthHeaders(),
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch college');
      }

      return result;
    },
    enabled: !!id,
  });
};

// Create college
export const useCreateCollege = () => {
  const queryClient = useQueryClient();

  return useMutation<CollegeResponse, Error, Partial<College>>({
    mutationFn: async (data) => {
      const response = await fetch('/api/admin/colleges', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        } as HeadersInit,
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create college');
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'colleges'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
    },
  });
};

// Update college
export const useUpdateCollege = () => {
  const queryClient = useQueryClient();

  return useMutation<CollegeResponse, Error, { id: string; data: Partial<College> }>({
    mutationFn: async ({ id, data }) => {
      const response = await fetch(`/api/admin/colleges/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        } as HeadersInit,
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update college');
      }

      return result;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'colleges'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'colleges', id] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
    },
  });
};

// Delete college
export const useDeleteCollege = () => {
  const queryClient = useQueryClient();

  return useMutation<{ success: boolean }, Error, string>({
    mutationFn: async (id) => {
      const response = await fetch(`/api/admin/colleges/${id}`, {
        method: 'DELETE',
        headers: {
          ...getAuthHeaders(),
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete college');
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'colleges'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
    },
  });
};

// Toggle college status
export const useToggleCollegeStatus = () => {
  const queryClient = useQueryClient();

  return useMutation<CollegeResponse, Error, { id: string; field: 'isPublished' | 'isVerified' | 'isFeatured'; value: boolean }>({
    mutationFn: async ({ id, field, value }) => {
      const response = await fetch(`/api/admin/colleges/${id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        } as HeadersInit,
        body: JSON.stringify({ [field]: value }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update college status');
      }

      return result;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'colleges'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'colleges', id] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
    },
  });
};
