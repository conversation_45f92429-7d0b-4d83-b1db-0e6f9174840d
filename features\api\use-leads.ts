import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthToken } from '@/hooks/use-local-storage';

// Types
export interface Lead {
  id: string;
  name: string;
  email: string;
  phone?: string;
  subject?: string;
  message: string;
  source: 'contact_form' | 'popup' | 'phone' | 'social_media' | 'referral' | 'other';
  status: 'new' | 'open' | 'counselling_completed' | 'admission_confirmed' | 'closed' | 'rejected' | 'follow_up_required';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  interestedCourses?: string[];
  preferredLocation?: string;
  budget?: string;
  timeline?: string;
  assignedTo?: string;
  tags?: string[];
  notes?: string;
  followUpDate?: string;
  lastContactDate?: string;
  conversionValue?: number;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  createdAt: string;
  updatedAt: string;
  // Joined data
  assignedToName?: string;
}

interface LeadsResponse {
  success: boolean;
  data: Lead[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  error?: string;
}

interface LeadResponse {
  success: boolean;
  data: Lead;
  error?: string;
}

interface LeadFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  priority?: string;
  source?: string;
  assignedTo?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Get leads list
export const useLeads = (filters: LeadFilters = {}) => {
  const { page = 1, limit = 10, search = '', status = 'all', priority = 'all', ...otherFilters } = filters;
  const { getAuthHeaders, isClient } = useAuthToken();

  return useQuery<LeadsResponse, Error>({
    queryKey: ['admin', 'leads', { page, limit, search, status, priority, ...otherFilters }],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
        ...(status !== 'all' && { status }),
        ...(priority !== 'all' && { priority }),
        ...Object.entries(otherFilters).reduce((acc, [key, value]) => {
          if (value !== undefined && value !== '') {
            acc[key] = value.toString();
          }
          return acc;
        }, {} as Record<string, string>)
      });

      const response = await fetch(`/api/leads?${params}`, {
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch leads');
      }

      return result;
    },
    enabled: isClient, // Only run query when client-side
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get single lead
export const useLead = (id: string) => {
  const { getAuthHeaders, isClient } = useAuthToken();

  return useQuery<LeadResponse, Error>({
    queryKey: ['admin', 'leads', id],
    queryFn: async () => {
      const response = await fetch(`/api/leads/${id}`, {
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch lead');
      }

      return result;
    },
    enabled: !!id && isClient,
  });
};

// Create lead
export const useCreateLead = () => {
  const queryClient = useQueryClient();
  const { getAuthHeaders } = useAuthToken();

  return useMutation<LeadResponse, Error, Partial<Lead>>({
    mutationFn: async (data) => {
      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create lead');
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'leads'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
    },
  });
};

// Update lead
export const useUpdateLead = () => {
  const queryClient = useQueryClient();
  const { getAuthHeaders } = useAuthToken();

  return useMutation<LeadResponse, Error, { id: string; data: Partial<Lead> }>({
    mutationFn: async ({ id, data }) => {
      const response = await fetch(`/api/leads/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update lead');
      }

      return result;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'leads'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'leads', id] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
    },
  });
};

// Update lead status
export const useUpdateLeadStatus = () => {
  const queryClient = useQueryClient();
  const { getAuthHeaders } = useAuthToken();

  return useMutation<LeadResponse, Error, { id: string; status: Lead['status']; notes?: string }>({
    mutationFn: async ({ id, status, notes }) => {
      const response = await fetch(`/api/leads/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        },
        body: JSON.stringify({ status, notes }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update lead status');
      }

      return result;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'leads'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'leads', id] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
    },
  });
};

// Assign lead
export const useAssignLead = () => {
  const queryClient = useQueryClient();
  const { getAuthHeaders } = useAuthToken();

  return useMutation<LeadResponse, Error, { id: string; assignedTo: string }>({
    mutationFn: async ({ id, assignedTo }) => {
      const response = await fetch(`/api/leads/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        },
        body: JSON.stringify({ assignedTo }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to assign lead');
      }

      return result;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'leads'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'leads', id] });
    },
  });
};

// Delete lead
export const useDeleteLead = () => {
  const queryClient = useQueryClient();
  const { getAuthHeaders } = useAuthToken();

  return useMutation<{ success: boolean }, Error, string>({
    mutationFn: async (id) => {
      const response = await fetch(`/api/leads/${id}`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete lead');
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'leads'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
    },
  });
};

// Get lead statistics
export const useLeadStats = () => {
  const { getAuthHeaders, isClient } = useAuthToken();

  return useQuery<any, Error>({
    queryKey: ['admin', 'leads', 'stats'],
    queryFn: async () => {
      const response = await fetch('/api/leads/stats', {
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch lead statistics');
      }

      return result;
    },
    enabled: isClient,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};
