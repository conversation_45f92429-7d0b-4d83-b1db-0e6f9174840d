import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getAuthHeaders } from './use-admin-auth';

// Types
export interface Review {
  id: string;
  collegeId: string;
  studentId: string;
  courseId?: string;
  title: string;
  overallRating: number;
  ratings: {
    academics: number;
    faculty: number;
    infrastructure: number;
    placements: number;
    socialLife: number;
    valueForMoney: number;
  };
  pros?: string;
  cons?: string;
  review: string;
  yearOfStudy?: string;
  graduationYear?: number;
  isAnonymous: boolean;
  status: 'pending' | 'approved' | 'rejected';
  moderatedBy?: string;
  moderatedAt?: string;
  moderationNotes?: string;
  helpfulCount: number;
  reportCount: number;
  attachments?: any;
  createdAt: string;
  updatedAt: string;
  // Joined data
  collegeName?: string;
  studentName?: string;
  courseName?: string;
}

interface ReviewsResponse {
  success: boolean;
  data: Review[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  error?: string;
}

interface ReviewResponse {
  success: boolean;
  data: Review;
  error?: string;
}

interface ReviewFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  collegeId?: string;
  rating?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Get reviews list
export const useReviews = (filters: ReviewFilters = {}) => {
  const { page = 1, limit = 10, search = '', status = 'all', ...otherFilters } = filters;
  
  return useQuery<ReviewsResponse, Error>({
    queryKey: ['admin', 'reviews', { page, limit, search, status, ...otherFilters }],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
        ...(status !== 'all' && { status }),
        ...Object.entries(otherFilters).reduce((acc, [key, value]) => {
          if (value !== undefined && value !== '') {
            acc[key] = value.toString();
          }
          return acc;
        }, {} as Record<string, string>)
      });

      const response = await fetch(`/api/admin/reviews?${params}`, {
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch reviews');
      }

      return result;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get single review
export const useReview = (id: string) => {
  return useQuery<ReviewResponse, Error>({
    queryKey: ['admin', 'reviews', id],
    queryFn: async () => {
      const response = await fetch(`/api/admin/reviews/${id}`, {
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch review');
      }

      return result;
    },
    enabled: !!id,
  });
};

// Moderate review (approve/reject)
export const useModerateReview = () => {
  const queryClient = useQueryClient();

  return useMutation<ReviewResponse, Error, { id: string; action: 'approve' | 'reject'; notes?: string }>({
    mutationFn: async ({ id, action, notes }) => {
      const response = await fetch(`/api/admin/reviews/${id}/moderate`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        },
        body: JSON.stringify({ action, notes }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to moderate review');
      }

      return result;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'reviews'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'reviews', id] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
    },
  });
};

// Delete review
export const useDeleteReview = () => {
  const queryClient = useQueryClient();

  return useMutation<{ success: boolean }, Error, string>({
    mutationFn: async (id) => {
      const response = await fetch(`/api/admin/reviews/${id}`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete review');
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'reviews'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'dashboard'] });
    },
  });
};

// Update review helpful count
export const useUpdateReviewHelpful = () => {
  const queryClient = useQueryClient();

  return useMutation<ReviewResponse, Error, { id: string; helpful: boolean }>({
    mutationFn: async ({ id, helpful }) => {
      const response = await fetch(`/api/admin/reviews/${id}/helpful`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders(),
        },
        body: JSON.stringify({ helpful }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update review');
      }

      return result;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'reviews'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'reviews', id] });
    },
  });
};

// Get review statistics
export const useReviewStats = () => {
  return useQuery<any, Error>({
    queryKey: ['admin', 'reviews', 'stats'],
    queryFn: async () => {
      const response = await fetch('/api/admin/reviews/stats', {
        headers: getAuthHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch review statistics');
      }

      return result;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};
