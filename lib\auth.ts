import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { db } from '@/db';
import { users, userSessions, type User } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = '7d';

export interface AuthUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  status: string;
}

export class AuthService {
  // Hash password
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 12);
  }

  // Verify password
  static async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  // Generate JWT token
  static generateToken(user: AuthUser): string {
    return jwt.sign(
      {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );
  }

  // Verify JWT token
  static verifyToken(token: string): AuthUser | null {
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as any;
      return decoded;
    } catch (error) {
      return null;
    }
  }

  // Register new user
  static async register(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
  }): Promise<{ user: AuthUser; token: string } | { error: string }> {
    try {
      // Check if user already exists
      const existingUser = await db
        .select()
        .from(users)
        .where(eq(users.email, userData.email))
        .limit(1);

      if (existingUser.length > 0) {
        return { error: 'User already exists with this email' };
      }

      // Hash password
      const hashedPassword = await this.hashPassword(userData.password);

      // Create user
      const [newUser] = await db
        .insert(users)
        .values({
          email: userData.email,
          password: hashedPassword,
          firstName: userData.firstName,
          lastName: userData.lastName,
          phone: userData.phone,
          role: 'student',
          status: 'pending_verification',
        })
        .returning();

      const authUser: AuthUser = {
        id: newUser.id,
        email: newUser.email,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        role: newUser.role,
        status: newUser.status,
      };

      const token = this.generateToken(authUser);

      // Create session
      await db.insert(userSessions).values({
        userId: newUser.id,
        token,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      });

      return { user: authUser, token };
    } catch (error) {
      console.error('Registration error:', error);
      return { error: 'Registration failed' };
    }
  }

  // Login user
  static async login(email: string, password: string): Promise<{ user: AuthUser; token: string } | { error: string }> {
    try {
      // Find user
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.email, email))
        .limit(1);

      if (!user || !user.password) {
        return { error: 'Invalid email or password' };
      }

      // Verify password
      const isValidPassword = await this.verifyPassword(password, user.password);
      if (!isValidPassword) {
        return { error: 'Invalid email or password' };
      }

      // Check if user is active
      if (user.status === 'suspended') {
        return { error: 'Account is suspended' };
      }

      const authUser: AuthUser = {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status,
      };

      const token = this.generateToken(authUser);

      // Create session
      await db.insert(userSessions).values({
        userId: user.id,
        token,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      });

      // Update last login
      await db
        .update(users)
        .set({ lastLoginAt: new Date() })
        .where(eq(users.id, user.id));

      return { user: authUser, token };
    } catch (error) {
      console.error('Login error:', error);
      return { error: 'Login failed' };
    }
  }

  // Get user by token
  static async getUserByToken(token: string): Promise<AuthUser | null> {
    try {
      // Verify token
      const decoded = this.verifyToken(token);
      if (!decoded) return null;

      // Check if session exists and is valid
      const [session] = await db
        .select()
        .from(userSessions)
        .where(and(
          eq(userSessions.token, token),
          eq(userSessions.userId, decoded.id)
        ))
        .limit(1);

      if (!session || session.expiresAt < new Date()) {
        return null;
      }

      // Get user details
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, decoded.id))
        .limit(1);

      if (!user) return null;

      return {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status,
      };
    } catch (error) {
      console.error('Get user by token error:', error);
      return null;
    }
  }

  // Logout user
  static async logout(token: string): Promise<boolean> {
    try {
      await db
        .delete(userSessions)
        .where(eq(userSessions.token, token));
      return true;
    } catch (error) {
      console.error('Logout error:', error);
      return false;
    }
  }

  // Check if user has permission
  static hasPermission(userRole: string, requiredRoles: string[]): boolean {
    return requiredRoles.includes(userRole);
  }

  // Check if user is admin
  static isAdmin(userRole: string): boolean {
    return ['admin', 'super_admin'].includes(userRole);
  }

  // Check if user is super admin
  static isSuperAdmin(userRole: string): boolean {
    return userRole === 'super_admin';
  }
}
