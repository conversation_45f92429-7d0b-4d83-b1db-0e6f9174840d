import { pgTable, text, timestamp, integer, boolean, decimal, jsonb, uuid, varchar, serial } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Admin Users Table
export const adminUsers: any = pgTable('admin_users', {
  id: uuid('id').defaultRandom().primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  password: text('password').notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  role: varchar('role', { length: 50 }).notNull().default('admin'),
  isActive: boolean('is_active').notNull().default(true),
  lastLogin: timestamp('last_login'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by').references(() => adminUsers.id),
});

// Admin Roles Table
export const adminRoles = pgTable('admin_roles', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: varchar('name', { length: 100 }).notNull().unique(),
  description: text('description'),
  permissions: jsonb('permissions').notNull(), // Array of permission strings
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Admin Activity Logs
export const adminActivityLogs = pgTable('admin_activity_logs', {
  id: uuid('id').defaultRandom().primaryKey(),
  adminId: uuid('admin_id').notNull().references(() => adminUsers.id),
  action: varchar('action', { length: 100 }).notNull(),
  resource: varchar('resource', { length: 100 }).notNull(),
  resourceId: varchar('resource_id', { length: 255 }),
  details: jsonb('details'),
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Colleges Table
export const colleges = pgTable('colleges', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  shortName: varchar('short_name', { length: 100 }),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  description: text('description'),
  about: text('about'),
  vision: text('vision'),
  mission: text('mission'),
  establishedYear: integer('established_year'),
  collegeType: varchar('college_type', { length: 50 }), // Government, Private, Deemed, etc.
  affiliation: varchar('affiliation', { length: 255 }),
  approvals: jsonb('approvals'), // Array of approval bodies
  accreditations: jsonb('accreditations'), // Array of accreditations
  location: jsonb('location'), // {address, city, state, country, pincode, coordinates}
  contactInfo: jsonb('contact_info'), // {phone, email, website, fax}
  logo: text('logo'),
  bannerImages: jsonb('banner_images'), // Array of image URLs
  nirfRanking: integer('nirf_ranking'),
  overallRating: decimal('overall_rating', { precision: 3, scale: 2 }),
  totalReviews: integer('total_reviews').default(0),
  totalStudents: integer('total_students'),
  facultyCount: integer('faculty_count'),
  campusArea: varchar('campus_area', { length: 100 }),
  isVerified: boolean('is_verified').default(false),
  isFeatured: boolean('is_featured').default(false),
  isPublished: boolean('is_published').default(false),
  seoTitle: varchar('seo_title', { length: 255 }),
  seoDescription: text('seo_description'),
  seoKeywords: text('seo_keywords'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by').references(() => adminUsers.id),
});

// Courses Table
export const courses = pgTable('courses', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  shortName: varchar('short_name', { length: 100 }),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  level: varchar('level', { length: 50 }).notNull(), // Undergraduate, Postgraduate, Doctoral
  duration: varchar('duration', { length: 50 }),
  stream: varchar('stream', { length: 100 }),
  description: text('description'),
  detailedDescription: text('detailed_description'),
  eligibility: jsonb('eligibility'),
  syllabus: jsonb('syllabus'),
  careerProspects: jsonb('career_prospects'),
  averageFees: integer('average_fees'),
  averageSalary: integer('average_salary'),
  isPopular: boolean('is_popular').default(false),
  isPublished: boolean('is_published').default(false),
  seoTitle: varchar('seo_title', { length: 255 }),
  seoDescription: text('seo_description'),
  seoKeywords: text('seo_keywords'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by').references(() => adminUsers.id),
});

// College Courses Junction Table
export const collegesCourses = pgTable('colleges_courses', {
  id: uuid('id').defaultRandom().primaryKey(),
  collegeId: uuid('college_id').notNull().references(() => colleges.id, { onDelete: 'cascade' }),
  courseId: uuid('course_id').notNull().references(() => courses.id, { onDelete: 'cascade' }),
  specializations: jsonb('specializations'),
  fees: jsonb('fees'), // {tuition, hostel, other, total}
  seats: integer('seats'),
  selectionProcess: text('selection_process'),
  entranceExams: jsonb('entrance_exams'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Student Users Table
export const studentUsers = pgTable('student_users', {
  id: uuid('id').defaultRandom().primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  password: text('password'),
  name: varchar('name', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 20 }),
  dateOfBirth: timestamp('date_of_birth'),
  gender: varchar('gender', { length: 10 }),
  address: jsonb('address'),
  profilePicture: text('profile_picture'),
  isVerified: boolean('is_verified').default(false),
  isActive: boolean('is_active').default(true),
  lastLogin: timestamp('last_login'),
  authProvider: varchar('auth_provider', { length: 50 }).default('email'), // email, google, facebook
  authProviderId: varchar('auth_provider_id', { length: 255 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Reviews Table
export const reviews = pgTable('reviews', {
  id: uuid('id').defaultRandom().primaryKey(),
  collegeId: uuid('college_id').notNull().references(() => colleges.id, { onDelete: 'cascade' }),
  studentId: uuid('student_id').notNull().references(() => studentUsers.id, { onDelete: 'cascade' }),
  courseId: uuid('course_id').references(() => courses.id),
  title: varchar('title', { length: 255 }).notNull(),
  overallRating: decimal('overall_rating', { precision: 3, scale: 2 }).notNull(),
  ratings: jsonb('ratings').notNull(), // {academics, faculty, infrastructure, placements, socialLife, valueForMoney}
  pros: text('pros'),
  cons: text('cons'),
  review: text('review').notNull(),
  yearOfStudy: varchar('year_of_study', { length: 50 }),
  graduationYear: integer('graduation_year'),
  isAnonymous: boolean('is_anonymous').default(false),
  status: varchar('status', { length: 20 }).default('pending'), // pending, approved, rejected
  moderatedBy: uuid('moderated_by').references(() => adminUsers.id),
  moderatedAt: timestamp('moderated_at'),
  moderationNotes: text('moderation_notes'),
  helpfulCount: integer('helpful_count').default(0),
  reportCount: integer('report_count').default(0),
  attachments: jsonb('attachments'), // Array of image/video URLs
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Contact Inquiries / Leads Table
export const contactInquiries = pgTable('contact_inquiries', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  email: varchar('email', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 20 }),
  subject: varchar('subject', { length: 255 }),
  message: text('message').notNull(),
  source: varchar('source', { length: 50 }).default('contact_form'), // contact_form, popup, phone, etc.
  status: varchar('status', { length: 50 }).default('new'), // new, open, counselling_completed, admission_confirmed, closed, rejected, follow_up_required
  priority: varchar('priority', { length: 20 }).default('medium'), // low, medium, high, urgent
  assignedTo: uuid('assigned_to').references(() => adminUsers.id),
  interestedCourses: jsonb('interested_courses'),
  interestedColleges: jsonb('interested_colleges'),
  notes: text('notes'),
  followUpDate: timestamp('follow_up_date'),
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  utmSource: varchar('utm_source', { length: 100 }),
  utmMedium: varchar('utm_medium', { length: 100 }),
  utmCampaign: varchar('utm_campaign', { length: 100 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Scholarship Applications Table
export const scholarshipApplications = pgTable('scholarship_applications', {
  id: uuid('id').defaultRandom().primaryKey(),
  studentId: uuid('student_id').references(() => studentUsers.id),
  name: varchar('name', { length: 255 }).notNull(),
  email: varchar('email', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 20 }).notNull(),
  dateOfBirth: timestamp('date_of_birth').notNull(),
  gender: varchar('gender', { length: 10 }).notNull(),
  category: varchar('category', { length: 50 }), // General, SC, ST, OBC, etc.
  address: jsonb('address').notNull(),
  academicDetails: jsonb('academic_details').notNull(),
  familyIncome: integer('family_income').notNull(),
  courseAppliedFor: varchar('course_applied_for', { length: 255 }).notNull(),
  collegePreferences: jsonb('college_preferences'),
  documents: jsonb('documents'), // Array of document URLs
  personalStatement: text('personal_statement'),
  status: varchar('status', { length: 50 }).default('received'), // received, under_review, shortlisted, approved, rejected, disbursed
  reviewedBy: uuid('reviewed_by').references(() => adminUsers.id),
  reviewedAt: timestamp('reviewed_at'),
  reviewNotes: text('review_notes'),
  scholarshipAmount: integer('scholarship_amount'),
  disbursementStatus: varchar('disbursement_status', { length: 50 }), // pending, processed, disbursed, on_hold, rejected
  disbursementDate: timestamp('disbursement_date'),
  disbursementTransactionId: varchar('disbursement_transaction_id', { length: 255 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Student Admissions & Financial Tracking Table
export const studentAdmissions = pgTable('student_admissions', {
  id: uuid('id').defaultRandom().primaryKey(),
  studentId: varchar('student_id', { length: 255 }).notNull(), // Can be UUID or custom ID
  leadId: uuid('lead_id').references(() => contactInquiries.id),
  name: varchar('name', { length: 255 }).notNull(),
  email: varchar('email', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 20 }).notNull(),
  address: jsonb('address'),
  collegeId: uuid('college_id').notNull().references(() => colleges.id),
  courseId: uuid('course_id').notNull().references(() => courses.id),
  yearOfAdmission: varchar('year_of_admission', { length: 10 }).notNull(),
  admissionDate: timestamp('admission_date').notNull(),
  collegeFirstYearFee: integer('college_first_year_fee'),
  scholarshipApplicationId: uuid('scholarship_application_id').references(() => scholarshipApplications.id),
  scholarshipAmountAwarded: integer('scholarship_amount_awarded'),
  scholarshipDisbursementStatus: varchar('scholarship_disbursement_status', { length: 50 }),
  scholarshipDisbursementDate: timestamp('scholarship_disbursement_date'),
  scholarshipTransactionId: varchar('scholarship_transaction_id', { length: 255 }),
  scholarshipNotes: text('scholarship_notes'),
  commissionAmount: integer('commission_amount'),
  commissionPercentage: decimal('commission_percentage', { precision: 5, scale: 2 }),
  commissionStatus: varchar('commission_status', { length: 50 }), // pending_invoice, invoice_sent, partially_received, fully_received, disputed, not_applicable
  commissionReceivedDate: timestamp('commission_received_date'),
  commissionTransactionId: varchar('commission_transaction_id', { length: 255 }),
  commissionNotes: text('commission_notes'),
  overallStatus: varchar('overall_status', { length: 50 }).default('active_student'), // active_student, withdrew_admission, course_completed, dropout
  internalNotes: text('internal_notes'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by').references(() => adminUsers.id),
  lastUpdatedBy: uuid('last_updated_by').references(() => adminUsers.id),
});

// Articles/Blog Posts Table
export const articles = pgTable('articles', {
  id: uuid('id').defaultRandom().primaryKey(),
  title: varchar('title', { length: 255 }).notNull(),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  excerpt: text('excerpt'),
  content: text('content').notNull(),
  featuredImage: text('featured_image'),
  category: varchar('category', { length: 100 }),
  tags: jsonb('tags'), // Array of tags
  authorId: uuid('author_id').notNull().references(() => adminUsers.id),
  status: varchar('status', { length: 20 }).default('draft'), // draft, published, archived
  publishedAt: timestamp('published_at'),
  readTime: integer('read_time'), // in minutes
  viewCount: integer('view_count').default(0),
  seoTitle: varchar('seo_title', { length: 255 }),
  seoDescription: text('seo_description'),
  seoKeywords: text('seo_keywords'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Website Settings Table
export const websiteSettings = pgTable('website_settings', {
  id: uuid('id').defaultRandom().primaryKey(),
  key: varchar('key', { length: 100 }).notNull().unique(),
  value: jsonb('value'),
  description: text('description'),
  category: varchar('category', { length: 50 }),
  isPublic: boolean('is_public').default(false),
  updatedBy: uuid('updated_by').references(() => adminUsers.id),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Define Relations
export const adminUsersRelations = relations(adminUsers, ({ many }) => ({
  activityLogs: many(adminActivityLogs),
  createdColleges: many(colleges),
  createdCourses: many(courses),
  moderatedReviews: many(reviews),
  assignedInquiries: many(contactInquiries),
  reviewedScholarships: many(scholarshipApplications),
  createdAdmissions: many(studentAdmissions),
  articles: many(articles),
}));

export const collegesRelations = relations(colleges, ({ many }) => ({
  courses: many(collegesCourses),
  reviews: many(reviews),
  admissions: many(studentAdmissions),
}));

export const coursesRelations = relations(courses, ({ many }) => ({
  colleges: many(collegesCourses),
  reviews: many(reviews),
  admissions: many(studentAdmissions),
}));

export const studentUsersRelations = relations(studentUsers, ({ many }) => ({
  reviews: many(reviews),
  scholarshipApplications: many(scholarshipApplications),
}));

export const reviewsRelations = relations(reviews, ({ one }) => ({
  college: one(colleges, {
    fields: [reviews.collegeId],
    references: [colleges.id],
  }),
  student: one(studentUsers, {
    fields: [reviews.studentId],
    references: [studentUsers.id],
  }),
  course: one(courses, {
    fields: [reviews.courseId],
    references: [courses.id],
  }),
  moderator: one(adminUsers, {
    fields: [reviews.moderatedBy],
    references: [adminUsers.id],
  }),
}));
