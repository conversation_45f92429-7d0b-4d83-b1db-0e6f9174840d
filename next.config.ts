import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  serverExternalPackages: ['@tanstack/react-query'],

  webpack: (config, { isServer }) => {
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push({
        '@tanstack/react-query': '@tanstack/react-query',
        '@tanstack/react-query-devtools': '@tanstack/react-query-devtools',
      });
    }
    return config;
  },
};

export default nextConfig;
