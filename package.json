{"name": "college-campus", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:introspect": "drizzle-kit introspect", "db:seed": "tsx scripts/seed-admin.ts"}, "dependencies": {"@hono/zod-validator": "^0.5.0", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.4.7", "drizzle-orm": "^0.36.4", "drizzle-zod": "^0.8.1", "hono": "^4.7.10", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "next": "15.3.2", "node-fetch": "^3.3.2", "postgres": "^3.4.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.4", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "undici": "^7.10.0", "zod": "^3.25.20"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/typography": "^0.5.15", "@types/node": "^20", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.6", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}