# Course Management System - Implementation Plan

## 📋 Overview

Comprehensive course management system that provides detailed information about academic programs, their relationships with colleges, career prospects, and admission requirements.

## 🎯 Objectives

- **Primary**: Complete course catalog with detailed information and college relationships
- **Secondary**: Career guidance system and admission pathway mapping
- **Tertiary**: Integration with college data and student recommendation engine

## 📊 Current Status

### ✅ Completed Features
- Basic course database schema
- Admin course CRUD operations
- Public course listing and display
- Course detail pages (basic structure)
- API endpoints for course data
- Course-specific search and filtering

### 🔄 Partially Implemented
- Course-college relationships
- Career prospects data
- Eligibility criteria management

### 📋 Pending Features
- Advanced course profiles
- Curriculum management
- Entrance exam integration
- Scholarship information
- Course comparison tools
- Career pathway mapping

## 🗄 Database Schema Enhancements

### Current Schema
```sql
-- courses table (existing)
- id, name, slug, level, duration, stream
- description, detailed_description, eligibility
- syllabus, career_prospects, average_fees, average_salary
- is_popular, is_published, seo_title, seo_description
```

### Required Enhancements
```sql
-- College-Course Relationships
CREATE TABLE college_courses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  college_id UUID REFERENCES colleges(id) ON DELETE CASCADE,
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  admission_status admission_status_enum DEFAULT 'open', -- 'open', 'closed', 'coming_soon'
  seats_available INTEGER,
  total_seats INTEGER,
  fees_per_year DECIMAL(12,2),
  duration_years DECIMAL(3,1),
  specializations JSONB, -- Array of specialization names
  admission_criteria TEXT,
  entrance_exams JSONB, -- Array of accepted entrance exams
  cutoff_ranks JSONB, -- Previous year cutoffs
  placement_stats JSONB, -- Course-specific placement data
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(college_id, course_id)
);

-- Course Curriculum
CREATE TABLE course_curriculum (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  semester INTEGER NOT NULL,
  year INTEGER NOT NULL,
  subject_name VARCHAR(255) NOT NULL,
  subject_code VARCHAR(50),
  credits INTEGER,
  subject_type VARCHAR(50), -- 'core', 'elective', 'practical', 'project'
  description TEXT,
  prerequisites JSONB, -- Array of prerequisite subject codes
  learning_outcomes TEXT,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Entrance Exams
CREATE TABLE entrance_exams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL UNIQUE,
  full_name VARCHAR(500),
  conducting_body VARCHAR(255),
  exam_type VARCHAR(100), -- 'national', 'state', 'university', 'college'
  exam_mode VARCHAR(50), -- 'online', 'offline', 'both'
  frequency VARCHAR(50), -- 'annual', 'biannual', 'multiple'
  application_fee DECIMAL(10,2),
  exam_duration INTEGER, -- in minutes
  total_marks INTEGER,
  negative_marking BOOLEAN DEFAULT false,
  eligibility_criteria TEXT,
  exam_pattern TEXT,
  syllabus TEXT,
  important_dates JSONB, -- {registration_start, registration_end, exam_date, result_date}
  official_website VARCHAR(500),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Course-Entrance Exam Relationships
CREATE TABLE course_entrance_exams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  entrance_exam_id UUID REFERENCES entrance_exams(id) ON DELETE CASCADE,
  is_mandatory BOOLEAN DEFAULT false,
  weightage DECIMAL(5,2), -- Percentage weightage in admission
  minimum_score DECIMAL(8,2),
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(course_id, entrance_exam_id)
);

-- Course Scholarships
CREATE TABLE course_scholarships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  scholarship_name VARCHAR(255) NOT NULL,
  provider VARCHAR(255), -- Government, College, Private
  scholarship_type VARCHAR(100), -- 'merit', 'need', 'category', 'sports'
  amount DECIMAL(12,2),
  amount_type VARCHAR(50), -- 'fixed', 'percentage', 'full_fee'
  eligibility_criteria TEXT,
  application_process TEXT,
  important_dates JSONB,
  renewal_criteria TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Career Pathways
CREATE TABLE career_pathways (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  career_title VARCHAR(255) NOT NULL,
  job_description TEXT,
  required_skills JSONB, -- Array of skill names
  average_salary_range JSONB, -- {min, max, currency}
  experience_level VARCHAR(50), -- 'entry', 'mid', 'senior'
  growth_prospects TEXT,
  industry_demand VARCHAR(50), -- 'high', 'medium', 'low'
  related_job_titles JSONB, -- Array of similar job titles
  companies_hiring JSONB, -- Array of company names
  further_education JSONB, -- Array of higher education options
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 🔧 API Endpoints

### Public Endpoints
```typescript
// Course Listing & Search
GET /api/courses
  - Query params: page, limit, search, level, stream, duration, fees
  - Response: Paginated course list

GET /api/courses/popular
  - Response: Popular courses for homepage

GET /api/courses/by-stream/[stream]
  - Response: Courses filtered by stream

// Course Details
GET /api/courses/[slug]
  - Response: Complete course information

GET /api/courses/[slug]/colleges
  - Response: Colleges offering this course

GET /api/courses/[slug]/curriculum
  - Response: Course curriculum and syllabus

GET /api/courses/[slug]/entrance-exams
  - Response: Entrance exams for the course

GET /api/courses/[slug]/scholarships
  - Response: Available scholarships

GET /api/courses/[slug]/career-pathways
  - Response: Career options after the course

// Course Comparison
GET /api/courses/compare
  - Query params: course_ids (comma-separated)
  - Response: Comparison data for selected courses

// Course Statistics
GET /api/courses/stats/overview
  - Response: Platform-wide course statistics
```

### Admin Endpoints
```typescript
// Course Management
POST /api/admin/courses
PUT /api/admin/courses/[id]
DELETE /api/admin/courses/[id]

// College-Course Relationships
POST /api/admin/courses/[id]/colleges
PUT /api/admin/courses/[id]/colleges/[collegeId]
DELETE /api/admin/courses/[id]/colleges/[collegeId]

// Curriculum Management
POST /api/admin/courses/[id]/curriculum
PUT /api/admin/courses/[id]/curriculum/[curriculumId]
DELETE /api/admin/courses/[id]/curriculum/[curriculumId]

// Entrance Exam Management
POST /api/admin/entrance-exams
PUT /api/admin/entrance-exams/[id]
DELETE /api/admin/entrance-exams/[id]

// Scholarship Management
POST /api/admin/courses/[id]/scholarships
PUT /api/admin/courses/[id]/scholarships/[scholarshipId]
DELETE /api/admin/courses/[id]/scholarships/[scholarshipId]

// Career Pathway Management
POST /api/admin/courses/[id]/career-pathways
PUT /api/admin/courses/[id]/career-pathways/[pathwayId]
DELETE /api/admin/courses/[id]/career-pathways/[pathwayId]
```

## 🎨 Frontend Components

### Public Components
```typescript
// Course Listing
- CourseListingPage (✅ Implemented)
- CourseCard (✅ Implemented)
- CourseFilters (✅ Implemented)
- CourseSearch (Enhancement needed)

// Course Details
- CourseDetailPage (✅ Basic implementation)
- CourseHero
- CourseOverview
- CourseCurriculum
- CourseColleges
- CourseEntranceExams
- CourseScholarships
- CourseCareerPathways
- CourseComparison

// Specialized Components
- EntranceExamCard
- ScholarshipCard
- CareerPathwayCard
- CurriculumTimeline
```

### Admin Components
```typescript
// Course Management
- CourseForm (✅ Implemented)
- CourseList (✅ Implemented)
- CourseAnalytics

// Relationship Management
- CollegeCourseForm
- CollegeCourseList
- CurriculumBuilder
- CurriculumEditor

// Supporting Systems
- EntranceExamForm
- EntranceExamList
- ScholarshipForm
- ScholarshipList
- CareerPathwayForm
- CareerPathwayList
```

## 🚀 Implementation Timeline

### Phase 1: Enhanced Course Profiles (Week 1-2)
- [ ] Implement college-course relationships
- [ ] Add curriculum management system
- [ ] Create entrance exam integration
- [ ] Enhance course detail pages

### Phase 2: Career & Scholarship Systems (Week 3)
- [ ] Implement career pathway mapping
- [ ] Add scholarship management
- [ ] Create course comparison tools
- [ ] Build recommendation engine

### Phase 3: Advanced Features (Week 4)
- [ ] Advanced search and filtering
- [ ] Course analytics dashboard
- [ ] SEO optimization for courses
- [ ] Performance optimizations

## 📱 Responsive Design Requirements

### Mobile Optimization
- Course cards optimized for touch
- Collapsible curriculum sections
- Swipeable course comparison
- Mobile-friendly filters

### Tablet & Desktop
- Multi-column course listings
- Advanced comparison tables
- Interactive curriculum timeline
- Comprehensive filter sidebar

## 🧪 Testing Strategy

### Unit Tests
- Course CRUD operations
- Relationship management
- Search functionality
- Data validation

### Integration Tests
- Course-college relationships
- Entrance exam integration
- Career pathway mapping
- Scholarship eligibility

## 📋 Dependencies

### Technical Dependencies
- College management system (Phase 1)
- Search engine integration
- Analytics tracking
- CDN for course materials

### Data Dependencies
- Entrance exam data sources
- Scholarship information
- Industry salary data
- Career pathway research

## 📈 Success Metrics

- Course search results < 300ms
- Course detail page load < 2 seconds
- 95%+ API uptime
- Mobile responsiveness score > 90
- SEO optimization score > 85

## 🔄 Future Enhancements

- AI-powered course recommendations
- Virtual course previews
- Real-time admission updates
- Industry partnership integration
- Personalized learning paths

---

**Previous Plan**: [College Management System](./01-college-management-system.md)  
**Next Plan**: [User Management & Authentication](./03-user-management-auth.md)
