# Review & Rating System - Implementation Plan

## 📋 Overview

Comprehensive review and rating system that allows verified users to share their experiences about colleges and courses, helping prospective students make informed decisions.

## 🎯 Objectives

- **Primary**: Authentic user-generated reviews and ratings for colleges and courses
- **Secondary**: Review moderation and quality control system
- **Tertiary**: Analytics and insights from review data

## 📊 Current Status

### ✅ Completed Features
- Basic database structure for reviews
- Admin review moderation capabilities

### 🔄 Partially Implemented
- Review display components
- Rating aggregation system

### 📋 Pending Features
- User review submission system
- Advanced moderation tools
- Review analytics and insights
- Review helpfulness voting
- Verified review system

## 🗄 Database Schema

### Review System Tables
```sql
-- Review Categories
CREATE TYPE review_category AS ENUM (
  'academics', 'infrastructure', 'faculty', 'placements', 
  'campus_life', 'hostel', 'food', 'sports', 'overall'
);

CREATE TYPE review_status AS ENUM ('pending', 'approved', 'rejected', 'flagged');
CREATE TYPE reviewer_type AS ENUM ('student', 'alumni', 'parent', 'faculty');

-- College Reviews
CREATE TABLE college_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  college_id UUID REFERENCES colleges(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  reviewer_type reviewer_type NOT NULL,
  
  -- Overall Rating (1-5 scale)
  overall_rating DECIMAL(2,1) NOT NULL CHECK (overall_rating >= 1 AND overall_rating <= 5),
  
  -- Category-wise Ratings
  academics_rating DECIMAL(2,1) CHECK (academics_rating >= 1 AND academics_rating <= 5),
  infrastructure_rating DECIMAL(2,1) CHECK (infrastructure_rating >= 1 AND infrastructure_rating <= 5),
  faculty_rating DECIMAL(2,1) CHECK (faculty_rating >= 1 AND faculty_rating <= 5),
  placements_rating DECIMAL(2,1) CHECK (placements_rating >= 1 AND placements_rating <= 5),
  campus_life_rating DECIMAL(2,1) CHECK (campus_life_rating >= 1 AND campus_life_rating <= 5),
  
  -- Review Content
  title VARCHAR(255) NOT NULL,
  review_text TEXT NOT NULL,
  pros TEXT,
  cons TEXT,
  
  -- Verification & Metadata
  is_verified BOOLEAN DEFAULT false,
  verification_method VARCHAR(100), -- 'email', 'document', 'manual'
  course_studied VARCHAR(255),
  year_of_study VARCHAR(50),
  graduation_year INTEGER,
  
  -- Moderation
  status review_status DEFAULT 'pending',
  moderated_by UUID REFERENCES admin_users(id),
  moderated_at TIMESTAMP,
  moderation_notes TEXT,
  
  -- Engagement
  helpful_count INTEGER DEFAULT 0,
  not_helpful_count INTEGER DEFAULT 0,
  report_count INTEGER DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(college_id, user_id) -- One review per user per college
);

-- Course Reviews
CREATE TABLE course_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  college_id UUID REFERENCES colleges(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  reviewer_type reviewer_type NOT NULL,
  
  -- Ratings
  overall_rating DECIMAL(2,1) NOT NULL CHECK (overall_rating >= 1 AND overall_rating <= 5),
  curriculum_rating DECIMAL(2,1) CHECK (curriculum_rating >= 1 AND curriculum_rating <= 5),
  faculty_rating DECIMAL(2,1) CHECK (faculty_rating >= 1 AND faculty_rating <= 5),
  practical_exposure_rating DECIMAL(2,1) CHECK (practical_exposure_rating >= 1 AND practical_exposure_rating <= 5),
  placement_rating DECIMAL(2,1) CHECK (placement_rating >= 1 AND placement_rating <= 5),
  
  -- Review Content
  title VARCHAR(255) NOT NULL,
  review_text TEXT NOT NULL,
  pros TEXT,
  cons TEXT,
  
  -- Course Specific
  difficulty_level INTEGER CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
  workload_rating INTEGER CHECK (workload_rating >= 1 AND workload_rating <= 5),
  would_recommend BOOLEAN,
  
  -- Verification
  is_verified BOOLEAN DEFAULT false,
  verification_method VARCHAR(100),
  year_of_study VARCHAR(50),
  graduation_year INTEGER,
  
  -- Moderation
  status review_status DEFAULT 'pending',
  moderated_by UUID REFERENCES admin_users(id),
  moderated_at TIMESTAMP,
  moderation_notes TEXT,
  
  -- Engagement
  helpful_count INTEGER DEFAULT 0,
  not_helpful_count INTEGER DEFAULT 0,
  report_count INTEGER DEFAULT 0,
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(course_id, college_id, user_id)
);

-- Review Helpfulness Votes
CREATE TABLE review_votes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  review_id UUID NOT NULL, -- Can reference either college_reviews or course_reviews
  review_type VARCHAR(20) NOT NULL, -- 'college' or 'course'
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  vote_type VARCHAR(20) NOT NULL, -- 'helpful' or 'not_helpful'
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(review_id, review_type, user_id)
);

-- Review Reports
CREATE TABLE review_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  review_id UUID NOT NULL,
  review_type VARCHAR(20) NOT NULL,
  reported_by UUID REFERENCES users(id) ON DELETE CASCADE,
  report_reason VARCHAR(100) NOT NULL, -- 'spam', 'inappropriate', 'fake', 'offensive'
  report_details TEXT,
  status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'reviewed', 'resolved'
  reviewed_by UUID REFERENCES admin_users(id),
  reviewed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Review Media (Images/Videos)
CREATE TABLE review_media (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  review_id UUID NOT NULL,
  review_type VARCHAR(20) NOT NULL,
  media_type VARCHAR(20) NOT NULL, -- 'image', 'video'
  media_url VARCHAR(500) NOT NULL,
  caption TEXT,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 🔧 API Endpoints

### Public Review Endpoints
```typescript
// College Reviews
GET /api/colleges/[slug]/reviews
  - Query: page, limit, sort, rating_filter
  - Response: Paginated college reviews

GET /api/colleges/[slug]/reviews/stats
  - Response: Review statistics and rating breakdown

// Course Reviews
GET /api/courses/[slug]/reviews
  - Query: page, limit, sort, college_id
  - Response: Paginated course reviews

GET /api/courses/[slug]/reviews/stats
  - Response: Course review statistics

// Individual Review
GET /api/reviews/[id]
  - Response: Detailed review information
```

### User Review Endpoints
```typescript
// Submit Reviews
POST /api/user/reviews/college
  - Headers: Authorization
  - Body: College review data
  - Response: Created review

POST /api/user/reviews/course
  - Headers: Authorization
  - Body: Course review data
  - Response: Created review

// Manage Reviews
GET /api/user/reviews
  - Headers: Authorization
  - Response: User's submitted reviews

PUT /api/user/reviews/[id]
  - Headers: Authorization
  - Body: Updated review data
  - Response: Updated review

DELETE /api/user/reviews/[id]
  - Headers: Authorization
  - Response: Deletion confirmation

// Review Interactions
POST /api/user/reviews/[id]/vote
  - Headers: Authorization
  - Body: {voteType: 'helpful' | 'not_helpful'}
  - Response: Vote confirmation

POST /api/user/reviews/[id]/report
  - Headers: Authorization
  - Body: {reason, details}
  - Response: Report confirmation
```

### Admin Review Endpoints
```typescript
// Review Moderation
GET /api/admin/reviews/pending
  - Headers: Authorization
  - Response: Pending reviews for moderation

PUT /api/admin/reviews/[id]/moderate
  - Headers: Authorization
  - Body: {status, moderationNotes}
  - Response: Moderation result

GET /api/admin/reviews/reported
  - Headers: Authorization
  - Response: Reported reviews

// Review Analytics
GET /api/admin/reviews/analytics
  - Headers: Authorization
  - Response: Review system analytics
```

## 🎨 Frontend Components

### Public Review Components
```typescript
// Review Display
- ReviewList
- ReviewCard
- ReviewStats
- RatingBreakdown
- ReviewFilters
- ReviewSort

// Review Details
- ReviewModal
- ReviewMedia
- ReviewVoting
- ReviewReport
```

### User Review Components
```typescript
// Review Submission
- ReviewForm
- CollegeReviewForm
- CourseReviewForm
- RatingInput
- MediaUpload

// Review Management
- UserReviewsList
- ReviewEditor
- ReviewStatus
```

### Admin Components
```typescript
// Moderation
- ReviewModerationQueue
- ReviewModerationForm
- ReportedReviewsList
- ReviewAnalytics

// Management
- ReviewBulkActions
- ReviewFilters
- ModerationHistory
```

## 🚀 Implementation Timeline

### Phase 1: Core Review System (Week 1-2)
- [ ] Implement review submission forms
- [ ] Create review display components
- [ ] Add basic moderation system
- [ ] Implement rating aggregation

### Phase 2: Enhanced Features (Week 3)
- [ ] Add review voting system
- [ ] Implement review reporting
- [ ] Create review verification system
- [ ] Add media upload for reviews

### Phase 3: Analytics & Optimization (Week 4)
- [ ] Review analytics dashboard
- [ ] Advanced moderation tools
- [ ] Review quality scoring
- [ ] Performance optimizations

## 📱 Responsive Design

### Mobile Review Experience
- Touch-friendly rating inputs
- Optimized review forms
- Swipeable review cards
- Mobile photo upload

### Desktop Features
- Advanced filtering options
- Detailed review analytics
- Bulk moderation tools
- Rich text editor

## 🧪 Testing Strategy

### Functional Testing
- Review submission workflow
- Rating calculation accuracy
- Moderation process
- Voting and reporting systems

### Quality Assurance
- Review content validation
- Spam detection testing
- Performance under load
- Data integrity checks

## 📋 Dependencies

### Technical Dependencies
- User authentication system
- Image/video upload service
- Content moderation tools
- Analytics tracking

### Content Guidelines
- Review quality standards
- Moderation policies
- Community guidelines
- Verification criteria

## 📈 Success Metrics

- Review submission rate > 15%
- Review approval rate > 80%
- Average review helpfulness > 70%
- Review moderation time < 24 hours
- User engagement with reviews > 60%

## 🔄 Future Enhancements

- AI-powered review analysis
- Sentiment analysis
- Review recommendation system
- Video review support
- Integration with social media

---

**Previous Plan**: [User Management & Authentication](./03-user-management-auth.md)  
**Next Plan**: [Search & Discovery Engine](./05-search-discovery-engine.md)
