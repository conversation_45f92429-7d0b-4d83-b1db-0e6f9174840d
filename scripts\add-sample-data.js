const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env' });

const sql = neon(process.env.DATABASE_URL);

async function addSampleData() {
  try {
    console.log('📊 Adding sample data...');

    // Get admin user ID
    const [admin] = await sql`SELECT id FROM admin_users LIMIT 1`;
    if (!admin) {
      console.log('❌ No admin user found. Please create admin first.');
      return;
    }

    console.log('👤 Using admin ID:', admin.id);

    // Add sample courses with comprehensive data
    console.log('📚 Adding sample courses...');

    const courses = [
      {
        name: 'Bachelor of Technology in Computer Science',
        shortName: 'B.Tech CSE',
        slug: 'btech-computer-science',
        level: 'Undergraduate',
        duration: '4 years',
        stream: 'Engineering',
        description: 'Comprehensive computer science and engineering program',
        detailedDescription: 'This program provides a strong foundation in computer science fundamentals including programming, algorithms, data structures, software engineering, and emerging technologies like AI and machine learning.',
        averageFees: 200000,
        averageSalary: 800000,
        isPopular: true,
        isPublished: true,
        seoTitle: 'B.Tech Computer Science Engineering - Best CSE Colleges',
        seoDescription: 'Find the best B.Tech Computer Science Engineering colleges. Get admission details, fees, placements, and career prospects.',
        seoKeywords: 'btech cse, computer science engineering, programming, software development',
        eligibility: JSON.stringify({
          minPercentage: 75,
          qualification: '10+2 with Physics, Chemistry, Mathematics from a recognized board',
          ageLimit: '17-25 years',
          entranceExams: ['JEE Main', 'JEE Advanced', 'BITSAT', 'State CET']
        }),
        syllabus: JSON.stringify({
          coreSubjects: ['Mathematics', 'Physics', 'Programming Fundamentals', 'Data Structures', 'Computer Networks', 'Database Management', 'Software Engineering', 'Operating Systems'],
          electiveSubjects: ['Machine Learning', 'Cybersecurity', 'Mobile App Development', 'Cloud Computing', 'Blockchain Technology'],
          practicalComponents: ['Programming Labs', 'Hardware Labs', 'Project Work', 'Industrial Training'],
          projectWork: 'Final year capstone project involving real-world problem solving using latest technologies'
        }),
        careerProspects: JSON.stringify({
          jobRoles: ['Software Engineer', 'Data Scientist', 'System Administrator', 'Product Manager', 'Technical Consultant', 'Full Stack Developer'],
          industries: ['Information Technology', 'Banking & Finance', 'Healthcare', 'E-commerce', 'Telecommunications', 'Gaming'],
          higherStudyOptions: ['M.Tech in Computer Science', 'MBA', 'MS in Computer Science', 'PhD in Computer Science'],
          skillsDeveloped: ['Programming', 'Problem Solving', 'Analytical Thinking', 'Project Management', 'Team Collaboration', 'Technical Communication']
        })
      },
      {
        name: 'Master of Business Administration',
        shortName: 'MBA',
        slug: 'mba',
        level: 'Postgraduate',
        duration: '2 years',
        stream: 'Management',
        description: 'Advanced business administration and management program',
        detailedDescription: 'A comprehensive MBA program covering all aspects of business management including finance, marketing, operations, human resources, and strategic management.',
        averageFees: 500000,
        averageSalary: 1200000,
        isPopular: true,
        isPublished: true,
        seoTitle: 'MBA - Master of Business Administration - Top MBA Colleges',
        seoDescription: 'Explore top MBA colleges and programs. Get details on specializations, fees, placements, and admission process.',
        seoKeywords: 'mba, business administration, management, finance, marketing',
        eligibility: JSON.stringify({
          minPercentage: 60,
          qualification: 'Bachelor\'s degree in any discipline from a recognized university',
          ageLimit: 'No age limit',
          entranceExams: ['CAT', 'XAT', 'GMAT', 'MAT', 'CMAT']
        }),
        syllabus: JSON.stringify({
          coreSubjects: ['Financial Management', 'Marketing Management', 'Operations Management', 'Human Resource Management', 'Strategic Management', 'Business Analytics', 'Organizational Behavior'],
          electiveSubjects: ['Digital Marketing', 'Investment Banking', 'Supply Chain Management', 'International Business', 'Entrepreneurship'],
          practicalComponents: ['Case Studies', 'Industry Projects', 'Internships', 'Business Simulations'],
          projectWork: 'Summer internship project and final dissertation on contemporary business issues'
        }),
        careerProspects: JSON.stringify({
          jobRoles: ['Business Analyst', 'Product Manager', 'Consultant', 'Investment Banker', 'Marketing Manager', 'Operations Manager'],
          industries: ['Consulting', 'Banking & Finance', 'FMCG', 'Technology', 'Healthcare', 'Manufacturing'],
          higherStudyOptions: ['PhD in Management', 'Executive MBA', 'Specialized Certifications'],
          skillsDeveloped: ['Leadership', 'Strategic Thinking', 'Financial Analysis', 'Communication', 'Negotiation', 'Decision Making']
        })
      },
      {
        name: 'Bachelor of Medicine and Bachelor of Surgery',
        shortName: 'MBBS',
        slug: 'mbbs',
        level: 'Undergraduate',
        duration: '5.5 years',
        stream: 'Medical',
        description: 'Medical degree program for aspiring doctors',
        detailedDescription: 'MBBS is a comprehensive medical program that prepares students to become qualified doctors. The program includes theoretical knowledge, practical training, and clinical experience.',
        averageFees: 800000,
        averageSalary: 1000000,
        isPopular: true,
        isPublished: true,
        seoTitle: 'MBBS - Bachelor of Medicine and Surgery - Medical Colleges',
        seoDescription: 'Find the best MBBS colleges and medical universities. Get admission details, fees, NEET requirements, and career prospects.',
        seoKeywords: 'mbbs, medical college, doctor, medicine, neet, medical education',
        eligibility: JSON.stringify({
          minPercentage: 50,
          qualification: '10+2 with Physics, Chemistry, Biology from a recognized board',
          ageLimit: '17-25 years (30 for reserved categories)',
          entranceExams: ['NEET UG', 'AIIMS MBBS', 'JIPMER MBBS']
        }),
        syllabus: JSON.stringify({
          coreSubjects: ['Anatomy', 'Physiology', 'Biochemistry', 'Pathology', 'Pharmacology', 'Microbiology', 'Forensic Medicine', 'Community Medicine'],
          electiveSubjects: ['Dermatology', 'Psychiatry', 'Radiology', 'Anesthesiology'],
          practicalComponents: ['Clinical Rotations', 'Hospital Training', 'Laboratory Work', 'Internship'],
          projectWork: 'Research project and clinical case studies during internship'
        }),
        careerProspects: JSON.stringify({
          jobRoles: ['General Physician', 'Specialist Doctor', 'Surgeon', 'Medical Officer', 'Researcher', 'Medical Consultant'],
          industries: ['Healthcare', 'Hospitals', 'Research Institutions', 'Pharmaceutical', 'Public Health', 'Medical Education'],
          higherStudyOptions: ['MD/MS Specialization', 'DM/MCh Super Specialization', 'PhD in Medical Sciences'],
          skillsDeveloped: ['Clinical Skills', 'Diagnostic Abilities', 'Patient Care', 'Medical Research', 'Communication', 'Emergency Management']
        })
      }
    ];

    for (const course of courses) {
      await sql`
        INSERT INTO courses (
          name, short_name, slug, level, duration, stream, description,
          detailed_description, average_fees, average_salary, is_popular, is_published,
          seo_title, seo_description, seo_keywords, eligibility, syllabus,
          career_prospects, created_by
        ) VALUES (
          ${course.name}, ${course.shortName}, ${course.slug}, ${course.level},
          ${course.duration}, ${course.stream}, ${course.description},
          ${course.detailedDescription}, ${course.averageFees}, ${course.averageSalary},
          ${course.isPopular}, ${course.isPublished}, ${course.seoTitle},
          ${course.seoDescription}, ${course.seoKeywords}, ${course.eligibility},
          ${course.syllabus}, ${course.careerProspects}, ${admin.id}
        ) ON CONFLICT (slug) DO NOTHING
      `;
    }

    // Add sample colleges
    console.log('🏫 Adding sample colleges...');

    const colleges = [
      {
        name: 'Indian Institute of Technology Delhi',
        shortName: 'IIT Delhi',
        slug: 'iit-delhi',
        description: 'Premier engineering institute in India',
        establishedYear: 1961,
        collegeType: 'Government',
        nirfRanking: 2,
        totalStudents: 8000,
        facultyCount: 500,
        isVerified: true,
        isFeatured: true,
        isPublished: true,
        location: JSON.stringify({
          address: 'Hauz Khas, New Delhi',
          city: 'New Delhi',
          state: 'Delhi',
          country: 'India',
          pincode: '110016'
        }),
        contactInfo: JSON.stringify({
          phone: '+91-11-2659-1000',
          email: '<EMAIL>',
          website: 'https://home.iitd.ac.in'
        })
      },
      {
        name: 'All India Institute of Medical Sciences',
        shortName: 'AIIMS Delhi',
        slug: 'aiims-delhi',
        description: 'Premier medical institute in India',
        establishedYear: 1956,
        collegeType: 'Government',
        nirfRanking: 1,
        totalStudents: 3000,
        facultyCount: 800,
        isVerified: true,
        isFeatured: true,
        isPublished: true,
        location: JSON.stringify({
          address: 'Ansari Nagar, New Delhi',
          city: 'New Delhi',
          state: 'Delhi',
          country: 'India',
          pincode: '110029'
        }),
        contactInfo: JSON.stringify({
          phone: '+91-11-2659-3333',
          email: '<EMAIL>',
          website: 'https://www.aiims.edu'
        })
      }
    ];

    for (const college of colleges) {
      await sql`
        INSERT INTO colleges (
          name, short_name, slug, description, established_year, college_type,
          nirf_ranking, total_students, faculty_count, is_verified, is_featured,
          is_published, location, contact_info, created_by
        ) VALUES (
          ${college.name}, ${college.shortName}, ${college.slug}, ${college.description},
          ${college.establishedYear}, ${college.collegeType}, ${college.nirfRanking},
          ${college.totalStudents}, ${college.facultyCount}, ${college.isVerified},
          ${college.isFeatured}, ${college.isPublished}, ${college.location},
          ${college.contactInfo}, ${admin.id}
        ) ON CONFLICT (slug) DO NOTHING
      `;
    }

    console.log('✅ Sample data added successfully!');
    console.log('🎯 You can now:');
    console.log('1. View colleges at: http://localhost:3000/admin/colleges');
    console.log('2. Add new college at: http://localhost:3000/admin/colleges/new');
    console.log('3. View courses at: http://localhost:3000/admin/courses');

  } catch (error) {
    console.error('❌ Error adding sample data:', error);
    process.exit(1);
  }
}

addSampleData().then(() => {
  console.log('✅ Sample data setup completed');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Sample data setup failed:', error);
  process.exit(1);
});
