const { neon } = require('@neondatabase/serverless');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: '.env' });

const sql = neon(process.env.DATABASE_URL);

async function createSuperAdmin() {
  try {
    console.log('👤 Creating super admin user...');

    // Check if admin already exists
    const existingAdmin = await sql`
      SELECT id FROM admin_users WHERE email = '<EMAIL>'
    `;

    if (existingAdmin.length > 0) {
      console.log('⚠️ Super admin already exists!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: Admin@123456');
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('Admin@123456', 12);

    // Create super admin
    const [admin] = await sql`
      INSERT INTO admin_users (
        email,
        password,
        name,
        role,
        is_active,
        created_at,
        updated_at
      ) VALUES (
        '<EMAIL>',
        ${hashedPassword},
        'Super Admin',
        'super_admin',
        true,
        NOW(),
        NOW()
      ) RETURNING id, email, name, role
    `;

    console.log('✅ Super admin created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: Admin@123456');
    console.log('🆔 Admin ID:', admin.id);
    
    // Create some basic website settings
    console.log('⚙️ Creating basic website settings...');
    
    const settings = [
      {
        key: 'site_name',
        value: JSON.stringify('College Campus'),
        description: 'Website name',
        category: 'general'
      },
      {
        key: 'site_description', 
        value: JSON.stringify('Find the best colleges and courses for your future'),
        description: 'Website description',
        category: 'general'
      },
      {
        key: 'contact_email',
        value: JSON.stringify('<EMAIL>'),
        description: 'Contact email address',
        category: 'contact'
      },
      {
        key: 'contact_phone',
        value: JSON.stringify('******-567-8900'),
        description: 'Contact phone number',
        category: 'contact'
      }
    ];

    for (const setting of settings) {
      await sql`
        INSERT INTO website_settings (key, value, description, category, updated_by, updated_at)
        VALUES (${setting.key}, ${setting.value}, ${setting.description}, ${setting.category}, ${admin.id}, NOW())
        ON CONFLICT (key) DO NOTHING
      `;
    }

    console.log('✅ Basic settings created!');
    console.log('');
    console.log('🎉 Setup completed! You can now:');
    console.log('1. Start the development server: npm run dev');
    console.log('2. Visit: http://localhost:3000/admin/login');
    console.log('3. Login with the credentials above');
    
  } catch (error) {
    console.error('❌ Error creating super admin:', error);
    process.exit(1);
  }
}

createSuperAdmin().then(() => {
  console.log('✅ Super admin setup completed');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Super admin setup failed:', error);
  process.exit(1);
});
