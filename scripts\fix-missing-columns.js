const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env' });

const sql = neon(process.env.DATABASE_URL);

async function fixDatabaseSchema() {
  try {
    console.log('🔧 Fixing database schema issues...');

    // Check if tables exist and create them if needed
    console.log('📋 Checking table existence...');

    // Check if reviews table has student_id column
    try {
      await sql`SELECT student_id FROM reviews LIMIT 1`;
      console.log('✅ Reviews table has student_id column');
    } catch (error) {
      if (error.message.includes('does not exist')) {
        console.log('⚠️ Reviews table missing student_id, adding...');
        await sql`ALTER TABLE reviews ADD COLUMN IF NOT EXISTS student_id UUID REFERENCES student_users(id) ON DELETE CASCADE`;
      }
    }

    // Check if contact_inquiries table has interested_colleges column
    try {
      await sql`SELECT interested_colleges FROM contact_inquiries LIMIT 1`;
      console.log('✅ Contact inquiries table has interested_colleges column');
    } catch (error) {
      if (error.message.includes('does not exist')) {
        console.log('⚠️ Contact inquiries table missing interested_colleges, adding...');
        await sql`ALTER TABLE contact_inquiries ADD COLUMN IF NOT EXISTS interested_colleges JSONB`;
      }
    }

    // Check if colleges table has location column
    try {
      await sql`SELECT location FROM colleges LIMIT 1`;
      console.log('✅ Colleges table has location column');
    } catch (error) {
      if (error.message.includes('does not exist')) {
        console.log('⚠️ Colleges table missing location, adding...');
        await sql`ALTER TABLE colleges ADD COLUMN IF NOT EXISTS location JSONB`;
      }
    }

    // Ensure name column exists in contact_inquiries
    try {
      await sql`SELECT name FROM contact_inquiries LIMIT 1`;
      console.log('✅ Contact inquiries table has name column');
    } catch (error) {
      if (error.message.includes('does not exist')) {
        console.log('⚠️ Contact inquiries table missing name, adding...');
        await sql`ALTER TABLE contact_inquiries ADD COLUMN IF NOT EXISTS name VARCHAR(255) DEFAULT 'Unknown'`;
      }
    }

    // Test all the critical queries
    console.log('🧪 Testing database queries...');

    try {
      const collegesTest = await sql`SELECT id, name, location FROM colleges LIMIT 1`;
      console.log('✅ Colleges table query works');
    } catch (error) {
      console.log('❌ Colleges query failed:', error.message);
    }

    try {
      const reviewsTest = await sql`SELECT id, student_id FROM reviews LIMIT 1`;
      console.log('✅ Reviews table query works');
    } catch (error) {
      console.log('❌ Reviews query failed:', error.message);
    }

    try {
      const leadsTest = await sql`SELECT id, email, name, interested_colleges FROM contact_inquiries LIMIT 1`;
      console.log('✅ Contact inquiries table query works');
    } catch (error) {
      console.log('❌ Contact inquiries query failed:', error.message);
    }

    // Check if all admin tables exist
    const adminTables = [
      'admin_users',
      'admin_activity_logs',
      'student_users',
      'colleges',
      'courses',
      'colleges_courses',
      'reviews',
      'contact_inquiries',
      'scholarship_applications',
      'student_admissions',
      'articles',
      'website_settings'
    ];

    console.log('📊 Checking all required tables...');
    for (const table of adminTables) {
      try {
        await sql`SELECT 1 FROM ${sql(table)} LIMIT 1`;
        console.log(`✅ Table ${table} exists`);
      } catch (error) {
        console.log(`❌ Table ${table} missing or has issues:`, error.message);
      }
    }

    console.log('🎉 Database schema check completed!');

  } catch (error) {
    console.error('❌ Error fixing database schema:', error);
    process.exit(1);
  }
}

fixDatabaseSchema().then(() => {
  console.log('✅ Schema fix completed');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Schema fix failed:', error);
  process.exit(1);
});
