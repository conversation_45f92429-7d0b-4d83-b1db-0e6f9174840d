const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env' });

const sql = neon(process.env.DATABASE_URL);

async function resetDatabase() {
  try {
    console.log('🗑️ Resetting database...');
    
    // Drop all tables to start fresh
    console.log('Dropping all existing tables...');
    
    const dropTables = [
      'admin_activity_logs',
      'admin_users', 
      'admin_roles',
      'student_admissions',
      'scholarship_applications',
      'contact_inquiries',
      'reviews',
      'colleges_courses',
      'articles',
      'website_settings',
      'student_users',
      'courses',
      'colleges',
      // Drop any other existing tables
      'college_gallery',
      'article_likes',
      'article_views', 
      'article_categories',
      'article_tag_mapping',
      'newsletter_subscriptions',
      'college_news',
      'article_tags',
      'contact_inquiry_notes',
      'answers',
      'questions',
      'course_category_mapping',
      'college_ratings',
      'email_verification_tokens',
      'college_courses',
      'course_categories',
      'password_reset_tokens',
      'article_comments',
      'users',
      'review_helpfulness',
      'user_sessions',
      'scholarships',
      'scholarship_testimonials'
    ];

    for (const table of dropTables) {
      try {
        await sql`DROP TABLE IF EXISTS ${sql(table)} CASCADE`;
        console.log(`✅ Dropped table: ${table}`);
      } catch (error) {
        console.log(`⚠️ Table ${table} doesn't exist or already dropped`);
      }
    }

    // Drop any remaining types
    const dropTypes = [
      'accreditation',
      'admission_status', 
      'application_status',
      'article_status',
      'article_type',
      'college_status',
      'college_type',
      'contact_status',
      'course_level',
      'course_mode',
      'course_status',
      'inquiry_type',
      'lead_status',
      'review_status',
      'scholarship_status',
      'scholarship_type',
      'stream',
      'student_status',
      'user_role',
      'user_status'
    ];

    for (const type of dropTypes) {
      try {
        await sql`DROP TYPE IF EXISTS ${sql(type)} CASCADE`;
        console.log(`✅ Dropped type: ${type}`);
      } catch (error) {
        console.log(`⚠️ Type ${type} doesn't exist or already dropped`);
      }
    }

    console.log('🎉 Database reset completed!');
    console.log('📝 Now run: npx drizzle-kit push');
    console.log('👤 Then run: node scripts/create-super-admin.js');
    
  } catch (error) {
    console.error('❌ Error resetting database:', error);
    process.exit(1);
  }
}

resetDatabase().then(() => {
  console.log('✅ Database reset completed successfully');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Database reset failed:', error);
  process.exit(1);
});
