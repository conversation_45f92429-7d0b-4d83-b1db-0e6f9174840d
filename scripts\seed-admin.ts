import { config } from 'dotenv';
config({ path: '.env' });

import bcrypt from 'bcryptjs';
import { db } from '../lib/db';
import { adminUsers } from '../lib/db/schema';
import { eq } from 'drizzle-orm';

async function seedSuperAdmin() {
  try {
    console.log('🌱 Seeding super admin...');

    const email = '<EMAIL>';
    const password = 'Admin@123456'; // Change this in production
    const name = 'Super Admin';

    // Check if super admin already exists
    const [existingAdmin] = await db
      .select()
      .from(adminUsers)
      .where(eq(adminUsers.email, email))
      .limit(1);

    if (existingAdmin) {
      console.log('✅ Super admin already exists');
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create super admin
    const [newAdmin] = await db
      .insert(adminUsers)
      .values({
        name,
        email,
        password: hashedPassword,
        role: 'super_admin',
        isActive: true,
      })
      .returning({
        id: adminUsers.id,
        name: adminUsers.name,
        email: adminUsers.email,
        role: adminUsers.role,
      });

    console.log('✅ Super admin created successfully:');
    console.log(`   Email: ${newAdmin.email}`);
    console.log(`   Password: ${password}`);
    console.log(`   Role: ${newAdmin.role}`);
    console.log('');
    console.log('⚠️  IMPORTANT: Change the default password after first login!');

  } catch (error) {
    console.error('❌ Error seeding super admin:', error);
    process.exit(1);
  }
}

// Run the seed function
seedSuperAdmin()
  .then(() => {
    console.log('🎉 Seeding completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  });
