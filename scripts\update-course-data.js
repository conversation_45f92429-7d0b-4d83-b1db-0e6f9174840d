const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env' });

const sql = neon(process.env.DATABASE_URL);

async function updateCourseData() {
  try {
    console.log('📚 Updating course data with detailed information...');

    // Update B.Tech CSE
    await sql`
      UPDATE courses 
      SET 
        detailed_description = 'This comprehensive program provides a strong foundation in computer science principles, software development, algorithms, data structures, and modern computing technologies. Students learn programming languages, database systems, computer networks, artificial intelligence, and software engineering practices.',
        eligibility = ${JSON.stringify({
          minPercentage: 75,
          qualification: '12th with Physics, Chemistry, Mathematics',
          ageLimit: '25 years',
          entranceExams: ['JEE Main', 'JEE Advanced', 'State CET']
        })},
        syllabus = ${JSON.stringify({
          coreSubjects: ['Programming Fundamentals', 'Data Structures', 'Algorithms', 'Computer Networks', 'Database Systems', 'Operating Systems', 'Software Engineering'],
          electiveSubjects: ['Artificial Intelligence', 'Machine Learning', 'Cybersecurity', 'Mobile App Development', 'Cloud Computing'],
          practicalComponents: ['Programming Labs', 'Project Work', 'Industrial Training'],
          projectWork: 'Final year project and internship'
        })},
        career_prospects = ${JSON.stringify({
          jobRoles: ['Software Developer', 'System Analyst', 'Data Scientist', 'DevOps Engineer', 'Product Manager'],
          industries: ['Information Technology', 'Banking & Finance', 'E-commerce', 'Healthcare', 'Gaming'],
          higherStudyOptions: ['M.Tech', 'MS', 'MBA', 'PhD'],
          skillsDeveloped: ['Programming', 'Problem Solving', 'System Design', 'Project Management']
        })},
        seo_title = 'B.Tech Computer Science Engineering - Course Details',
        seo_description = 'Complete information about Bachelor of Technology in Computer Science Engineering including syllabus, eligibility, career prospects and top colleges.',
        seo_keywords = 'B.Tech CSE, Computer Science Engineering, Programming, Software Development'
      WHERE slug = 'btech-computer-science'
    `;

    // Update MBA
    await sql`
      UPDATE courses 
      SET 
        detailed_description = 'MBA program focuses on developing managerial and leadership skills through comprehensive study of business functions including finance, marketing, operations, human resources, and strategy. The program includes case studies, group projects, and industry interactions.',
        eligibility = ${JSON.stringify({
          minPercentage: 50,
          qualification: 'Bachelor\'s degree in any discipline',
          ageLimit: '30 years',
          entranceExams: ['CAT', 'XAT', 'GMAT', 'MAT', 'CMAT']
        })},
        syllabus = ${JSON.stringify({
          coreSubjects: ['Financial Management', 'Marketing Management', 'Operations Management', 'Human Resource Management', 'Strategic Management', 'Business Analytics'],
          electiveSubjects: ['Digital Marketing', 'International Business', 'Entrepreneurship', 'Supply Chain Management', 'Investment Banking'],
          practicalComponents: ['Case Studies', 'Business Simulation', 'Industry Projects', 'Summer Internship'],
          projectWork: 'Dissertation and live projects'
        })},
        career_prospects = ${JSON.stringify({
          jobRoles: ['Business Analyst', 'Product Manager', 'Consultant', 'Investment Banker', 'Marketing Manager'],
          industries: ['Consulting', 'Banking & Finance', 'FMCG', 'Technology', 'Healthcare'],
          higherStudyOptions: ['PhD in Management', 'Executive MBA', 'Specialized Certifications'],
          skillsDeveloped: ['Leadership', 'Strategic Thinking', 'Communication', 'Team Management']
        })},
        seo_title = 'MBA - Master of Business Administration Course Details',
        seo_description = 'Complete information about MBA program including specializations, eligibility, career prospects and top business schools.',
        seo_keywords = 'MBA, Master of Business Administration, Management, Leadership, Business'
      WHERE slug = 'mba'
    `;

    // Update MBBS
    await sql`
      UPDATE courses 
      SET 
        detailed_description = 'MBBS is a comprehensive medical program that prepares students to become qualified doctors. The curriculum includes pre-clinical subjects, clinical subjects, and practical training in hospitals. Students learn anatomy, physiology, pathology, pharmacology, and clinical medicine.',
        eligibility = ${JSON.stringify({
          minPercentage: 50,
          qualification: '12th with Physics, Chemistry, Biology',
          ageLimit: '25 years (30 for reserved categories)',
          entranceExams: ['NEET UG']
        })},
        syllabus = ${JSON.stringify({
          coreSubjects: ['Anatomy', 'Physiology', 'Biochemistry', 'Pathology', 'Pharmacology', 'Microbiology', 'Forensic Medicine', 'Community Medicine'],
          electiveSubjects: ['Dermatology', 'Psychiatry', 'Radiology', 'Anesthesiology'],
          practicalComponents: ['Clinical Rotations', 'Hospital Training', 'Internship'],
          projectWork: 'Research project and thesis'
        })},
        career_prospects = ${JSON.stringify({
          jobRoles: ['General Physician', 'Specialist Doctor', 'Surgeon', 'Medical Officer', 'Researcher'],
          industries: ['Healthcare', 'Hospitals', 'Government Health Services', 'Medical Research', 'Pharmaceuticals'],
          higherStudyOptions: ['MD', 'MS', 'Diploma courses', 'Fellowship programs'],
          skillsDeveloped: ['Clinical Skills', 'Patient Care', 'Medical Knowledge', 'Emergency Management']
        })},
        seo_title = 'MBBS - Bachelor of Medicine and Bachelor of Surgery',
        seo_description = 'Complete information about MBBS program including syllabus, eligibility, NEET requirements and career prospects in medicine.',
        seo_keywords = 'MBBS, Medicine, Doctor, Medical College, NEET, Healthcare'
      WHERE slug = 'mbbs'
    `;

    console.log('✅ Course data updated successfully!');
    
    // Verify updates
    const courses = await sql`
      SELECT name, slug, eligibility, syllabus, career_prospects 
      FROM courses 
      WHERE slug IN ('btech-computer-science', 'mba', 'mbbs')
    `;
    
    console.log('\n📚 Updated courses:');
    courses.forEach(course => {
      console.log(`- ${course.name}: ${course.eligibility ? 'Has eligibility data' : 'Missing eligibility'}`);
    });
    
  } catch (error) {
    console.error('❌ Error updating course data:', error);
    process.exit(1);
  }
}

updateCourseData().then(() => {
  console.log('✅ Course data update completed');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Course data update failed:', error);
  process.exit(1);
});
